{"version": 3, "file": "indexList.js", "sources": ["uni_modules/uview-plus/components/u-index-list/indexList.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:13:35\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/indexList.js\n */\nexport default {\n    // indexList 组件\n    indexList: {\n        inactiveColor: '#606266',\n        activeColor: '#5677fc',\n        indexList: [],\n        sticky: true,\n        customNavHeight: 0,\n        safeBottomFix: false\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,YAAA;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,eAAe;AAAA,IACf,aAAa;AAAA,IACb,WAAW,CAAE;AAAA,IACb,QAAQ;AAAA,IACR,iBAAiB;AAAA,IACjB,eAAe;AAAA,EAClB;AACL;;"}