"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const src_stores_user = require("../../src/stores/user.js");
const src_stores_app = require("../../src/stores/app.js");
const src_api_index = require("../../src/api/index.js");
const src_constants_index = require("../../src/constants/index.js");
const src_utils_index = require("../../src/utils/index.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_tabs2 = common_vendor.resolveComponent("u-tabs");
  (_easycom_u_icon2 + _easycom_u_tabs2)();
}
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_tabs = () => "../../uni_modules/uview-plus/components/u-tabs/u-tabs.js";
if (!Math) {
  (_easycom_u_icon + UserStatusBanner + _easycom_u_tabs + EmptyState + StatusTag + LoadingSpinner)();
}
const UserStatusBanner = () => "../../src/components/common/UserStatusBanner.js";
const EmptyState = () => "../../src/components/common/EmptyState.js";
const LoadingSpinner = () => "../../src/components/common/LoadingSpinner.js";
const StatusTag = () => "../../src/components/common/StatusTag.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "info",
  setup(__props) {
    src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const statusBarHeight = common_vendor.ref(0);
    const currentTab = common_vendor.ref(0);
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const currentPage = common_vendor.ref(1);
    const bannerList = common_vendor.ref([]);
    const allList = common_vendor.ref([]);
    const tabList = [
      { name: "全部", key: "all" },
      { name: "公告", key: "notice" },
      { name: "政策法规", key: "policy" },
      { name: "重要通知", key: "news" }
    ];
    const currentList = common_vendor.computed(() => {
      const currentTabKey = tabList[currentTab.value].key;
      if (currentTabKey === "all") {
        return allList.value;
      }
      return allList.value.filter((item) => item.type === currentTabKey);
    });
    common_vendor.onMounted(() => {
      try {
        if (common_vendor.index.canIUse("getWindowInfo")) {
          const windowInfo = common_vendor.index.getWindowInfo();
          statusBarHeight.value = windowInfo.statusBarHeight || 0;
        } else {
          const systemInfo = common_vendor.index.getSystemInfoSync();
          statusBarHeight.value = systemInfo.statusBarHeight || 0;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/info/info.vue:185", "获取系统信息失败:", error);
        statusBarHeight.value = 0;
      }
      loadData();
      loadBanners();
    });
    const onTabChange = (index) => {
      currentTab.value = index;
      currentPage.value = 1;
      hasMore.value = true;
      allList.value = [];
      loadData();
    };
    const loadBanners = () => __async(this, null, function* () {
      try {
        const response = yield src_api_index.api.info.getAnnouncements({
          page: 1,
          pageSize: 3,
          type: "notice"
        });
        bannerList.value = response.data.list.filter((item) => item.isImportant);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/info/info.vue:214", "加载轮播数据失败:", error);
      }
    });
    const loadData = () => __async(this, null, function* () {
      if (isLoading.value)
        return;
      isLoading.value = true;
      try {
        const params = {
          page: currentPage.value,
          pageSize: 10
        };
        const currentTabKey = tabList[currentTab.value].key;
        if (currentTabKey !== "all") {
          params.type = currentTabKey;
        }
        const response = yield src_api_index.api.info.getAnnouncements(params);
        if (currentPage.value === 1) {
          allList.value = response.data.list;
        } else {
          allList.value.push(...response.data.list);
        }
        hasMore.value = response.data.page < response.data.totalPages;
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/info/info.vue:245", "加载数据失败:", error);
        appStore.showToast(error.message || "加载失败，请重试");
      } finally {
        isLoading.value = false;
        isRefreshing.value = false;
      }
    });
    const onRefresh = () => {
      isRefreshing.value = true;
      currentPage.value = 1;
      hasMore.value = true;
      allList.value = [];
      loadData();
      loadBanners();
    };
    const onLoadMore = () => {
      if (hasMore.value && !isLoading.value) {
        currentPage.value++;
        loadData();
      }
    };
    const viewDetail = (item) => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.INFO_DETAIL, { id: item.id });
    };
    const handleSearch = () => {
      appStore.showToast("搜索功能开发中");
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + "px",
        b: common_vendor.o(handleSearch),
        c: common_vendor.p({
          name: "search",
          color: "#fff",
          size: "44"
        }),
        d: common_vendor.p({
          showAction: true
        }),
        e: bannerList.value.length > 0
      }, bannerList.value.length > 0 ? {
        f: common_vendor.f(bannerList.value, (banner, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(banner.title),
            b: common_vendor.t(banner.content.substring(0, 50)),
            c: banner.isImportant
          }, banner.isImportant ? {} : {}, {
            d: common_vendor.o(($event) => viewDetail(banner), banner.id),
            e: banner.id
          });
        }),
        g: bannerList.value.length > 1
      } : {}, {
        h: common_vendor.o(onTabChange),
        i: common_vendor.o(($event) => currentTab.value = $event),
        j: common_vendor.p({
          list: tabList,
          activeColor: "#2E8B57",
          inactiveColor: "#666",
          lineColor: "#2E8B57",
          lineWidth: 60,
          lineHeight: 6,
          modelValue: currentTab.value
        }),
        k: currentList.value.length === 0 && !isLoading.value
      }, currentList.value.length === 0 && !isLoading.value ? {
        l: common_vendor.o(onRefresh),
        m: common_vendor.p({
          type: "no-data",
          title: "暂无内容",
          description: "当前分类下暂时没有内容",
          showButton: true,
          buttonText: "刷新"
        })
      } : {
        n: common_vendor.f(currentList.value, (item, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(item.title),
            b: item.isImportant
          }, item.isImportant ? {} : {}, {
            c: item.isTop
          }, item.isTop ? {} : {}, {
            d: item.content
          }, item.content ? {
            e: common_vendor.t(item.content.substring(0, 100))
          } : {}, {
            f: common_vendor.t(common_vendor.unref(src_utils_index.formatRelativeTime)(item.publishTime)),
            g: item.source
          }, item.source ? {
            h: common_vendor.t(item.source)
          } : {}, {
            i: "f52d2d81-4-" + i0,
            j: common_vendor.p({
              type: "announcement",
              status: item.type
            }),
            k: item.id,
            l: common_vendor.o(($event) => viewDetail(item), item.id)
          });
        })
      }, {
        o: isLoading.value
      }, isLoading.value ? {
        p: common_vendor.p({
          size: "small",
          text: "加载中..."
        })
      } : {}, {
        q: !hasMore.value && currentList.value.length > 0
      }, !hasMore.value && currentList.value.length > 0 ? {} : {}, {
        r: isRefreshing.value,
        s: common_vendor.o(onRefresh),
        t: common_vendor.o(onLoadMore)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f52d2d81"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/info/info.js.map
