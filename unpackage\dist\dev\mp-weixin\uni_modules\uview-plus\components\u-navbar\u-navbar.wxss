/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-f631659b,
.u-empty__wrap.data-v-f631659b,
.u-tabs.data-v-f631659b,
.u-tabs__wrapper.data-v-f631659b,
.u-tabs__wrapper__scroll-view-wrapper.data-v-f631659b,
.u-tabs__wrapper__scroll-view.data-v-f631659b,
.u-tabs__wrapper__nav.data-v-f631659b,
.u-tabs__wrapper__nav__line.data-v-f631659b,
.up-empty.data-v-f631659b,
.up-empty__wrap.data-v-f631659b,
.up-tabs.data-v-f631659b,
.up-tabs__wrapper.data-v-f631659b,
.up-tabs__wrapper__scroll-view-wrapper.data-v-f631659b,
.up-tabs__wrapper__scroll-view.data-v-f631659b,
.up-tabs__wrapper__nav.data-v-f631659b,
.up-tabs__wrapper__nav__line.data-v-f631659b {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-navbar--fixed.data-v-f631659b {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  z-index: 11;
}
.u-navbar__content.data-v-f631659b {
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 44px;
  background-color: #9acafc;
  position: relative;
  justify-content: center;
}
.u-navbar__content__left.data-v-f631659b, .u-navbar__content__right.data-v-f631659b {
  padding: 0 13px;
  position: absolute;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-navbar__content__left.data-v-f631659b {
  left: 0;
}
.u-navbar__content__left--hover.data-v-f631659b {
  opacity: 0.7;
}
.u-navbar__content__left__text.data-v-f631659b {
  font-size: 15px;
  margin-left: 3px;
}
.u-navbar__content__title.data-v-f631659b {
  text-align: center;
  font-size: 16px;
  color: #303133;
}
.u-navbar__content__right.data-v-f631659b {
  right: 0;
}
.u-navbar__content__right__text.data-v-f631659b {
  font-size: 15px;
  margin-left: 3px;
}