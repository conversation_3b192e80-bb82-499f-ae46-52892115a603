/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-f667648f,
.u-empty__wrap.data-v-f667648f,
.u-tabs.data-v-f667648f,
.u-tabs__wrapper.data-v-f667648f,
.u-tabs__wrapper__scroll-view-wrapper.data-v-f667648f,
.u-tabs__wrapper__scroll-view.data-v-f667648f,
.u-tabs__wrapper__nav.data-v-f667648f,
.u-tabs__wrapper__nav__line.data-v-f667648f,
.up-empty.data-v-f667648f,
.up-empty__wrap.data-v-f667648f,
.up-tabs.data-v-f667648f,
.up-tabs__wrapper.data-v-f667648f,
.up-tabs__wrapper__scroll-view-wrapper.data-v-f667648f,
.up-tabs__wrapper__scroll-view.data-v-f667648f,
.up-tabs__wrapper__nav.data-v-f667648f,
.up-tabs__wrapper__nav__line.data-v-f667648f {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-modal.data-v-f667648f {
  width: 650rpx;
  border-radius: 6px;
  overflow: hidden;
}
.u-modal__title.data-v-f667648f {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
  color: #606266;
  text-align: center;
  padding-top: 25px;
}
.u-modal__content.data-v-f667648f {
  padding: 12px 25px 25px 25px;
  display: flex;
  flex-direction: row;
  justify-content: center;
}
.u-modal__content__text.data-v-f667648f {
  font-size: 15px;
  color: #606266;
  flex: 1;
}
.u-modal__button-group.data-v-f667648f {
  display: flex;
  flex-direction: row;
}
.u-modal__button-group--confirm-button.data-v-f667648f {
  flex-direction: column;
  padding: 0px 25px 15px 25px;
}
.u-modal__button-group__wrapper.data-v-f667648f {
  flex: 1;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  height: 48px;
}
.u-modal__button-group__wrapper--confirm.data-v-f667648f, .u-modal__button-group__wrapper--only-cancel.data-v-f667648f {
  border-bottom-right-radius: 6px;
}
.u-modal__button-group__wrapper--cancel.data-v-f667648f, .u-modal__button-group__wrapper--only-confirm.data-v-f667648f {
  border-bottom-left-radius: 6px;
}
.u-modal__button-group__wrapper--hover.data-v-f667648f {
  background-color: #f3f4f6;
}
.u-modal__button-group__wrapper__text.data-v-f667648f {
  color: #606266;
  font-size: 16px;
  text-align: center;
}