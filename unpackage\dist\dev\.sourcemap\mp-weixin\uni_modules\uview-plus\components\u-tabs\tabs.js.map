{"version": 3, "file": "tabs.js", "sources": ["uni_modules/uview-plus/components/u-tabs/tabs.js"], "sourcesContent": ["/*\r\n * <AUTHOR> LQ\r\n * @Description  :\r\n * @version      : 1.0\r\n * @Date         : 2021-08-20 16:44:21\r\n * @LastAuthor   : LQ\r\n * @lastTime     : 2021-08-20 17:23:14\r\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/tabs.js\r\n */\r\nexport default {\r\n    //\r\n    tabs: {\r\n        duration: 300,\r\n        list: [],\r\n        lineColor: '#3c9cff',\r\n        activeStyle: {\r\n            color: '#303133'\r\n        },\r\n        inactiveStyle: {\r\n            color: '#606266'\r\n        },\r\n        lineWidth: 20,\r\n        lineHeight: 3,\r\n        lineBgSize: 'cover',\r\n        itemStyle: {\r\n            height: '44px'\r\n        },\r\n        scrollable: true,\r\n\t\tcurrent: 0,\r\n\t\tkeyName: 'name',\r\n        iconStyle: {}\r\n    }\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,OAAA;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,UAAU;AAAA,IACV,MAAM,CAAE;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,MACT,OAAO;AAAA,IACV;AAAA,IACD,eAAe;AAAA,MACX,OAAO;AAAA,IACV;AAAA,IACD,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,MACP,QAAQ;AAAA,IACX;AAAA,IACD,YAAY;AAAA,IAClB,SAAS;AAAA,IACT,SAAS;AAAA,IACH,WAAW,CAAE;AAAA,EAChB;AACL;;"}