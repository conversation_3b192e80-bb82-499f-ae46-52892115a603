<view class="profile-submit-container data-v-dd383ca2"><u-navbar wx:if="{{a}}" class="data-v-dd383ca2" u-i="dd383ca2-0" bind:__l="__l" u-p="{{a}}"/><view class="main-content data-v-dd383ca2"><view class="progress-section data-v-dd383ca2"><view class="progress-item active data-v-dd383ca2"><view class="progress-icon data-v-dd383ca2"><u-icon wx:if="{{b}}" class="data-v-dd383ca2" u-i="dd383ca2-1" bind:__l="__l" u-p="{{b}}"/></view><text class="progress-text data-v-dd383ca2">微信登录</text></view><view class="progress-line data-v-dd383ca2"></view><view class="progress-item current data-v-dd383ca2"><view class="progress-icon data-v-dd383ca2">2</view><text class="progress-text data-v-dd383ca2">完善资料</text></view><view class="progress-line data-v-dd383ca2"></view><view class="progress-item data-v-dd383ca2"><view class="progress-icon data-v-dd383ca2">3</view><text class="progress-text data-v-dd383ca2">等待审核</text></view></view><view class="form-container data-v-dd383ca2"><u-form wx:if="{{K}}" class="r data-v-dd383ca2" u-s="{{['d']}}" u-r="formRef" u-i="dd383ca2-2" bind:__l="__l" u-p="{{K}}"><view class="form-section data-v-dd383ca2"><view class="section-title data-v-dd383ca2"><u-icon wx:if="{{c}}" class="data-v-dd383ca2" u-i="dd383ca2-3,dd383ca2-2" bind:__l="__l" u-p="{{c}}"/><text class="data-v-dd383ca2">基本信息</text></view><u-form-item wx:if="{{f}}" class="data-v-dd383ca2" u-s="{{['d']}}" u-i="dd383ca2-4,dd383ca2-2" bind:__l="__l" u-p="{{f}}"><u-input wx:if="{{e}}" class="data-v-dd383ca2" u-i="dd383ca2-5,dd383ca2-4" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"/></u-form-item><u-form-item wx:if="{{i}}" class="data-v-dd383ca2" u-s="{{['d']}}" u-i="dd383ca2-6,dd383ca2-2" bind:__l="__l" u-p="{{i}}"><u-input wx:if="{{h}}" class="data-v-dd383ca2" u-i="dd383ca2-7,dd383ca2-6" bind:__l="__l" bindupdateModelValue="{{g}}" u-p="{{h}}"/></u-form-item><u-form-item wx:if="{{l}}" class="data-v-dd383ca2" u-s="{{['d']}}" u-i="dd383ca2-8,dd383ca2-2" bind:__l="__l" u-p="{{l}}"><u-input wx:if="{{k}}" class="data-v-dd383ca2" u-i="dd383ca2-9,dd383ca2-8" bind:__l="__l" bindupdateModelValue="{{j}}" u-p="{{k}}"/></u-form-item></view><view class="form-section data-v-dd383ca2"><view class="section-title data-v-dd383ca2"><u-icon wx:if="{{m}}" class="data-v-dd383ca2" u-i="dd383ca2-10,dd383ca2-2" bind:__l="__l" u-p="{{m}}"/><text class="data-v-dd383ca2">工作信息</text></view><u-form-item wx:if="{{r}}" class="data-v-dd383ca2" u-s="{{['d']}}" u-i="dd383ca2-11,dd383ca2-2" bind:__l="__l" u-p="{{r}}"><u-input wx:if="{{q}}" class="data-v-dd383ca2" u-s="{{['suffix']}}" bindclick="{{o}}" u-i="dd383ca2-12,dd383ca2-11" bind:__l="__l" bindupdateModelValue="{{p}}" u-p="{{q}}"><u-icon class="data-v-dd383ca2" u-i="dd383ca2-13,dd383ca2-12" bind:__l="__l" u-p="{{n}}" slot="suffix"/></u-input></u-form-item><u-form-item wx:if="{{x}}" class="data-v-dd383ca2" u-s="{{['d']}}" u-i="dd383ca2-14,dd383ca2-2" bind:__l="__l" u-p="{{x}}"><u-input wx:if="{{w}}" class="data-v-dd383ca2" u-s="{{['suffix']}}" bindclick="{{t}}" u-i="dd383ca2-15,dd383ca2-14" bind:__l="__l" bindupdateModelValue="{{v}}" u-p="{{w}}"><u-icon class="data-v-dd383ca2" u-i="dd383ca2-16,dd383ca2-15" bind:__l="__l" u-p="{{s}}" slot="suffix"/></u-input></u-form-item></view><view class="form-section data-v-dd383ca2"><view class="section-title data-v-dd383ca2"><u-icon wx:if="{{y}}" class="data-v-dd383ca2" u-i="dd383ca2-17,dd383ca2-2" bind:__l="__l" u-p="{{y}}"/><text class="data-v-dd383ca2">身份验证</text></view><view class="photo-upload-section data-v-dd383ca2"><view class="upload-tips data-v-dd383ca2"><u-icon wx:if="{{z}}" class="data-v-dd383ca2" u-i="dd383ca2-18,dd383ca2-2" bind:__l="__l" u-p="{{z}}"/><text class="tips-text data-v-dd383ca2">请上传本人近期正面免冠证件照或清晰的生活照，确保五官清晰可见，用于线上考试人脸识别验证</text></view><view class="upload-area data-v-dd383ca2" bindtap="{{I}}"><view wx:if="{{A}}" class="upload-placeholder data-v-dd383ca2"><u-icon wx:if="{{B}}" class="data-v-dd383ca2" u-i="dd383ca2-19,dd383ca2-2" bind:__l="__l" u-p="{{B}}"/><text class="upload-text data-v-dd383ca2">点击上传本人照片</text><text class="upload-size data-v-dd383ca2">支持JPG、PNG格式，不超过200KB</text></view><image wx:else src="{{C}}" class="uploaded-image data-v-dd383ca2" mode="aspectFill"/><view wx:if="{{D}}" class="image-actions data-v-dd383ca2"><view class="action-btn data-v-dd383ca2" catchtap="{{F}}"><u-icon wx:if="{{E}}" class="data-v-dd383ca2" u-i="dd383ca2-20,dd383ca2-2" bind:__l="__l" u-p="{{E}}"/></view><view class="action-btn data-v-dd383ca2" catchtap="{{H}}"><u-icon wx:if="{{G}}" class="data-v-dd383ca2" u-i="dd383ca2-21,dd383ca2-2" bind:__l="__l" u-p="{{G}}"/></view></view></view></view></view></u-form><view class="action-buttons data-v-dd383ca2"><u-button wx:if="{{M}}" u-s="{{['d']}}" class="skip-btn data-v-dd383ca2" bindclick="{{L}}" u-i="dd383ca2-22" bind:__l="__l" u-p="{{M}}"> 跳过，先去学习 </u-button><u-button wx:if="{{O}}" u-s="{{['d']}}" class="submit-btn data-v-dd383ca2" bindclick="{{N}}" u-i="dd383ca2-23" bind:__l="__l" u-p="{{O}}"> 提交审核 </u-button></view></view></view><u-picker wx:if="{{S}}" class="data-v-dd383ca2" bindconfirm="{{P}}" bindcancel="{{Q}}" u-i="dd383ca2-24" bind:__l="__l" bindupdateModelValue="{{R}}" u-p="{{S}}"/><u-picker wx:if="{{W}}" class="data-v-dd383ca2" bindconfirm="{{T}}" bindcancel="{{U}}" u-i="dd383ca2-25" bind:__l="__l" bindupdateModelValue="{{V}}" u-p="{{W}}"/></view>