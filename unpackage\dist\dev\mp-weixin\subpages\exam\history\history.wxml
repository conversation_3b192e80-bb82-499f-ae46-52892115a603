<view class="exam-history-container data-v-31bd7746"><u-navbar wx:if="{{a}}" class="data-v-31bd7746" u-i="31bd7746-0" bind:__l="__l" u-p="{{a}}"/><view class="stats-section data-v-31bd7746"><view class="stats-card data-v-31bd7746"><view class="stat-item data-v-31bd7746"><text class="stat-number data-v-31bd7746">{{b}}</text><text class="stat-label data-v-31bd7746">总考试</text></view><view class="stat-divider data-v-31bd7746"></view><view class="stat-item data-v-31bd7746"><text class="stat-number data-v-31bd7746">{{c}}</text><text class="stat-label data-v-31bd7746">已通过</text></view><view class="stat-divider data-v-31bd7746"></view><view class="stat-item data-v-31bd7746"><text class="stat-number data-v-31bd7746">{{d}}</text><text class="stat-label data-v-31bd7746">平均分</text></view></view></view><view class="filter-section data-v-31bd7746"><view class="filter-tabs data-v-31bd7746"><view wx:for="{{e}}" wx:for-item="filter" wx:key="b" class="{{['filter-tab', 'data-v-31bd7746', filter.c && 'active']}}" bindtap="{{filter.d}}"><text class="data-v-31bd7746">{{filter.a}}</text></view></view></view><loading-spinner wx:if="{{f}}" class="data-v-31bd7746" u-i="31bd7746-1" bind:__l="__l" u-p="{{g}}"/><empty-state wx:elif="{{h}}" class="data-v-31bd7746" u-i="31bd7746-2" bind:__l="__l" u-p="{{i}}"/><view wx:else class="exam-list data-v-31bd7746"><view wx:for="{{j}}" wx:for-item="exam" wx:key="v" class="exam-item data-v-31bd7746" bindtap="{{exam.w}}"><view class="exam-header data-v-31bd7746"><view class="exam-info data-v-31bd7746"><text class="exam-name data-v-31bd7746">{{exam.a}}</text><text class="exam-time data-v-31bd7746">{{exam.b}}</text></view><view class="exam-type data-v-31bd7746"><status-tag wx:if="{{exam.d}}" class="data-v-31bd7746" u-i="{{exam.c}}" bind:__l="__l" u-p="{{exam.d}}"/></view></view><view class="exam-content data-v-31bd7746"><view class="score-section data-v-31bd7746"><view class="{{['score-display', 'data-v-31bd7746', exam.f]}}"><text class="score-number data-v-31bd7746">{{exam.e}}</text><text class="score-unit data-v-31bd7746">分</text></view><view class="score-info data-v-31bd7746"><text class="{{['pass-status', 'data-v-31bd7746', exam.h && 'passed']}}">{{exam.g}}</text><text class="score-detail data-v-31bd7746">{{exam.i}}/{{exam.j}}题正确</text></view></view><view class="exam-meta data-v-31bd7746"><view class="meta-item data-v-31bd7746"><u-icon wx:if="{{k}}" class="data-v-31bd7746" u-i="{{exam.k}}" bind:__l="__l" u-p="{{k}}"/><text class="data-v-31bd7746">用时 {{exam.l}}</text></view><view class="meta-item data-v-31bd7746"><u-icon wx:if="{{l}}" class="data-v-31bd7746" u-i="{{exam.m}}" bind:__l="__l" u-p="{{l}}"/><text class="data-v-31bd7746">及格线 {{exam.n}}分</text></view></view></view><view class="exam-actions data-v-31bd7746"><u-button wx:if="{{m}}" u-s="{{['d']}}" class="detail-btn data-v-31bd7746" catchclick="{{exam.o}}" u-i="{{exam.p}}" bind:__l="__l" u-p="{{m}}"> 查看详情 </u-button><u-button wx:if="{{exam.q}}" u-s="{{['d']}}" class="certificate-btn data-v-31bd7746" catchclick="{{exam.r}}" u-i="{{exam.s}}" bind:__l="__l" u-p="{{exam.t}}"> 查看证书 </u-button></view></view></view><view wx:if="{{n}}" class="load-more data-v-31bd7746" bindtap="{{o}}"><text class="data-v-31bd7746">加载更多</text></view></view>