"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
const src_stores_app = require("../../../src/stores/app.js");
const src_utils_index = require("../../../src/utils/index.js");
const src_constants_index = require("../../../src/constants/index.js");
const src_api_index = require("../../../src/api/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_navbar2 + _easycom_u_icon2 + _easycom_u_button2)();
}
const _easycom_u_navbar = () => "../../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_navbar + LoadingSpinner + EmptyState + StatusTag + _easycom_u_icon + _easycom_u_button)();
}
const LoadingSpinner = () => "../../../src/components/common/LoadingSpinner.js";
const EmptyState = () => "../../../src/components/common/EmptyState.js";
const StatusTag = () => "../../../src/components/common/StatusTag.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "history",
  setup(__props) {
    const appStore = src_stores_app.useAppStore();
    const isLoading = common_vendor.ref(true);
    const examHistory = common_vendor.ref([]);
    const currentFilter = common_vendor.ref("all");
    const currentPage = common_vendor.ref(1);
    const hasMore = common_vendor.ref(true);
    const filterOptions = [
      { key: "all", label: "全部" },
      { key: "passed", label: "已通过" },
      { key: "failed", label: "未通过" },
      { key: "online", label: "线上考试" },
      { key: "offline", label: "线下考试" }
    ];
    const examStats = common_vendor.ref({
      total: 0,
      passed: 0,
      averageScore: 0
    });
    const filteredExamHistory = common_vendor.computed(() => {
      let filtered = examHistory.value;
      switch (currentFilter.value) {
        case "passed":
          filtered = filtered.filter((exam) => exam.passed);
          break;
        case "failed":
          filtered = filtered.filter((exam) => !exam.passed);
          break;
        case "online":
          filtered = filtered.filter((exam) => exam.examType === "online");
          break;
        case "offline":
          filtered = filtered.filter((exam) => exam.examType === "offline");
          break;
      }
      return filtered;
    });
    common_vendor.onMounted(() => {
      loadExamHistory();
      loadExamStats();
    });
    const loadExamHistory = (page = 1) => __async(this, null, function* () {
      if (page === 1) {
        isLoading.value = true;
      }
      try {
        const response = yield src_api_index.api.exam.getExamHistory({
          page,
          limit: 10
        });
        if (page === 1) {
          examHistory.value = response.data.list;
        } else {
          examHistory.value.push(...response.data.list);
        }
        hasMore.value = response.data.hasMore;
        currentPage.value = page;
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/exam/history/history.vue:220", "加载考试记录失败:", error);
        appStore.showToast(error.message || "加载失败");
      } finally {
        isLoading.value = false;
      }
    });
    const loadExamStats = () => __async(this, null, function* () {
      try {
        const response = yield src_api_index.api.exam.getExamStats();
        examStats.value = response.data;
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/exam/history/history.vue:233", "加载考试统计失败:", error);
      }
    });
    const getScoreClass = (score) => {
      if (score >= 90)
        return "excellent";
      if (score >= 80)
        return "good";
      if (score >= 60)
        return "normal";
      return "poor";
    };
    const viewExamDetail = (exam) => {
      if (exam.examType === "online") {
        appStore.navigateTo(src_constants_index.PAGE_PATHS.STUDY_SUMMARY, {
          sessionId: exam.id
        });
      } else {
        appStore.navigateTo(src_constants_index.PAGE_PATHS.EXAM_OFFLINE_DETAIL, {
          examId: exam.examId
        });
      }
    };
    const viewCertificate = (exam) => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.PERSONAL_CERTIFICATE, {
        certificateId: exam.certificateId
      });
    };
    const loadMore = () => {
      if (hasMore.value && !isLoading.value) {
        loadExamHistory(currentPage.value + 1);
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "考试记录",
          autoBack: true,
          background: {
            background: "linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: common_vendor.t(examStats.value.total),
        c: common_vendor.t(examStats.value.passed),
        d: common_vendor.t(examStats.value.averageScore),
        e: common_vendor.f(filterOptions, (filter, k0, i0) => {
          return {
            a: common_vendor.t(filter.label),
            b: filter.key,
            c: currentFilter.value === filter.key ? 1 : "",
            d: common_vendor.o(($event) => currentFilter.value = filter.key, filter.key)
          };
        }),
        f: isLoading.value
      }, isLoading.value ? {
        g: common_vendor.p({
          text: "加载考试记录..."
        })
      } : filteredExamHistory.value.length === 0 ? {
        i: common_vendor.p({
          type: "no-data",
          title: "暂无考试记录",
          description: "您还没有参加过考试",
          showButton: false
        })
      } : {
        j: common_vendor.f(filteredExamHistory.value, (exam, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(exam.examName),
            b: common_vendor.t(common_vendor.unref(src_utils_index.formatDate)(exam.completedAt, "YYYY-MM-DD HH:mm")),
            c: "31bd7746-3-" + i0,
            d: common_vendor.p({
              type: "exam-type",
              status: exam.examType
            }),
            e: common_vendor.t(exam.score),
            f: common_vendor.n(getScoreClass(exam.score)),
            g: common_vendor.t(exam.passed ? "通过" : "未通过"),
            h: exam.passed ? 1 : "",
            i: common_vendor.t(exam.correctCount),
            j: common_vendor.t(exam.totalCount),
            k: "31bd7746-4-" + i0,
            l: common_vendor.t(common_vendor.unref(src_utils_index.formatDuration)(exam.duration)),
            m: "31bd7746-5-" + i0,
            n: common_vendor.t(exam.passScore),
            o: common_vendor.o(($event) => viewExamDetail(exam), exam.id),
            p: "31bd7746-6-" + i0,
            q: exam.certificateId
          }, exam.certificateId ? {
            r: common_vendor.o(($event) => viewCertificate(exam), exam.id),
            s: "31bd7746-7-" + i0,
            t: common_vendor.p({
              type: "success",
              size: "small"
            })
          } : {}, {
            v: exam.id,
            w: common_vendor.o(($event) => viewExamDetail(exam), exam.id)
          });
        }),
        k: common_vendor.p({
          name: "clock",
          color: "#666",
          size: "24"
        }),
        l: common_vendor.p({
          name: "checkmark-circle",
          color: "#666",
          size: "24"
        }),
        m: common_vendor.p({
          type: "info",
          size: "small",
          plain: true
        })
      }, {
        h: filteredExamHistory.value.length === 0,
        n: hasMore.value && !isLoading.value
      }, hasMore.value && !isLoading.value ? {
        o: common_vendor.o(loadMore)
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-31bd7746"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpages/exam/history/history.js.map
