{"version": 3, "file": "card.js", "sources": ["uni_modules/uview-plus/components/u-card/card.js"], "sourcesContent": ["/*\r\n * <AUTHOR> jry\r\n * @Description  :\r\n * @version      : 3.0\r\n * @Date         : 2025-04-26 16:37:21\r\n * @LastAuthor   : jry\r\n * @lastTime     : 2025-04-26 16:37:21\r\n * @FilePath     : /uview-plus/libs/config/props/card.js\r\n */\r\nexport default {\r\n\t// card组件的props\r\n\tcard: {\r\n\t\tfull: false,\r\n\t\ttitle: '',\r\n\t\ttitleColor: '#303133',\r\n\t\ttitleSize: '15px',\r\n\t\tsubTitle: '',\r\n\t\tsubTitleColor: '#909399',\r\n\t\tsubTitleSize: '13px',\r\n\t\tborder: true,\r\n\t\tindex: '',\r\n\t\tmargin: '15px',\r\n\t\tborderRadius: '8px',\r\n\t\theadStyle: {},\r\n\t\tbodyStyle: {},\r\n\t\tfootStyle: {},\r\n\t\theadBorderBottom: true,\r\n\t\tfootBorderTop: true,\r\n\t\tthumb: '',\r\n\t\tthumbWidth: '30px',\r\n\t\tthumbCircle: false,\r\n\t\tpadding: '15px',\r\n\t\tpaddingHead: '',\r\n        paddingBody: '',\r\n        paddingFoot: '',\r\n        showHead: true,\r\n        showFoot: true,\r\n        boxShadow: 'none'\r\n\t}\r\n}\r\n"], "names": [], "mappings": ";AASA,MAAe,OAAA;AAAA;AAAA,EAEd,MAAM;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,IACd,QAAQ;AAAA,IACR,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,WAAW,CAAE;AAAA,IACb,WAAW,CAAE;AAAA,IACb,WAAW,CAAE;AAAA,IACb,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,SAAS;AAAA,IACT,aAAa;AAAA,IACP,aAAa;AAAA,IACb,aAAa;AAAA,IACb,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,EACjB;AACF;;"}