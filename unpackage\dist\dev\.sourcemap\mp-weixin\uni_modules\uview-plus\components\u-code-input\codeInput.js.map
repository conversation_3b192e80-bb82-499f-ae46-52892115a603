{"version": 3, "file": "codeInput.js", "sources": ["uni_modules/uview-plus/components/u-code-input/codeInput.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:55:58\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/codeInput.js\n */\nexport default {\n    // codeInput 组件\n    codeInput: {\n\t\tadjustPosition: true,\n        maxlength: 6,\n        dot: false,\n        mode: 'box',\n        hairline: false,\n        space: 10,\n        value: '',\n        focus: false,\n        bold: false,\n        color: '#606266',\n        fontSize: 18,\n        size: 35,\n        disabledKeyboard: false,\n        borderColor: '#c9cacc',\n\t\tdisabledDot: true\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,YAAA;AAAA;AAAA,EAEX,WAAW;AAAA,IACb,gBAAgB;AAAA,IACV,WAAW;AAAA,IACX,KAAK;AAAA,IACL,MAAM;AAAA,IACN,UAAU;AAAA,IACV,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,kBAAkB;AAAA,IAClB,aAAa;AAAA,IACnB,aAAa;AAAA,EACV;AACL;;"}