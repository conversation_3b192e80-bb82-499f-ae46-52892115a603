{"version": 3, "file": "permission.js", "sources": ["src/utils/permission.ts"], "sourcesContent": ["import { useUserStore } from '../stores/user'\nimport { useAppStore } from '../stores/app'\nimport { PAGE_PATHS } from '../constants'\nimport type { UserStatus } from '../types'\n\n/**\n * 权限控制工具类\n */\nexport class PermissionManager {\n  private _userStore: ReturnType<typeof useUserStore> | null = null\n  private _appStore: ReturnType<typeof useAppStore> | null = null\n\n  private get userStore() {\n    if (!this._userStore) {\n      this._userStore = useUserStore()\n    }\n    return this._userStore\n  }\n\n  private get appStore() {\n    if (!this._appStore) {\n      this._appStore = useAppStore()\n    }\n    return this._appStore\n  }\n\n  /**\n   * 检查用户是否已登录\n   */\n  isLoggedIn(): boolean {\n    return this.userStore.isLoggedIn\n  }\n\n  /**\n   * 检查用户是否已认证（通过审核）\n   */\n  isAuthenticated(): boolean {\n    return this.userStore.isAuthenticated\n  }\n\n  /**\n   * 检查用户状态\n   */\n  getUserStatus(): UserStatus | null {\n    return this.userStore.userInfo?.status || null\n  }\n\n  /**\n   * 检查是否可以访问考试功能\n   */\n  canAccessExam(): boolean {\n    return this.userStore.canAccessExam\n  }\n\n  /**\n   * 检查是否可以访问完整功能\n   */\n  canAccessFullFeatures(): boolean {\n    return this.userStore.canAccessFullFeatures\n  }\n\n  /**\n   * 页面访问权限检查\n   */\n  checkPageAccess(pagePath: string): {\n    allowed: boolean\n    redirectTo?: string\n    message?: string\n  } {\n    // 公开页面，无需权限检查\n    const publicPages = [\n      PAGE_PATHS.LOGIN,\n      PAGE_PATHS.PROFILE,\n    ]\n\n    if (publicPages.includes(pagePath)) {\n      return { allowed: true }\n    }\n\n    // 检查是否已登录\n    if (!this.isLoggedIn()) {\n      return {\n        allowed: false,\n        redirectTo: PAGE_PATHS.LOGIN,\n        message: '请先登录'\n      }\n    }\n\n    // 需要认证的页面\n    const authRequiredPages = [\n      PAGE_PATHS.EXAM,\n      PAGE_PATHS.EXAM_ONLINE_READING,\n      PAGE_PATHS.EXAM_ONLINE_FACE_VERIFY,\n      PAGE_PATHS.EXAM_ONLINE_ANSWER,\n      PAGE_PATHS.EXAM_OFFLINE_DETAIL,\n      PAGE_PATHS.EXAM_HISTORY,\n    ]\n\n    if (authRequiredPages.includes(pagePath)) {\n      if (!this.isAuthenticated()) {\n        return {\n          allowed: false,\n          redirectTo: PAGE_PATHS.PERSONAL,\n          message: '请先完善个人资料并通过审核'\n        }\n      }\n    }\n\n    // 部分功能页面（未认证用户可访问但功能受限）\n    const partialAccessPages = [\n      PAGE_PATHS.INFO,\n      PAGE_PATHS.STUDY,\n      PAGE_PATHS.PERSONAL,\n    ]\n\n    if (partialAccessPages.includes(pagePath)) {\n      return { allowed: true }\n    }\n\n    return { allowed: true }\n  }\n\n  /**\n   * 功能权限检查\n   */\n  checkFeatureAccess(feature: string): {\n    allowed: boolean\n    message?: string\n  } {\n    const userStatus = this.getUserStatus()\n\n    switch (feature) {\n      case 'practice':\n        // 练习功能：所有用户都可以使用，但有次数限制\n        return { allowed: true }\n\n      case 'exam':\n        // 考试功能：只有认证用户可以使用\n        if (!this.isAuthenticated()) {\n          return {\n            allowed: false,\n            message: '请先完善个人资料并通过机构审核后才能参加考试'\n          }\n        }\n        return { allowed: true }\n\n      case 'certificate':\n        // 证书功能：只有认证用户可以使用\n        if (!this.isAuthenticated()) {\n          return {\n            allowed: false,\n            message: '请先完善个人资料并通过机构审核'\n          }\n        }\n        return { allowed: true }\n\n      case 'profile_edit':\n        // 资料编辑：待审核和已认证用户不能编辑基本信息\n        if (userStatus === 'pending' || userStatus === 'approved') {\n          return {\n            allowed: false,\n            message: '资料审核中或已通过审核，无法修改基本信息'\n          }\n        }\n        return { allowed: true }\n\n      case 'unlimited_practice':\n        // 无限练习：只有认证用户可以使用\n        if (!this.isAuthenticated()) {\n          return {\n            allowed: false,\n            message: '认证用户可享受无限练习，请先完善资料并通过审核'\n          }\n        }\n        return { allowed: true }\n\n      default:\n        return { allowed: true }\n    }\n  }\n\n  /**\n   * 执行权限检查并处理结果\n   */\n  async enforcePageAccess(pagePath: string): Promise<boolean> {\n    const result = this.checkPageAccess(pagePath)\n\n    if (!result.allowed) {\n      if (result.message) {\n        this.appStore.showToast(result.message)\n      }\n\n      if (result.redirectTo) {\n        // 延迟跳转，确保toast显示\n        setTimeout(() => {\n          if (result.redirectTo === PAGE_PATHS.LOGIN) {\n            this.appStore.redirectTo(result.redirectTo)\n          } else {\n            this.appStore.switchTab(result.redirectTo)\n          }\n        }, 1500)\n      }\n\n      return false\n    }\n\n    return true\n  }\n\n  /**\n   * 执行功能权限检查\n   */\n  enforceFeatureAccess(feature: string): boolean {\n    const result = this.checkFeatureAccess(feature)\n\n    if (!result.allowed && result.message) {\n      this.appStore.showToast(result.message)\n    }\n\n    return result.allowed\n  }\n\n  /**\n   * 获取用户状态提示信息\n   */\n  getUserStatusMessage(): string {\n    const userStatus = this.getUserStatus()\n\n    switch (userStatus) {\n      case 'not_submitted':\n        return '请完善个人资料以使用完整功能'\n      case 'pending':\n        return '个人资料审核中，请耐心等待'\n      case 'approved':\n        return '已通过认证，可使用所有功能'\n      case 'rejected':\n        return '资料审核未通过，请重新提交'\n      default:\n        return '请先登录'\n    }\n  }\n\n  /**\n   * 获取功能限制提示\n   */\n  getFeatureLimitMessage(feature: string): string {\n    switch (feature) {\n      case 'practice':\n        if (!this.isAuthenticated()) {\n          return '未认证用户每日限制3次练习'\n        }\n        return '认证用户可无限练习'\n      case 'exam':\n        return '需要通过机构审核后才能参加正式考试'\n      case 'certificate':\n        return '需要通过考试后才能获得证书'\n      default:\n        return ''\n    }\n  }\n}\n\n// 延迟创建全局权限管理器实例\nlet _permissionManager: PermissionManager | null = null\n\nexport const getPermissionManager = (): PermissionManager => {\n  if (!_permissionManager) {\n    _permissionManager = new PermissionManager()\n  }\n  return _permissionManager\n}\n\n// 为了保持向后兼容性，提供一个getter\nexport const permissionManager = {\n  get instance() {\n    return getPermissionManager()\n  }\n}\n\n// 页面权限检查装饰器\nexport function requireAuth(target: any, propertyKey: string, descriptor: PropertyDescriptor) {\n  const originalMethod = descriptor.value\n\n  descriptor.value = async function (...args: any[]) {\n    const manager = getPermissionManager()\n    if (!manager.isLoggedIn()) {\n      manager.appStore.showToast('请先登录')\n      manager.appStore.redirectTo(PAGE_PATHS.LOGIN)\n      return\n    }\n\n    return originalMethod.apply(this, args)\n  }\n\n  return descriptor\n}\n\n// 认证权限检查装饰器\nexport function requireAuthentication(target: any, propertyKey: string, descriptor: PropertyDescriptor) {\n  const originalMethod = descriptor.value\n\n  descriptor.value = async function (...args: any[]) {\n    if (!permissionManager.isAuthenticated()) {\n      permissionManager.appStore.showToast('请先完善个人资料并通过审核')\n      permissionManager.appStore.switchTab(PAGE_PATHS.PERSONAL)\n      return\n    }\n\n    return originalMethod.apply(this, args)\n  }\n\n  return descriptor\n}\n\n// 导出权限检查函数\nexport const checkAuth = () => permissionManager.isLoggedIn()\nexport const checkAuthentication = () => permissionManager.isAuthenticated()\nexport const checkPageAccess = (pagePath: string) => permissionManager.checkPageAccess(pagePath)\nexport const checkFeatureAccess = (feature: string) => permissionManager.checkFeatureAccess(feature)\nexport const enforcePageAccess = (pagePath: string) => permissionManager.enforcePageAccess(pagePath)\nexport const enforceFeatureAccess = (feature: string) => permissionManager.enforceFeatureAccess(feature)\n"], "names": ["useUserStore", "useAppStore", "PAGE_PATHS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAQO,MAAM,kBAAkB;AAAA,EAAxB,cAAA;AACL,SAAQ,aAAqD;AAC7D,SAAQ,YAAmD;AAAA,EAAA;AAAA,EAE3D,IAAY,YAAY;AAClB,QAAA,CAAC,KAAK,YAAY;AACpB,WAAK,aAAaA,gBAAAA;IACpB;AACA,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAY,WAAW;AACjB,QAAA,CAAC,KAAK,WAAW;AACnB,WAAK,YAAYC,eAAAA;IACnB;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,aAAsB;AACpB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,kBAA2B;AACzB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAmC;;AAC1B,aAAA,UAAK,UAAU,aAAf,mBAAyB,WAAU;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAyB;AACvB,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,wBAAiC;AAC/B,WAAO,KAAK,UAAU;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,gBAAgB,UAId;AAEA,UAAM,cAAc;AAAA,MAClBC,oBAAAA,WAAW;AAAA,MACXA,oBAAAA,WAAW;AAAA,IAAA;AAGT,QAAA,YAAY,SAAS,QAAQ,GAAG;AAC3B,aAAA,EAAE,SAAS;IACpB;AAGI,QAAA,CAAC,KAAK,cAAc;AACf,aAAA;AAAA,QACL,SAAS;AAAA,QACT,YAAYA,oBAAW,WAAA;AAAA,QACvB,SAAS;AAAA,MAAA;AAAA,IAEb;AAGA,UAAM,oBAAoB;AAAA,MACxBA,oBAAAA,WAAW;AAAA,MACXA,oBAAAA,WAAW;AAAA,MACXA,oBAAAA,WAAW;AAAA,MACXA,oBAAAA,WAAW;AAAA,MACXA,oBAAAA,WAAW;AAAA,MACXA,oBAAAA,WAAW;AAAA,IAAA;AAGT,QAAA,kBAAkB,SAAS,QAAQ,GAAG;AACpC,UAAA,CAAC,KAAK,mBAAmB;AACpB,eAAA;AAAA,UACL,SAAS;AAAA,UACT,YAAYA,oBAAW,WAAA;AAAA,UACvB,SAAS;AAAA,QAAA;AAAA,MAEb;AAAA,IACF;AAGA,UAAM,qBAAqB;AAAA,MACzBA,oBAAAA,WAAW;AAAA,MACXA,oBAAAA,WAAW;AAAA,MACXA,oBAAAA,WAAW;AAAA,IAAA;AAGT,QAAA,mBAAmB,SAAS,QAAQ,GAAG;AAClC,aAAA,EAAE,SAAS;IACpB;AAEO,WAAA,EAAE,SAAS;EACpB;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,SAGjB;AACM,UAAA,aAAa,KAAK;AAExB,YAAQ,SAAS;AAAA,MACf,KAAK;AAEI,eAAA,EAAE,SAAS;MAEpB,KAAK;AAEC,YAAA,CAAC,KAAK,mBAAmB;AACpB,iBAAA;AAAA,YACL,SAAS;AAAA,YACT,SAAS;AAAA,UAAA;AAAA,QAEb;AACO,eAAA,EAAE,SAAS;MAEpB,KAAK;AAEC,YAAA,CAAC,KAAK,mBAAmB;AACpB,iBAAA;AAAA,YACL,SAAS;AAAA,YACT,SAAS;AAAA,UAAA;AAAA,QAEb;AACO,eAAA,EAAE,SAAS;MAEpB,KAAK;AAEC,YAAA,eAAe,aAAa,eAAe,YAAY;AAClD,iBAAA;AAAA,YACL,SAAS;AAAA,YACT,SAAS;AAAA,UAAA;AAAA,QAEb;AACO,eAAA,EAAE,SAAS;MAEpB,KAAK;AAEC,YAAA,CAAC,KAAK,mBAAmB;AACpB,iBAAA;AAAA,YACL,SAAS;AAAA,YACT,SAAS;AAAA,UAAA;AAAA,QAEb;AACO,eAAA,EAAE,SAAS;MAEpB;AACS,eAAA,EAAE,SAAS;IACtB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKM,kBAAkB,UAAoC;AAAA;AACpD,YAAA,SAAS,KAAK,gBAAgB,QAAQ;AAExC,UAAA,CAAC,OAAO,SAAS;AACnB,YAAI,OAAO,SAAS;AACb,eAAA,SAAS,UAAU,OAAO,OAAO;AAAA,QACxC;AAEA,YAAI,OAAO,YAAY;AAErB,qBAAW,MAAM;AACX,gBAAA,OAAO,eAAeA,oBAAA,WAAW,OAAO;AACrC,mBAAA,SAAS,WAAW,OAAO,UAAU;AAAA,YAAA,OACrC;AACA,mBAAA,SAAS,UAAU,OAAO,UAAU;AAAA,YAC3C;AAAA,aACC,IAAI;AAAA,QACT;AAEO,eAAA;AAAA,MACT;AAEO,aAAA;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,qBAAqB,SAA0B;AACvC,UAAA,SAAS,KAAK,mBAAmB,OAAO;AAE9C,QAAI,CAAC,OAAO,WAAW,OAAO,SAAS;AAChC,WAAA,SAAS,UAAU,OAAO,OAAO;AAAA,IACxC;AAEA,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAKA,uBAA+B;AACvB,UAAA,aAAa,KAAK;AAExB,YAAQ,YAAY;AAAA,MAClB,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT;AACS,eAAA;AAAA,IACX;AAAA,EACF;AAAA;AAAA;AAAA;AAAA,EAKA,uBAAuB,SAAyB;AAC9C,YAAQ,SAAS;AAAA,MACf,KAAK;AACC,YAAA,CAAC,KAAK,mBAAmB;AACpB,iBAAA;AAAA,QACT;AACO,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT,KAAK;AACI,eAAA;AAAA,MACT;AACS,eAAA;AAAA,IACX;AAAA,EACF;AACF;AAGA,IAAI,qBAA+C;AAE5C,MAAM,uBAAuB,MAAyB;AAC3D,MAAI,CAAC,oBAAoB;AACvB,yBAAqB,IAAI;EAC3B;AACO,SAAA;AACT;AAGO,MAAM,oBAAoB;AAAA,EAC/B,IAAI,WAAW;AACb,WAAO,qBAAqB;AAAA,EAC9B;AACF;;;"}