{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-navbar/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\r\nimport defProps from '../../libs/config/props.js'\r\n\r\nexport const props = defineMixin({\r\n\tprops: {\r\n\t\t// 是否开启顶部安全区适配\r\n\t\tsafeAreaInsetTop: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.navbar.safeAreaInsetTop\r\n\t\t},\r\n\t\t// 固定在顶部时，是否生成一个等高元素，以防止塌陷\r\n\t\tplaceholder: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.navbar.placeholder\r\n\t\t},\r\n\t\t// 是否固定在顶部\r\n\t\tfixed: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.navbar.fixed\r\n\t\t},\r\n\t\t// 是否显示下边框\r\n\t\tborder: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.navbar.border\r\n\t\t},\r\n\t\t// 左边的图标\r\n\t\tleftIcon: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.navbar.leftIcon\r\n\t\t},\r\n\t\t// 左边的提示文字\r\n\t\tleftText: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.navbar.leftText\r\n\t\t},\r\n\t\t// 左右的提示文字\r\n\t\trightText: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.navbar.rightText\r\n\t\t},\r\n\t\t// 右边的图标\r\n\t\trightIcon: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.navbar.rightIcon\r\n\t\t},\r\n\t\t// 标题\r\n\t\ttitle: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.navbar.title\r\n\t\t},\r\n\t\t// 标题颜色\r\n\t\ttitleColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.navbar.titleColor\r\n\t\t},\r\n\t\t// 背景颜色\r\n\t\tbgColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.navbar.bgColor\r\n\t\t},\r\n        // 状态栏背景颜色 不写会使用背景颜色bgColor\r\n        statusBarBgColor: {\r\n            type: String,\r\n            default: () => ''\r\n        },\r\n\t\t// 标题的宽度\r\n\t\ttitleWidth: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.navbar.titleWidth\r\n\t\t},\r\n\t\t// 导航栏高度\r\n\t\theight: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.navbar.height\r\n\t\t},\r\n\t\t// 左侧返回图标的大小\r\n\t\tleftIconSize: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: () => defProps.navbar.leftIconSize\r\n\t\t},\r\n\t\t// 左侧返回图标的颜色\r\n\t\tleftIconColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: () => defProps.navbar.leftIconColor\r\n\t\t},\r\n\t\t// 点击左侧区域(返回图标)，是否自动返回上一页\r\n\t\tautoBack: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: () => defProps.navbar.autoBack\r\n\t\t},\r\n\t\t// 标题的样式，对象或字符串\r\n\t\ttitleStyle: {\r\n\t\t\ttype: [String, Object],\r\n\t\t\tdefault: () => defProps.navbar.titleStyle\r\n\t\t}\r\n\t}\r\n})\r\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAGY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAChC,OAAO;AAAA;AAAA,IAEN,kBAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,SAAS;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAEK,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAM;AAAA,IAClB;AAAA;AAAA,IAEP,YAAY;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA,EACD;AACF,CAAC;;"}