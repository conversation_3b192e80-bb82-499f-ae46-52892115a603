<view class="face-verify-container data-v-d787804b"><u-navbar wx:if="{{a}}" class="data-v-d787804b" u-i="d787804b-0" bind:__l="__l" u-p="{{a}}"/><view class="verify-steps data-v-d787804b"><view class="{{['step-item', 'data-v-d787804b', d && 'active', e && 'completed']}}"><view class="step-icon data-v-d787804b"><u-icon wx:if="{{b}}" class="data-v-d787804b" u-i="d787804b-1" bind:__l="__l" u-p="{{c}}"/><text wx:else class="data-v-d787804b">1</text></view><text class="step-text data-v-d787804b">身份确认</text></view><view class="{{['step-line', 'data-v-d787804b', f && 'active']}}"></view><view class="{{['step-item', 'data-v-d787804b', i && 'active', j && 'completed']}}"><view class="step-icon data-v-d787804b"><u-icon wx:if="{{g}}" class="data-v-d787804b" u-i="d787804b-2" bind:__l="__l" u-p="{{h}}"/><text wx:else class="data-v-d787804b">2</text></view><text class="step-text data-v-d787804b">人脸识别</text></view><view class="{{['step-line', 'data-v-d787804b', k && 'active']}}"></view><view class="{{['step-item', 'data-v-d787804b', l && 'active']}}"><view class="step-icon data-v-d787804b"><text class="data-v-d787804b">3</text></view><text class="step-text data-v-d787804b">验证完成</text></view></view><view wx:if="{{m}}" class="step-content data-v-d787804b"><view class="identity-confirm data-v-d787804b"><view class="user-info-card data-v-d787804b"><view class="avatar-section data-v-d787804b"><image src="{{n}}" class="user-avatar data-v-d787804b" mode="aspectFill"/></view><view class="info-section data-v-d787804b"><text class="user-name data-v-d787804b">{{o}}</text><text class="user-id data-v-d787804b">身份证：{{p}}</text><text class="user-org data-v-d787804b">{{q}}</text></view></view><view class="confirm-tips data-v-d787804b"><u-icon wx:if="{{r}}" class="data-v-d787804b" u-i="d787804b-3" bind:__l="__l" u-p="{{r}}"/><text class="tips-text data-v-d787804b">请确认以上信息是否为您本人信息，确认无误后进行人脸识别验证</text></view><u-button wx:if="{{t}}" u-s="{{['d']}}" class="confirm-btn data-v-d787804b" bindclick="{{s}}" u-i="d787804b-4" bind:__l="__l" u-p="{{t}}"> 确认身份，开始验证 </u-button></view></view><view wx:elif="{{v}}" class="step-content data-v-d787804b"><view class="face-recognition data-v-d787804b"><view class="camera-section data-v-d787804b"><view class="camera-frame data-v-d787804b"><camera wx:if="{{w}}" class="camera-view data-v-d787804b" device-position="front" flash="off" binderror="{{x}}"/><view wx:else class="camera-placeholder data-v-d787804b"><u-icon wx:if="{{y}}" class="data-v-d787804b" u-i="d787804b-5" bind:__l="__l" u-p="{{y}}"/><text class="placeholder-text data-v-d787804b">摄像头加载中...</text></view><view class="face-frame data-v-d787804b"><view class="frame-corner tl data-v-d787804b"></view><view class="frame-corner tr data-v-d787804b"></view><view class="frame-corner bl data-v-d787804b"></view><view class="frame-corner br data-v-d787804b"></view></view><view wx:if="{{z}}" class="{{['verify-status', 'data-v-d787804b', B]}}"><text class="data-v-d787804b">{{A}}</text></view></view><view class="camera-tips data-v-d787804b"><text class="tips-title data-v-d787804b">请按照以下要求进行人脸识别：</text><view class="tips-list data-v-d787804b"><view class="tip-item data-v-d787804b"><u-icon wx:if="{{C}}" class="data-v-d787804b" u-i="d787804b-6" bind:__l="__l" u-p="{{C}}"/><text class="data-v-d787804b">保持面部正对摄像头</text></view><view class="tip-item data-v-d787804b"><u-icon wx:if="{{D}}" class="data-v-d787804b" u-i="d787804b-7" bind:__l="__l" u-p="{{D}}"/><text class="data-v-d787804b">确保光线充足，面部清晰</text></view><view class="tip-item data-v-d787804b"><u-icon wx:if="{{E}}" class="data-v-d787804b" u-i="d787804b-8" bind:__l="__l" u-p="{{E}}"/><text class="data-v-d787804b">摘下眼镜、帽子等遮挡物</text></view></view></view></view><view class="verify-actions data-v-d787804b"><u-button wx:if="{{H}}" u-s="{{['d']}}" class="capture-btn data-v-d787804b" bindclick="{{G}}" u-i="d787804b-9" bind:__l="__l" u-p="{{H}}">{{F}}</u-button><view wx:if="{{I}}" class="retry-tips data-v-d787804b"><text class="data-v-d787804b">识别失败，请调整姿势和光线后重试</text></view></view></view></view><view wx:elif="{{J}}" class="step-content data-v-d787804b"><view class="verify-success data-v-d787804b"><view class="success-icon data-v-d787804b"><u-icon wx:if="{{K}}" class="data-v-d787804b" u-i="d787804b-10" bind:__l="__l" u-p="{{K}}"/></view><text class="success-title data-v-d787804b">人脸识别验证成功</text><text class="success-desc data-v-d787804b">身份验证通过，即将进入考试界面</text><view class="verify-result data-v-d787804b"><view class="result-item data-v-d787804b"><text class="result-label data-v-d787804b">相似度：</text><text class="result-value data-v-d787804b">{{L}}%</text></view><view class="result-item data-v-d787804b"><text class="result-label data-v-d787804b">验证时间：</text><text class="result-value data-v-d787804b">{{M}}</text></view></view><u-button wx:if="{{O}}" u-s="{{['d']}}" class="enter-exam-btn data-v-d787804b" bindclick="{{N}}" u-i="d787804b-11" bind:__l="__l" u-p="{{O}}"> 进入考试 </u-button></view></view><u-modal wx:if="{{S}}" class="data-v-d787804b" bindconfirm="{{P}}" bindcancel="{{Q}}" u-i="d787804b-12" bind:__l="__l" bindupdateModelValue="{{R}}" u-p="{{S}}"/></view>