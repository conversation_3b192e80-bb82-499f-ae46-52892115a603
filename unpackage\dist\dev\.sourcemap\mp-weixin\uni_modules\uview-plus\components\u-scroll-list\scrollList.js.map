{"version": 3, "file": "scrollList.js", "sources": ["uni_modules/uview-plus/components/u-scroll-list/scrollList.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:19:28\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/scrollList.js\n */\nexport default {\n    // scrollList\n    scrollList: {\n        indicatorWidth: 50,\n        indicatorBarWidth: 20,\n        indicator: true,\n        indicatorColor: '#f2f2f2',\n        indicatorActiveColor: '#3c9cff',\n        indicatorStyle: ''\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,aAAA;AAAA;AAAA,EAEX,YAAY;AAAA,IACR,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,IACnB,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,sBAAsB;AAAA,IACtB,gBAAgB;AAAA,EACnB;AACL;;"}