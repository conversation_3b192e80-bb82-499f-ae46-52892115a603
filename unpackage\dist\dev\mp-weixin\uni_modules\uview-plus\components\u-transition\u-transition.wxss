/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-5cec8177,
.u-empty__wrap.data-v-5cec8177,
.u-tabs.data-v-5cec8177,
.u-tabs__wrapper.data-v-5cec8177,
.u-tabs__wrapper__scroll-view-wrapper.data-v-5cec8177,
.u-tabs__wrapper__scroll-view.data-v-5cec8177,
.u-tabs__wrapper__nav.data-v-5cec8177,
.u-tabs__wrapper__nav__line.data-v-5cec8177,
.up-empty.data-v-5cec8177,
.up-empty__wrap.data-v-5cec8177,
.up-tabs.data-v-5cec8177,
.up-tabs__wrapper.data-v-5cec8177,
.up-tabs__wrapper__scroll-view-wrapper.data-v-5cec8177,
.up-tabs__wrapper__scroll-view.data-v-5cec8177,
.up-tabs__wrapper__nav.data-v-5cec8177,
.up-tabs__wrapper__nav__line.data-v-5cec8177 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
/**
 * vue版本动画内置的动画模式有如下：
 * fade：淡入
 * zoom：缩放
 * fade-zoom：缩放淡入
 * fade-up：上滑淡入
 * fade-down：下滑淡入
 * fade-left：左滑淡入
 * fade-right：右滑淡入
 * slide-up：上滑进入
 * slide-down：下滑进入
 * slide-left：左滑进入
 * slide-right：右滑进入
 */
.u-fade-enter-active.data-v-5cec8177,
.u-fade-leave-active.data-v-5cec8177 {
  transition-property: opacity;
}
.u-fade-enter.data-v-5cec8177,
.u-fade-leave-to.data-v-5cec8177 {
  opacity: 0;
}
.u-fade-zoom-enter.data-v-5cec8177,
.u-fade-zoom-leave-to.data-v-5cec8177 {
  transform: scale(0.95);
  opacity: 0;
}
.u-fade-zoom-enter-active.data-v-5cec8177,
.u-fade-zoom-leave-active.data-v-5cec8177 {
  transition-property: transform, opacity;
}
.u-fade-down-enter-active.data-v-5cec8177,
.u-fade-down-leave-active.data-v-5cec8177,
.u-fade-left-enter-active.data-v-5cec8177,
.u-fade-left-leave-active.data-v-5cec8177,
.u-fade-right-enter-active.data-v-5cec8177,
.u-fade-right-leave-active.data-v-5cec8177,
.u-fade-up-enter-active.data-v-5cec8177,
.u-fade-up-leave-active.data-v-5cec8177 {
  transition-property: opacity, transform;
}
.u-fade-up-enter.data-v-5cec8177,
.u-fade-up-leave-to.data-v-5cec8177 {
  transform: translate3d(0, 100%, 0);
  opacity: 0;
}
.u-fade-down-enter.data-v-5cec8177,
.u-fade-down-leave-to.data-v-5cec8177 {
  transform: translate3d(0, -100%, 0);
  opacity: 0;
}
.u-fade-left-enter.data-v-5cec8177,
.u-fade-left-leave-to.data-v-5cec8177 {
  transform: translate3d(-100%, 0, 0);
  opacity: 0;
}
.u-fade-right-enter.data-v-5cec8177,
.u-fade-right-leave-to.data-v-5cec8177 {
  transform: translate3d(100%, 0, 0);
  opacity: 0;
}
.u-slide-down-enter-active.data-v-5cec8177,
.u-slide-down-leave-active.data-v-5cec8177,
.u-slide-left-enter-active.data-v-5cec8177,
.u-slide-left-leave-active.data-v-5cec8177,
.u-slide-right-enter-active.data-v-5cec8177,
.u-slide-right-leave-active.data-v-5cec8177,
.u-slide-up-enter-active.data-v-5cec8177,
.u-slide-up-leave-active.data-v-5cec8177 {
  transition-property: transform;
}
.u-slide-up-enter.data-v-5cec8177,
.u-slide-up-leave-to.data-v-5cec8177 {
  transform: translate3d(0, 100%, 0);
}
.u-slide-down-enter.data-v-5cec8177,
.u-slide-down-leave-to.data-v-5cec8177 {
  transform: translate3d(0, -100%, 0);
}
.u-slide-left-enter.data-v-5cec8177,
.u-slide-left-leave-to.data-v-5cec8177 {
  transform: translate3d(-100%, 0, 0);
}
.u-slide-right-enter.data-v-5cec8177,
.u-slide-right-leave-to.data-v-5cec8177 {
  transform: translate3d(100%, 0, 0);
}
.u-zoom-enter-active.data-v-5cec8177,
.u-zoom-leave-active.data-v-5cec8177 {
  transition-property: transform;
}
.u-zoom-enter.data-v-5cec8177,
.u-zoom-leave-to.data-v-5cec8177 {
  transform: scale(0.95);
}