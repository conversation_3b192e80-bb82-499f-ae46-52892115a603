/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-eadae74e,
.u-empty__wrap.data-v-eadae74e,
.u-tabs.data-v-eadae74e,
.u-tabs__wrapper.data-v-eadae74e,
.u-tabs__wrapper__scroll-view-wrapper.data-v-eadae74e,
.u-tabs__wrapper__scroll-view.data-v-eadae74e,
.u-tabs__wrapper__nav.data-v-eadae74e,
.u-tabs__wrapper__nav__line.data-v-eadae74e,
.up-empty.data-v-eadae74e,
.up-empty__wrap.data-v-eadae74e,
.up-tabs.data-v-eadae74e,
.up-tabs__wrapper.data-v-eadae74e,
.up-tabs__wrapper__scroll-view-wrapper.data-v-eadae74e,
.up-tabs__wrapper__scroll-view.data-v-eadae74e,
.up-tabs__wrapper__nav.data-v-eadae74e,
.up-tabs__wrapper__nav__line.data-v-eadae74e {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-toolbar.data-v-eadae74e {
  height: 42px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.u-toolbar__wrapper__cancel.data-v-eadae74e {
  color: #909193;
  font-size: 15px;
  padding: 0 15px;
}
.u-toolbar__title.data-v-eadae74e {
  color: #303133;
  padding: 0 60rpx;
  font-size: 16px;
  font-weight: bold;
  flex: 1;
  text-align: center;
}
.u-toolbar__wrapper__left.data-v-eadae74e, .u-toolbar__wrapper__right.data-v-eadae74e {
  display: flex;
  flex-direction: row;
}
.u-toolbar__wrapper__confirm.data-v-eadae74e {
  color: #3c9cff;
  font-size: 15px;
  padding: 0 15px;
}