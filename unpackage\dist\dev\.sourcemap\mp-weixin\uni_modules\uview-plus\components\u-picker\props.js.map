{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-picker/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        modelValue: {\n            type: Array,\n            default: () => []\n        },\n        hasInput: {\n            type: Boolean,\n            default: false\n        },\n        inputProps: {\n            type: Object,\n            default: () => {\n                return {}\n            }\n        },\n        disabled: {\n            type: Boolean,\n            default: () => defProps.picker.disabled\n        },\n\t\tdisabledColor:{\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.picker.disabledColor\n\t\t},\n        placeholder: {\n            type: String,\n            default: () => defProps.picker.placeholder\n        },\n        // 是否展示picker弹窗\n        show: {\n            type: Boolean,\n            default: () => defProps.picker.show\n        },\n\t\t// 弹出的方向，可选值为 top bottom right left center\n        popupMode: {\n            type: String,\n            default: () => defProps.picker.popupMode\n        },\n        // 是否展示顶部的操作栏\n        showToolbar: {\n            type: Boolean,\n            default: () => defProps.picker.showToolbar\n        },\n        // 顶部标题\n        title: {\n            type: String,\n            default: () => defProps.picker.title\n        },\n        // 对象数组，设置每一列的数据\n        columns: {\n            type: Array,\n            default: () => defProps.picker.columns\n        },\n        // 是否显示加载中状态\n        loading: {\n            type: Boolean,\n            default: () => defProps.picker.loading\n        },\n        // 各列中，单个选项的高度\n        itemHeight: {\n            type: [String, Number],\n            default: () => defProps.picker.itemHeight\n        },\n        // 取消按钮的文字\n        cancelText: {\n            type: String,\n            default: () => defProps.picker.cancelText\n        },\n        // 确认按钮的文字\n        confirmText: {\n            type: String,\n            default: () => defProps.picker.confirmText\n        },\n        // 取消按钮的颜色\n        cancelColor: {\n            type: String,\n            default: () => defProps.picker.cancelColor\n        },\n        // 确认按钮的颜色\n        confirmColor: {\n            type: String,\n            default: () => defProps.picker.confirmColor\n        },\n        // 每列中可见选项的数量\n        visibleItemCount: {\n            type: [String, Number],\n            default: () => defProps.picker.visibleItemCount\n        },\n        // 选项对象中，需要展示的属性键名\n        keyName: {\n            type: String,\n            default: () => defProps.picker.keyName\n        },\n\t\t// 选项对象中，需要获取的属性值键名\n\t\tvalueName: {\n\t\t    type: String,\n\t\t    default: () => defProps.picker.valueName\n\t\t},\n        // 是否允许点击遮罩关闭选择器\n        closeOnClickOverlay: {\n            type: Boolean,\n            default: () => defProps.picker.closeOnClickOverlay\n        },\n        // 各列的默认索引\n        defaultIndex: {\n            type: Array,\n            default: () => defProps.picker.defaultIndex\n        },\n\t\t// 是否在手指松开时立即触发 change 事件。若不开启则会在滚动动画结束后触发 change 事件，只在微信2.21.1及以上有效\n\t\timmediateChange: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.picker.immediateChange\n\t\t},\n        // 工具栏右侧插槽是否开启\n        toolbarRightSlot: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\t// 层级\n\t\tzIndex: {\n\t\t    type: [String, Number],\n\t\t    default: () => defProps.picker.zIndex\n\t\t},\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA,IACH,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAM,CAAE;AAAA,IACpB;AAAA,IACD,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACZ;AAAA,IACD,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAM;AACX,eAAO,CAAE;AAAA,MACZ;AAAA,IACJ;AAAA,IACD,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,OAAO;AAAA,IAClC;AAAA,IACP,eAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA,IACK,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,kBAAkB;AAAA,MACd,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAEP,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAEK,qBAAqB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAEP,iBAAiB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAC/B;AAAA;AAAA,IAEK,kBAAkB;AAAA,MACvB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA,EACE;AACL,CAAC;;"}