"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
const src_stores_user = require("../../../src/stores/user.js");
const src_stores_app = require("../../../src/stores/app.js");
const src_utils_index = require("../../../src/utils/index.js");
const src_constants_index = require("../../../src/constants/index.js");
const src_api_index = require("../../../src/api/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  (_easycom_u_navbar2 + _easycom_u_icon2 + _easycom_u_button2 + _easycom_u_modal2)();
}
const _easycom_u_navbar = () => "../../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_u_modal = () => "../../../uni_modules/uview-plus/components/u-modal/u-modal.js";
if (!Math) {
  (_easycom_u_navbar + _easycom_u_icon + _easycom_u_button + _easycom_u_modal)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "face-verify",
  props: {
    examId: {}
  },
  setup(__props) {
    const userStore = src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const props = __props;
    const currentStep = common_vendor.ref(1);
    const showCamera = common_vendor.ref(false);
    const isVerifying = common_vendor.ref(false);
    const isEntering = common_vendor.ref(false);
    const verifyStatus = common_vendor.ref("");
    const retryCount = common_vendor.ref(0);
    const similarity = common_vendor.ref(0);
    const showFailModal = common_vendor.ref(false);
    const failMessage = common_vendor.ref("");
    const maxRetry = src_constants_index.EXAM_CONFIG.FACE_VERIFY_MAX_RETRY;
    const userInfo = common_vendor.computed(() => userStore.userInfo);
    common_vendor.onMounted(() => {
      initCamera();
    });
    common_vendor.onUnmounted(() => {
      if (showCamera.value) {
        showCamera.value = false;
      }
    });
    const initCamera = () => __async(this, null, function* () {
      try {
        yield common_vendor.index.authorize({
          scope: "scope.camera"
        });
        setTimeout(() => {
          showCamera.value = true;
        }, 500);
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/exam/online/face-verify.vue:244", "摄像头初始化失败:", error);
        appStore.showModal({
          title: "摄像头权限",
          content: "需要摄像头权限进行人脸识别验证，请在设置中开启权限。",
          confirmText: "去设置",
          cancelText: "退出"
        }).then((confirmed) => {
          if (confirmed) {
            common_vendor.index.openSetting();
          } else {
            exitExam();
          }
        });
      }
    });
    const onCameraError = (error) => {
      common_vendor.index.__f__("error", "at subpages/exam/online/face-verify.vue:262", "摄像头错误:", error);
      appStore.showToast("摄像头启动失败，请重试");
      showCamera.value = false;
      setTimeout(() => {
        initCamera();
      }, 2e3);
    };
    const confirmIdentity = () => {
      currentStep.value = 2;
    };
    const getStatusText = () => {
      const statusMap = {
        detecting: "正在检测人脸...",
        verifying: "正在验证身份...",
        success: "验证成功",
        failed: "验证失败"
      };
      return statusMap[verifyStatus.value] || "";
    };
    const captureAndVerify = () => __async(this, null, function* () {
      if (isVerifying.value)
        return;
      isVerifying.value = true;
      verifyStatus.value = "detecting";
      try {
        const cameraContext = common_vendor.index.createCameraContext();
        const photo = yield new Promise((resolve, reject) => {
          cameraContext.takePhoto({
            quality: "high",
            success: (res) => {
              resolve(res.tempImagePath);
            },
            fail: (error) => {
              reject(error);
            }
          });
        });
        verifyStatus.value = "verifying";
        const response = yield src_api_index.api.exam.verifyFace({
          examId: props.examId,
          photo
        });
        if (response.data.success) {
          similarity.value = Math.round(response.data.similarity * 100);
          verifyStatus.value = "success";
          setTimeout(() => {
            currentStep.value = 3;
          }, 1500);
        } else {
          handleVerifyFailed("人脸识别失败，请确保面部清晰可见");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/exam/online/face-verify.vue:333", "人脸识别失败:", error);
        handleVerifyFailed(error.message || "识别过程中出现错误，请重试");
      } finally {
        isVerifying.value = false;
      }
    });
    const handleVerifyFailed = (message) => {
      verifyStatus.value = "failed";
      retryCount.value++;
      if (retryCount.value >= maxRetry) {
        failMessage.value = `验证失败次数过多，无法继续考试。${message}`;
      } else {
        failMessage.value = `${message}，还可重试 ${maxRetry - retryCount.value} 次。`;
      }
      showFailModal.value = true;
    };
    const handleFailConfirm = () => {
      showFailModal.value = false;
      if (retryCount.value >= maxRetry) {
        exitExam();
      } else {
        verifyStatus.value = "";
      }
    };
    const enterExam = () => __async(this, null, function* () {
      isEntering.value = true;
      try {
        const response = yield src_api_index.api.exam.startExam(props.examId);
        appStore.redirectTo(src_constants_index.PAGE_PATHS.EXAM_ONLINE_ANSWER, {
          examId: props.examId,
          recordId: response.data.recordId
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/exam/online/face-verify.vue:381", "开始考试失败:", error);
        appStore.showToast(error.message || "开始考试失败，请重试");
      } finally {
        isEntering.value = false;
      }
    });
    const exitExam = () => {
      appStore.showModal({
        title: "确认退出",
        content: "确定要退出考试吗？退出后需要重新进行身份验证。",
        confirmText: "确认退出",
        cancelText: "取消"
      }).then((confirmed) => {
        if (confirmed) {
          appStore.navigateBack(2);
        }
      });
    };
    return (_ctx, _cache) => {
      var _a, _b, _c, _d;
      return common_vendor.e({
        a: common_vendor.p({
          title: "人脸识别验证",
          autoBack: false,
          background: {
            background: "linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: currentStep.value > 1
      }, currentStep.value > 1 ? {
        c: common_vendor.p({
          name: "checkmark",
          color: "#fff",
          size: "24"
        })
      } : {}, {
        d: currentStep.value >= 1 ? 1 : "",
        e: currentStep.value > 1 ? 1 : "",
        f: currentStep.value > 1 ? 1 : "",
        g: currentStep.value > 2
      }, currentStep.value > 2 ? {
        h: common_vendor.p({
          name: "checkmark",
          color: "#fff",
          size: "24"
        })
      } : {}, {
        i: currentStep.value >= 2 ? 1 : "",
        j: currentStep.value > 2 ? 1 : "",
        k: currentStep.value > 2 ? 1 : "",
        l: currentStep.value >= 3 ? 1 : "",
        m: currentStep.value === 1
      }, currentStep.value === 1 ? {
        n: ((_a = userInfo.value) == null ? void 0 : _a.photo) || "/static/images/default-avatar.png",
        o: common_vendor.t((_b = userInfo.value) == null ? void 0 : _b.realName),
        p: common_vendor.t(common_vendor.unref(src_utils_index.maskIdCard)(((_c = userInfo.value) == null ? void 0 : _c.idCard) || "")),
        q: common_vendor.t((_d = userInfo.value) == null ? void 0 : _d.organization),
        r: common_vendor.p({
          name: "info-circle",
          color: "#4A90E2",
          size: "32"
        }),
        s: common_vendor.o(confirmIdentity),
        t: common_vendor.p({
          type: "primary"
        })
      } : currentStep.value === 2 ? common_vendor.e({
        w: showCamera.value
      }, showCamera.value ? {
        x: common_vendor.o(onCameraError)
      } : {
        y: common_vendor.p({
          name: "camera",
          color: "#ccc",
          size: "120"
        })
      }, {
        z: verifyStatus.value
      }, verifyStatus.value ? {
        A: common_vendor.t(getStatusText()),
        B: common_vendor.n(verifyStatus.value)
      } : {}, {
        C: common_vendor.p({
          name: "checkmark-circle",
          color: "#4CAF50",
          size: "24"
        }),
        D: common_vendor.p({
          name: "checkmark-circle",
          color: "#4CAF50",
          size: "24"
        }),
        E: common_vendor.p({
          name: "checkmark-circle",
          color: "#4CAF50",
          size: "24"
        }),
        F: common_vendor.t(retryCount.value > 0 ? `重新识别 (${common_vendor.unref(maxRetry) - retryCount.value}/${common_vendor.unref(maxRetry)})` : "开始识别"),
        G: common_vendor.o(captureAndVerify),
        H: common_vendor.p({
          type: "primary",
          loading: isVerifying.value,
          disabled: !showCamera.value,
          loadingText: "识别中..."
        }),
        I: retryCount.value > 0
      }, retryCount.value > 0 ? {} : {}) : currentStep.value === 3 ? {
        K: common_vendor.p({
          name: "checkmark-circle-fill",
          color: "#4CAF50",
          size: "120"
        }),
        L: common_vendor.t(similarity.value),
        M: common_vendor.t(common_vendor.unref(src_utils_index.formatDate)(/* @__PURE__ */ new Date(), "HH:mm:ss")),
        N: common_vendor.o(enterExam),
        O: common_vendor.p({
          type: "primary",
          loading: isEntering.value,
          loadingText: "进入中..."
        })
      } : {}, {
        v: currentStep.value === 2,
        J: currentStep.value === 3,
        P: common_vendor.o(handleFailConfirm),
        Q: common_vendor.o(exitExam),
        R: common_vendor.o(($event) => showFailModal.value = $event),
        S: common_vendor.p({
          title: "验证失败",
          content: failMessage.value,
          showCancelButton: retryCount.value < common_vendor.unref(maxRetry),
          confirmText: retryCount.value >= common_vendor.unref(maxRetry) ? "返回" : "重试",
          cancelText: "退出考试",
          modelValue: showFailModal.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d787804b"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpages/exam/online/face-verify.js.map
