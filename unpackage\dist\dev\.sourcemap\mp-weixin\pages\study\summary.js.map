{"version": 3, "file": "summary.js", "sources": ["pages/study/summary.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc3R1ZHkvc3VtbWFyeS52dWU"], "sourcesContent": ["<template>\n  <view class=\"summary-container\">\n    <!-- 自定义导航栏 -->\n    <u-navbar \n      title=\"练习总结\" \n      :autoBack=\"true\"\n      :background=\"{ background: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)' }\"\n      titleStyle=\"color: #fff; font-weight: bold;\"\n    />\n    \n    <!-- 加载状态 -->\n    <LoadingSpinner v-if=\"isLoading\" text=\"生成总结中...\" />\n    \n    <!-- 错误状态 -->\n    <EmptyState \n      v-else-if=\"error\"\n      type=\"no-data\"\n      :title=\"error\"\n      description=\"无法加载练习总结\"\n      :showButton=\"true\"\n      buttonText=\"返回\"\n      @buttonClick=\"goBack\"\n    />\n    \n    <!-- 总结内容 -->\n    <view v-else-if=\"session\" class=\"summary-content\">\n      <!-- 成绩卡片 -->\n      <view class=\"score-card\">\n        <view class=\"score-header\">\n          <view class=\"score-icon\" :class=\"getScoreLevel(session.score || 0)\">\n            <u-icon :name=\"getScoreIcon(session.score || 0)\" color=\"#fff\" size=\"80\" />\n          </view>\n          <view class=\"score-info\">\n            <text class=\"score-number\">{{ session.score || 0 }}</text>\n            <text class=\"score-label\">分</text>\n          </view>\n        </view>\n        \n        <view class=\"score-details\">\n          <view class=\"detail-item\">\n            <text class=\"detail-label\">正确率</text>\n            <text class=\"detail-value\">{{ getAccuracy() }}%</text>\n          </view>\n          <view class=\"detail-item\">\n            <text class=\"detail-label\">答对题数</text>\n            <text class=\"detail-value\">{{ session.correctCount || 0 }}/{{ session.totalCount }}</text>\n          </view>\n          <view class=\"detail-item\">\n            <text class=\"detail-label\">用时</text>\n            <text class=\"detail-value\">{{ getDuration() }}</text>\n          </view>\n        </view>\n        \n        <view class=\"score-evaluation\">\n          <text class=\"evaluation-text\">{{ getEvaluationText() }}</text>\n        </view>\n      </view>\n      \n      <!-- 答题详情 -->\n      <view class=\"answer-details\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">答题详情</text>\n          <view class=\"filter-tabs\">\n            <view \n              v-for=\"filter in filterOptions\" \n              :key=\"filter.key\"\n              class=\"filter-tab\"\n              :class=\"{ active: currentFilter === filter.key }\"\n              @click=\"currentFilter = filter.key\"\n            >\n              <text>{{ filter.label }}</text>\n            </view>\n          </view>\n        </view>\n        \n        <view class=\"question-list\">\n          <view \n            v-for=\"(question, index) in filteredQuestions\" \n            :key=\"question.id\"\n            class=\"question-item\"\n            @click=\"viewQuestionDetail(question, index)\"\n          >\n            <view class=\"question-header\">\n              <view class=\"question-number\">\n                <text>{{ getQuestionNumber(question.id) }}</text>\n              </view>\n              <view class=\"question-result\">\n                <u-icon \n                  :name=\"isQuestionCorrect(question) ? 'checkmark-circle-fill' : 'close-circle-fill'\"\n                  :color=\"isQuestionCorrect(question) ? '#4CAF50' : '#f56c6c'\"\n                  size=\"32\"\n                />\n              </view>\n            </view>\n            \n            <view class=\"question-content\">\n              <text class=\"question-title\">{{ question.title }}</text>\n              <view class=\"question-meta\">\n                <StatusTag :type=\"'question'\" :status=\"question.type\" />\n                <text v-if=\"question.difficulty\" class=\"difficulty\">\n                  {{ getDifficultyText(question.difficulty) }}\n                </text>\n              </view>\n            </view>\n            \n            <view class=\"answer-preview\">\n              <view class=\"user-answer\">\n                <text class=\"answer-label\">我的答案：</text>\n                <text class=\"answer-value\" :class=\"{ correct: isQuestionCorrect(question), wrong: !isQuestionCorrect(question) }\">\n                  {{ formatUserAnswer(question) }}\n                </text>\n              </view>\n              <view v-if=\"!isQuestionCorrect(question)\" class=\"correct-answer\">\n                <text class=\"answer-label\">正确答案：</text>\n                <text class=\"answer-value correct\">{{ formatCorrectAnswer(question) }}</text>\n              </view>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 学习建议 -->\n      <view class=\"study-suggestions\">\n        <view class=\"section-header\">\n          <u-icon name=\"lightbulb\" color=\"#FF9500\" size=\"32\" />\n          <text class=\"section-title\">学习建议</text>\n        </view>\n        \n        <view class=\"suggestion-list\">\n          <view v-for=\"suggestion in getSuggestions()\" :key=\"suggestion.type\" class=\"suggestion-item\">\n            <view class=\"suggestion-icon\">\n              <u-icon :name=\"suggestion.icon\" :color=\"suggestion.color\" size=\"40\" />\n            </view>\n            <view class=\"suggestion-content\">\n              <text class=\"suggestion-title\">{{ suggestion.title }}</text>\n              <text class=\"suggestion-desc\">{{ suggestion.description }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部操作 -->\n    <view class=\"bottom-actions\">\n      <u-button \n        class=\"action-btn secondary\"\n        type=\"info\"\n        plain\n        @click=\"reviewWrongQuestions\"\n      >\n        错题回顾\n      </u-button>\n      \n      <u-button \n        class=\"action-btn primary\"\n        type=\"primary\"\n        @click=\"continueStudy\"\n      >\n        继续练习\n      </u-button>\n    </view>\n    \n    <!-- 题目详情弹窗 -->\n    <u-popup \n      v-model=\"showQuestionDetail\" \n      mode=\"bottom\" \n      height=\"80%\"\n      :closeOnClickOverlay=\"true\"\n    >\n      <view v-if=\"selectedQuestion\" class=\"question-detail-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">题目详情</text>\n          <u-icon name=\"close\" size=\"32\" @click=\"showQuestionDetail = false\" />\n        </view>\n        \n        <scroll-view class=\"modal-content\" scroll-y>\n          <view class=\"detail-question\">\n            <view class=\"question-info\">\n              <StatusTag :type=\"'question'\" :status=\"selectedQuestion.type\" />\n              <text class=\"question-number\">第{{ selectedQuestionIndex + 1 }}题</text>\n            </view>\n            <text class=\"question-title\">{{ selectedQuestion.title }}</text>\n          </view>\n          \n          <!-- 选择题选项 -->\n          <view v-if=\"selectedQuestion.options\" class=\"detail-options\">\n            <view \n              v-for=\"(option, index) in selectedQuestion.options\" \n              :key=\"index\"\n              class=\"detail-option\"\n              :class=\"getDetailOptionClass(selectedQuestion, index)\"\n            >\n              <text class=\"option-label\">{{ getOptionLabel(index) }}.</text>\n              <text class=\"option-text\">{{ option }}</text>\n            </view>\n          </view>\n          \n          <!-- 答案解析 -->\n          <view v-if=\"selectedQuestion.explanation\" class=\"explanation\">\n            <view class=\"explanation-header\">\n              <u-icon name=\"book\" color=\"#4A90E2\" size=\"32\" />\n              <text class=\"explanation-title\">答案解析</text>\n            </view>\n            <text class=\"explanation-content\">{{ selectedQuestion.explanation }}</text>\n          </view>\n        </scroll-view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport { useStudyStore } from '../../src/stores/study'\nimport { useAppStore } from '../../src/stores/app'\nimport { formatDuration } from '../../src/utils'\nimport { PAGE_PATHS } from '../../src/constants'\nimport type { Question, PracticeSession } from '../../src/types'\n\n// 导入组件\nimport LoadingSpinner from '../../src/components/common/LoadingSpinner.vue'\nimport EmptyState from '../../src/components/common/EmptyState.vue'\nimport StatusTag from '../../src/components/common/StatusTag.vue'\n\n// Store\nconst studyStore = useStudyStore()\nconst appStore = useAppStore()\n\n// 页面参数\nconst props = defineProps<{\n  sessionId: string\n}>()\n\n// 响应式数据\nconst isLoading = ref(true)\nconst error = ref('')\nconst session = ref<PracticeSession | null>(null)\nconst currentFilter = ref('all')\nconst showQuestionDetail = ref(false)\nconst selectedQuestion = ref<Question | null>(null)\nconst selectedQuestionIndex = ref(0)\n\n// 过滤选项\nconst filterOptions = [\n  { key: 'all', label: '全部' },\n  { key: 'correct', label: '正确' },\n  { key: 'wrong', label: '错误' }\n]\n\n// 计算属性\nconst filteredQuestions = computed(() => {\n  if (!session.value) return []\n  \n  const questions = session.value.questions\n  \n  switch (currentFilter.value) {\n    case 'correct':\n      return questions.filter(q => isQuestionCorrect(q))\n    case 'wrong':\n      return questions.filter(q => !isQuestionCorrect(q))\n    default:\n      return questions\n  }\n})\n\nonMounted(() => {\n  loadSession()\n})\n\n// 加载会话数据\nconst loadSession = () => {\n  isLoading.value = true\n  error.value = ''\n  \n  try {\n    // 从练习历史中查找会话\n    const foundSession = studyStore.practiceHistory.find(s => s.id === props.sessionId)\n    \n    if (!foundSession) {\n      error.value = '练习记录不存在'\n      return\n    }\n    \n    session.value = foundSession\n  } catch (err: any) {\n    uni.__f__('error','at pages/study/summary.vue:286','加载练习总结失败:', err)\n    error.value = '加载失败'\n  } finally {\n    isLoading.value = false\n  }\n}\n\n// 获取正确率\nconst getAccuracy = () => {\n  if (!session.value || session.value.totalCount === 0) return 0\n  return Math.round(((session.value.correctCount || 0) / session.value.totalCount) * 100)\n}\n\n// 获取用时\nconst getDuration = () => {\n  if (!session.value || !session.value.endTime) return '未知'\n  \n  const start = new Date(session.value.startTime).getTime()\n  const end = new Date(session.value.endTime).getTime()\n  const duration = Math.floor((end - start) / 1000)\n  \n  return formatDuration(duration)\n}\n\n// 获取分数等级\nconst getScoreLevel = (score: number) => {\n  if (score >= 90) return 'excellent'\n  if (score >= 80) return 'good'\n  if (score >= 60) return 'normal'\n  return 'poor'\n}\n\n// 获取分数图标\nconst getScoreIcon = (score: number) => {\n  if (score >= 90) return 'trophy'\n  if (score >= 80) return 'thumbs-up'\n  if (score >= 60) return 'checkmark'\n  return 'close'\n}\n\n// 获取评价文本\nconst getEvaluationText = () => {\n  const score = session.value?.score || 0\n  \n  if (score >= 90) return '优秀！您的表现非常出色，继续保持！'\n  if (score >= 80) return '良好！您掌握得不错，再接再厉！'\n  if (score >= 60) return '及格！还有提升空间，建议多加练习。'\n  return '需要加强！建议重点复习相关知识点。'\n}\n\n// 检查题目是否正确\nconst isQuestionCorrect = (question: Question) => {\n  if (!session.value) return false\n  \n  const userAnswer = session.value.answers[question.id]\n  return studyStore.isAnswerCorrect(question, userAnswer)\n}\n\n// 获取题目序号\nconst getQuestionNumber = (questionId: string) => {\n  if (!session.value) return 0\n  \n  const index = session.value.questions.findIndex(q => q.id === questionId)\n  return index + 1\n}\n\n// 获取难度文本\nconst getDifficultyText = (difficulty: string) => {\n  const textMap = {\n    easy: '基础',\n    medium: '进阶', \n    hard: '高级'\n  }\n  return textMap[difficulty] || '进阶'\n}\n\n// 格式化用户答案\nconst formatUserAnswer = (question: Question) => {\n  if (!session.value) return '未答'\n  \n  const answer = session.value.answers[question.id]\n  if (answer === null || answer === undefined) return '未答'\n  \n  if (question.type === 'judge') {\n    return answer ? '正确' : '错误'\n  }\n  \n  if (question.type === 'single') {\n    return question.options?.[answer] || '未知选项'\n  }\n  \n  if (question.type === 'multiple') {\n    if (!Array.isArray(answer)) return '未答'\n    return answer.map(index => question.options?.[index]).join('、') || '未知选项'\n  }\n  \n  if (question.type === 'essay') {\n    return answer.toString().substring(0, 50) + (answer.length > 50 ? '...' : '')\n  }\n  \n  return '未知'\n}\n\n// 格式化正确答案\nconst formatCorrectAnswer = (question: Question) => {\n  const answer = question.answer\n  \n  if (question.type === 'judge') {\n    return answer ? '正确' : '错误'\n  }\n  \n  if (question.type === 'single') {\n    return question.options?.[answer as number] || '未知'\n  }\n  \n  if (question.type === 'multiple') {\n    if (!Array.isArray(answer)) return '未知'\n    return answer.map(index => question.options?.[index]).join('、') || '未知'\n  }\n  \n  return answer.toString()\n}\n\n// 获取选项标签\nconst getOptionLabel = (index: number) => {\n  return String.fromCharCode(65 + index)\n}\n\n// 获取详情选项样式类\nconst getDetailOptionClass = (question: Question, index: number) => {\n  const classes = ['detail-option']\n  \n  const userAnswer = session.value?.answers[question.id]\n  const correctAnswer = question.answer\n  \n  // 用户选择的选项\n  if (question.type === 'single' && userAnswer === index) {\n    classes.push(userAnswer === correctAnswer ? 'user-correct' : 'user-wrong')\n  }\n  \n  if (question.type === 'multiple' && Array.isArray(userAnswer) && userAnswer.includes(index)) {\n    classes.push('user-selected')\n  }\n  \n  // 正确答案\n  if (question.type === 'single' && correctAnswer === index) {\n    classes.push('correct-answer')\n  }\n  \n  if (question.type === 'multiple' && Array.isArray(correctAnswer) && correctAnswer.includes(index)) {\n    classes.push('correct-answer')\n  }\n  \n  return classes.join(' ')\n}\n\n// 获取学习建议\nconst getSuggestions = () => {\n  const suggestions = []\n  const score = session.value?.score || 0\n  const accuracy = getAccuracy()\n  \n  if (score < 60) {\n    suggestions.push({\n      type: 'review',\n      icon: 'book',\n      color: '#f56c6c',\n      title: '加强基础学习',\n      description: '建议重新学习相关知识点，打好基础'\n    })\n  }\n  \n  if (accuracy < 70) {\n    suggestions.push({\n      type: 'practice',\n      icon: 'edit-pen',\n      color: '#FF9500',\n      title: '增加练习频次',\n      description: '多做练习题，提高答题准确率'\n    })\n  }\n  \n  if (score >= 80) {\n    suggestions.push({\n      type: 'advance',\n      icon: 'arrow-up',\n      color: '#4CAF50',\n      title: '挑战更高难度',\n      description: '可以尝试更高难度的题目'\n    })\n  }\n  \n  return suggestions\n}\n\n// 查看题目详情\nconst viewQuestionDetail = (question: Question, index: number) => {\n  selectedQuestion.value = question\n  selectedQuestionIndex.value = index\n  showQuestionDetail.value = true\n}\n\n// 错题回顾\nconst reviewWrongQuestions = () => {\n  currentFilter.value = 'wrong'\n  appStore.showToast('已切换到错题模式')\n}\n\n// 继续练习\nconst continueStudy = () => {\n  appStore.navigateTo(PAGE_PATHS.STUDY_CATEGORY)\n}\n\n// 返回\nconst goBack = () => {\n  appStore.navigateBack()\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../src/styles/global.scss';\n\n.summary-container {\n  min-height: 100vh;\n  background: $acdc-bg-primary;\n  padding-bottom: 120rpx;\n}\n\n.summary-content {\n  padding: 24rpx;\n}\n\n.score-card {\n  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);\n  border-radius: 24rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  color: #fff;\n  \n  .score-header {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-bottom: 40rpx;\n    \n    .score-icon {\n      width: 120rpx;\n      height: 120rpx;\n      border-radius: 60rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 40rpx;\n      \n      &.excellent {\n        background: #FFD700;\n      }\n      \n      &.good {\n        background: #4CAF50;\n      }\n      \n      &.normal {\n        background: #FF9500;\n      }\n      \n      &.poor {\n        background: #f56c6c;\n      }\n    }\n    \n    .score-info {\n      display: flex;\n      align-items: baseline;\n      \n      .score-number {\n        font-size: 80rpx;\n        font-weight: bold;\n      }\n      \n      .score-label {\n        font-size: 32rpx;\n        margin-left: 8rpx;\n      }\n    }\n  }\n  \n  .score-details {\n    display: flex;\n    justify-content: space-around;\n    margin-bottom: 32rpx;\n    \n    .detail-item {\n      text-align: center;\n      \n      .detail-label {\n        display: block;\n        font-size: 24rpx;\n        opacity: 0.8;\n        margin-bottom: 8rpx;\n      }\n      \n      .detail-value {\n        font-size: 32rpx;\n        font-weight: bold;\n      }\n    }\n  }\n  \n  .score-evaluation {\n    text-align: center;\n    padding: 24rpx;\n    background: rgba(255, 255, 255, 0.1);\n    border-radius: 16rpx;\n    \n    .evaluation-text {\n      font-size: 28rpx;\n      line-height: 1.4;\n    }\n  }\n}\n\n.answer-details,\n.study-suggestions {\n  background: #fff;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  \n  .section-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 32rpx;\n    \n    .section-title {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n      margin-left: 16rpx;\n    }\n    \n    .filter-tabs {\n      display: flex;\n      gap: 16rpx;\n      \n      .filter-tab {\n        padding: 12rpx 24rpx;\n        border-radius: 20rpx;\n        background: #f0f0f0;\n        \n        &.active {\n          background: #4A90E2;\n          color: #fff;\n        }\n        \n        text {\n          font-size: 24rpx;\n        }\n      }\n    }\n  }\n}\n\n.question-list {\n  .question-item {\n    border: 2rpx solid #f0f0f0;\n    border-radius: 16rpx;\n    padding: 32rpx;\n    margin-bottom: 24rpx;\n    \n    .question-header {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      margin-bottom: 16rpx;\n      \n      .question-number {\n        width: 60rpx;\n        height: 60rpx;\n        background: #4A90E2;\n        border-radius: 30rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        \n        text {\n          color: #fff;\n          font-size: 24rpx;\n          font-weight: bold;\n        }\n      }\n    }\n    \n    .question-content {\n      margin-bottom: 24rpx;\n      \n      .question-title {\n        font-size: 28rpx;\n        line-height: 1.5;\n        color: #333;\n        margin-bottom: 16rpx;\n      }\n      \n      .question-meta {\n        display: flex;\n        align-items: center;\n        gap: 16rpx;\n        \n        .difficulty {\n          font-size: 24rpx;\n          color: #666;\n        }\n      }\n    }\n    \n    .answer-preview {\n      .user-answer,\n      .correct-answer {\n        display: flex;\n        align-items: center;\n        margin-bottom: 8rpx;\n        \n        .answer-label {\n          font-size: 24rpx;\n          color: #666;\n          margin-right: 16rpx;\n        }\n        \n        .answer-value {\n          font-size: 26rpx;\n          \n          &.correct {\n            color: #4CAF50;\n          }\n          \n          &.wrong {\n            color: #f56c6c;\n          }\n        }\n      }\n    }\n  }\n}\n\n.suggestion-list {\n  .suggestion-item {\n    display: flex;\n    align-items: center;\n    padding: 24rpx 0;\n    border-bottom: 2rpx solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .suggestion-icon {\n      margin-right: 24rpx;\n    }\n    \n    .suggestion-content {\n      flex: 1;\n      \n      .suggestion-title {\n        display: block;\n        font-size: 28rpx;\n        font-weight: bold;\n        color: #333;\n        margin-bottom: 8rpx;\n      }\n      \n      .suggestion-desc {\n        font-size: 24rpx;\n        color: #666;\n        line-height: 1.4;\n      }\n    }\n  }\n}\n\n.bottom-actions {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #fff;\n  padding: 24rpx 30rpx;\n  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);\n  display: flex;\n  gap: 24rpx;\n  \n  .action-btn {\n    flex: 1;\n    \n    &.secondary {\n      flex: 1;\n    }\n    \n    &.primary {\n      flex: 1.5;\n    }\n  }\n}\n\n.question-detail-modal {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  \n  .modal-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 40rpx;\n    border-bottom: 2rpx solid #f0f0f0;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n    }\n  }\n  \n  .modal-content {\n    flex: 1;\n    padding: 40rpx;\n  }\n  \n  .detail-question {\n    margin-bottom: 40rpx;\n    \n    .question-info {\n      display: flex;\n      align-items: center;\n      gap: 16rpx;\n      margin-bottom: 24rpx;\n      \n      .question-number {\n        font-size: 24rpx;\n        color: #666;\n      }\n    }\n    \n    .question-title {\n      font-size: 32rpx;\n      line-height: 1.5;\n      color: #333;\n    }\n  }\n  \n  .detail-options {\n    margin-bottom: 40rpx;\n    \n    .detail-option {\n      display: flex;\n      align-items: flex-start;\n      padding: 20rpx;\n      margin-bottom: 16rpx;\n      border-radius: 12rpx;\n      border: 2rpx solid #f0f0f0;\n      \n      &.user-correct {\n        background: rgba(76, 175, 80, 0.1);\n        border-color: #4CAF50;\n      }\n      \n      &.user-wrong {\n        background: rgba(245, 108, 108, 0.1);\n        border-color: #f56c6c;\n      }\n      \n      &.correct-answer {\n        background: rgba(76, 175, 80, 0.1);\n        border-color: #4CAF50;\n      }\n      \n      .option-label {\n        font-size: 28rpx;\n        font-weight: bold;\n        color: #4A90E2;\n        margin-right: 16rpx;\n        min-width: 40rpx;\n      }\n      \n      .option-text {\n        flex: 1;\n        font-size: 28rpx;\n        line-height: 1.4;\n        color: #333;\n      }\n    }\n  }\n  \n  .explanation {\n    .explanation-header {\n      display: flex;\n      align-items: center;\n      margin-bottom: 24rpx;\n      \n      .explanation-title {\n        font-size: 28rpx;\n        font-weight: bold;\n        color: #333;\n        margin-left: 16rpx;\n      }\n    }\n    \n    .explanation-content {\n      font-size: 28rpx;\n      line-height: 1.6;\n      color: #666;\n      background: #f8f9fa;\n      padding: 24rpx;\n      border-radius: 12rpx;\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/study/summary.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useStudyStore", "useAppStore", "ref", "computed", "onMounted", "uni", "formatDuration", "_a", "PAGE_PATHS"], "mappings": ";;;;;;;;;;;;;;;;;;;;AA4NA,MAAA,iBAA2B,MAAA;AAC3B,MAAA,aAAuB,MAAA;AACvB,MAAA,YAAsB,MAAA;;;;;;;AAGtB,UAAM,aAAaA,iBAAAA;AACnB,UAAM,WAAWC,eAAAA;AAGjB,UAAM,QAAQ;AAKR,UAAA,YAAYC,kBAAI,IAAI;AACpB,UAAA,QAAQA,kBAAI,EAAE;AACd,UAAA,UAAUA,kBAA4B,IAAI;AAC1C,UAAA,gBAAgBA,kBAAI,KAAK;AACzB,UAAA,qBAAqBA,kBAAI,KAAK;AAC9B,UAAA,mBAAmBA,kBAAqB,IAAI;AAC5C,UAAA,wBAAwBA,kBAAI,CAAC;AAGnC,UAAM,gBAAgB;AAAA,MACpB,EAAE,KAAK,OAAO,OAAO,KAAK;AAAA,MAC1B,EAAE,KAAK,WAAW,OAAO,KAAK;AAAA,MAC9B,EAAE,KAAK,SAAS,OAAO,KAAK;AAAA,IAAA;AAIxB,UAAA,oBAAoBC,cAAAA,SAAS,MAAM;AACvC,UAAI,CAAC,QAAQ;AAAO,eAAO;AAErB,YAAA,YAAY,QAAQ,MAAM;AAEhC,cAAQ,cAAc,OAAO;AAAA,QAC3B,KAAK;AACH,iBAAO,UAAU,OAAO,CAAK,MAAA,kBAAkB,CAAC,CAAC;AAAA,QACnD,KAAK;AACH,iBAAO,UAAU,OAAO,CAAA,MAAK,CAAC,kBAAkB,CAAC,CAAC;AAAA,QACpD;AACS,iBAAA;AAAA,MACX;AAAA,IAAA,CACD;AAEDC,kBAAAA,UAAU,MAAM;AACF;IAAA,CACb;AAGD,UAAM,cAAc,MAAM;AACxB,gBAAU,QAAQ;AAClB,YAAM,QAAQ;AAEV,UAAA;AAEI,cAAA,eAAe,WAAW,gBAAgB,KAAK,OAAK,EAAE,OAAO,MAAM,SAAS;AAElF,YAAI,CAAC,cAAc;AACjB,gBAAM,QAAQ;AACd;AAAA,QACF;AAEA,gBAAQ,QAAQ;AAAA,eACT,KAAU;AACjBC,sBAAA,MAAI,MAAM,SAAQ,kCAAiC,aAAa,GAAG;AACnE,cAAM,QAAQ;AAAA,MAAA,UACd;AACA,kBAAU,QAAQ;AAAA,MACpB;AAAA,IAAA;AAIF,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,QAAQ,SAAS,QAAQ,MAAM,eAAe;AAAU,eAAA;AACtD,aAAA,KAAK,OAAQ,QAAQ,MAAM,gBAAgB,KAAK,QAAQ,MAAM,aAAc,GAAG;AAAA,IAAA;AAIxF,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,QAAQ,SAAS,CAAC,QAAQ,MAAM;AAAgB,eAAA;AAErD,YAAM,QAAQ,IAAI,KAAK,QAAQ,MAAM,SAAS,EAAE;AAChD,YAAM,MAAM,IAAI,KAAK,QAAQ,MAAM,OAAO,EAAE;AAC5C,YAAM,WAAW,KAAK,OAAO,MAAM,SAAS,GAAI;AAEhD,aAAOC,gBAAAA,eAAe,QAAQ;AAAA,IAAA;AAI1B,UAAA,gBAAgB,CAAC,UAAkB;AACvC,UAAI,SAAS;AAAW,eAAA;AACxB,UAAI,SAAS;AAAW,eAAA;AACxB,UAAI,SAAS;AAAW,eAAA;AACjB,aAAA;AAAA,IAAA;AAIH,UAAA,eAAe,CAAC,UAAkB;AACtC,UAAI,SAAS;AAAW,eAAA;AACxB,UAAI,SAAS;AAAW,eAAA;AACxB,UAAI,SAAS;AAAW,eAAA;AACjB,aAAA;AAAA,IAAA;AAIT,UAAM,oBAAoB,MAAM;;AACxB,YAAA,UAAQ,aAAQ,UAAR,mBAAe,UAAS;AAEtC,UAAI,SAAS;AAAW,eAAA;AACxB,UAAI,SAAS;AAAW,eAAA;AACxB,UAAI,SAAS;AAAW,eAAA;AACjB,aAAA;AAAA,IAAA;AAIH,UAAA,oBAAoB,CAAC,aAAuB;AAChD,UAAI,CAAC,QAAQ;AAAc,eAAA;AAE3B,YAAM,aAAa,QAAQ,MAAM,QAAQ,SAAS,EAAE;AAC7C,aAAA,WAAW,gBAAgB,UAAU,UAAU;AAAA,IAAA;AAIlD,UAAA,oBAAoB,CAAC,eAAuB;AAChD,UAAI,CAAC,QAAQ;AAAc,eAAA;AAErB,YAAA,QAAQ,QAAQ,MAAM,UAAU,UAAU,CAAK,MAAA,EAAE,OAAO,UAAU;AACxE,aAAO,QAAQ;AAAA,IAAA;AAIX,UAAA,oBAAoB,CAAC,eAAuB;AAChD,YAAM,UAAU;AAAA,QACd,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MAAA;AAED,aAAA,QAAQ,UAAU,KAAK;AAAA,IAAA;AAI1B,UAAA,mBAAmB,CAAC,aAAuB;;AAC/C,UAAI,CAAC,QAAQ;AAAc,eAAA;AAE3B,YAAM,SAAS,QAAQ,MAAM,QAAQ,SAAS,EAAE;AAC5C,UAAA,WAAW,QAAQ,WAAW;AAAkB,eAAA;AAEhD,UAAA,SAAS,SAAS,SAAS;AAC7B,eAAO,SAAS,OAAO;AAAA,MACzB;AAEI,UAAA,SAAS,SAAS,UAAU;AACvB,iBAAA,cAAS,YAAT,mBAAmB,YAAW;AAAA,MACvC;AAEI,UAAA,SAAS,SAAS,YAAY;AAC5B,YAAA,CAAC,MAAM,QAAQ,MAAM;AAAU,iBAAA;AAC5B,eAAA,OAAO,IAAI,CAAA,UAAS;;AAAA,kBAAAC,MAAA,SAAS,YAAT,gBAAAA,IAAmB;AAAA,SAAM,EAAE,KAAK,GAAG,KAAK;AAAA,MACrE;AAEI,UAAA,SAAS,SAAS,SAAS;AACtB,eAAA,OAAO,WAAW,UAAU,GAAG,EAAE,KAAK,OAAO,SAAS,KAAK,QAAQ;AAAA,MAC5E;AAEO,aAAA;AAAA,IAAA;AAIH,UAAA,sBAAsB,CAAC,aAAuB;;AAClD,YAAM,SAAS,SAAS;AAEpB,UAAA,SAAS,SAAS,SAAS;AAC7B,eAAO,SAAS,OAAO;AAAA,MACzB;AAEI,UAAA,SAAS,SAAS,UAAU;AACvB,iBAAA,cAAS,YAAT,mBAAmB,YAAqB;AAAA,MACjD;AAEI,UAAA,SAAS,SAAS,YAAY;AAC5B,YAAA,CAAC,MAAM,QAAQ,MAAM;AAAU,iBAAA;AAC5B,eAAA,OAAO,IAAI,CAAA,UAAS;;AAAA,kBAAAA,MAAA,SAAS,YAAT,gBAAAA,IAAmB;AAAA,SAAM,EAAE,KAAK,GAAG,KAAK;AAAA,MACrE;AAEA,aAAO,OAAO;IAAS;AAInB,UAAA,iBAAiB,CAAC,UAAkB;AACjC,aAAA,OAAO,aAAa,KAAK,KAAK;AAAA,IAAA;AAIjC,UAAA,uBAAuB,CAAC,UAAoB,UAAkB;;AAC5D,YAAA,UAAU,CAAC,eAAe;AAEhC,YAAM,cAAa,aAAQ,UAAR,mBAAe,QAAQ,SAAS;AACnD,YAAM,gBAAgB,SAAS;AAG/B,UAAI,SAAS,SAAS,YAAY,eAAe,OAAO;AACtD,gBAAQ,KAAK,eAAe,gBAAgB,iBAAiB,YAAY;AAAA,MAC3E;AAEI,UAAA,SAAS,SAAS,cAAc,MAAM,QAAQ,UAAU,KAAK,WAAW,SAAS,KAAK,GAAG;AAC3F,gBAAQ,KAAK,eAAe;AAAA,MAC9B;AAGA,UAAI,SAAS,SAAS,YAAY,kBAAkB,OAAO;AACzD,gBAAQ,KAAK,gBAAgB;AAAA,MAC/B;AAEI,UAAA,SAAS,SAAS,cAAc,MAAM,QAAQ,aAAa,KAAK,cAAc,SAAS,KAAK,GAAG;AACjG,gBAAQ,KAAK,gBAAgB;AAAA,MAC/B;AAEO,aAAA,QAAQ,KAAK,GAAG;AAAA,IAAA;AAIzB,UAAM,iBAAiB,MAAM;;AAC3B,YAAM,cAAc,CAAA;AACd,YAAA,UAAQ,aAAQ,UAAR,mBAAe,UAAS;AACtC,YAAM,WAAW;AAEjB,UAAI,QAAQ,IAAI;AACd,oBAAY,KAAK;AAAA,UACf,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,QAAA,CACd;AAAA,MACH;AAEA,UAAI,WAAW,IAAI;AACjB,oBAAY,KAAK;AAAA,UACf,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,QAAA,CACd;AAAA,MACH;AAEA,UAAI,SAAS,IAAI;AACf,oBAAY,KAAK;AAAA,UACf,MAAM;AAAA,UACN,MAAM;AAAA,UACN,OAAO;AAAA,UACP,OAAO;AAAA,UACP,aAAa;AAAA,QAAA,CACd;AAAA,MACH;AAEO,aAAA;AAAA,IAAA;AAIH,UAAA,qBAAqB,CAAC,UAAoB,UAAkB;AAChE,uBAAiB,QAAQ;AACzB,4BAAsB,QAAQ;AAC9B,yBAAmB,QAAQ;AAAA,IAAA;AAI7B,UAAM,uBAAuB,MAAM;AACjC,oBAAc,QAAQ;AACtB,eAAS,UAAU,UAAU;AAAA,IAAA;AAI/B,UAAM,gBAAgB,MAAM;AACjB,eAAA,WAAWC,+BAAW,cAAc;AAAA,IAAA;AAI/C,UAAM,SAAS,MAAM;AACnB,eAAS,aAAa;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnfxB,GAAG,WAAW,eAAe;"}