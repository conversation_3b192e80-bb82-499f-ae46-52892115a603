"use strict";
const common_vendor = require("../../../common/vendor.js");
const src_constants_index = require("../../constants/index.js");
if (!Array) {
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  _easycom_u_button2();
}
const _easycom_u_button = () => "../../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  _easycom_u_button();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "EmptyState",
  props: {
    type: { default: "no-data" },
    title: { default: "" },
    description: { default: "" },
    image: { default: "" },
    showButton: { type: Boolean, default: false },
    buttonText: { default: "重试" }
  },
  emits: ["buttonClick"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const imageUrl = common_vendor.computed(() => {
      if (props.image)
        return props.image;
      switch (props.type) {
        case "no-data":
          return src_constants_index.EMPTY_IMAGES.NO_DATA;
        case "no-network":
          return src_constants_index.EMPTY_IMAGES.NO_NETWORK;
        case "no-permission":
          return src_constants_index.EMPTY_IMAGES.NO_PERMISSION;
        default:
          return src_constants_index.EMPTY_IMAGES.NO_DATA;
      }
    });
    const title = common_vendor.computed(() => {
      if (props.title)
        return props.title;
      switch (props.type) {
        case "no-data":
          return "暂无数据";
        case "no-network":
          return "网络连接失败";
        case "no-permission":
          return "暂无权限";
        default:
          return "暂无数据";
      }
    });
    const handleButtonClick = () => {
      emit("buttonClick");
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: imageUrl.value,
        b: common_vendor.t(title.value),
        c: _ctx.description
      }, _ctx.description ? {
        d: common_vendor.t(_ctx.description)
      } : {}, {
        e: _ctx.showButton
      }, _ctx.showButton ? {
        f: common_vendor.o(handleButtonClick),
        g: common_vendor.p({
          text: _ctx.buttonText,
          type: "primary",
          size: "normal"
        })
      } : {});
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-27ab7390"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/src/components/common/EmptyState.js.map
