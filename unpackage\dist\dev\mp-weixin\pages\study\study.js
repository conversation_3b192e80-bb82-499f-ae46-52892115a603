"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const src_stores_user = require("../../src/stores/user.js");
const src_stores_app = require("../../src/stores/app.js");
const src_stores_study = require("../../src/stores/study.js");
const src_utils_permission = require("../../src/utils/permission.js");
const src_utils_index = require("../../src/utils/index.js");
const src_constants_index = require("../../src/constants/index.js");
const src_api_index = require("../../src/api/index.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_icon2 + _easycom_u_button2)();
}
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (UserStatusBanner + _easycom_u_icon + PermissionWrapper + _easycom_u_button)();
}
const UserStatusBanner = () => "../../src/components/common/UserStatusBanner.js";
const PermissionWrapper = () => "../../src/components/common/PermissionWrapper.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "study",
  setup(__props) {
    const userStore = src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const studyStore = src_stores_study.useStudyStore();
    const statusBarHeight = common_vendor.ref(0);
    const maxDailyPractice = src_constants_index.PRACTICE_CONFIG.QUESTIONS_PER_SESSION;
    const studyStats = common_vendor.computed(() => studyStore.getSessionStats());
    const recentPractices = common_vendor.computed(() => {
      return studyStore.practiceHistory.slice(0, 3);
    });
    common_vendor.onMounted(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight || 0;
      initData();
    });
    common_vendor.onShow(() => {
      if (userStore.isLoggedIn) {
        loadStudyData();
      }
    });
    const initData = () => __async(this, null, function* () {
      if (userStore.isLoggedIn) {
        yield loadStudyData();
        yield loadCategories();
      }
    });
    const loadStudyData = () => __async(this, null, function* () {
    });
    const loadCategories = () => __async(this, null, function* () {
      try {
        const response = yield src_api_index.api.study.getCategories();
        studyStore.setCategories(response.data);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/study/study.vue:236", "加载题库分类失败:", error);
      }
    });
    const getQuestionBankInfo = () => {
      if (!userStore.isLoggedIn) {
        return "登录后可免费练习";
      }
      if (userStore.isAuthenticated) {
        return "认证用户可无限练习";
      }
      if (!studyStore.canPracticeToday) {
        return "免费练习已用完，明日可继续";
      }
      return `免费练习 ${studyStore.remainingPracticeCount} 组剩余`;
    };
    const getCategoryName = (categoryId) => {
      const category = studyStore.categories.find((c) => c.id === categoryId);
      return (category == null ? void 0 : category.name) || "未知分类";
    };
    const getScoreClass = (score) => {
      if (score >= 80)
        return "good";
      if (score >= 60)
        return "normal";
      return "poor";
    };
    const handleTextbookClick = () => {
      appStore.showToast("教材功能正在建设中，敬请期待");
    };
    const handleQuestionBankClick = () => {
      if (!src_utils_permission.permissionManager.enforceFeatureAccess("practice")) {
        return;
      }
      if (!userStore.isAuthenticated && !studyStore.canPracticeToday) {
        appStore.showModal({
          title: "今日练习已达上限",
          content: "免费用户每天可练习3组题目，明天可继续练习。完善个人资料并通过审核后可享受无限练习特权。",
          confirmText: "完善资料",
          cancelText: "我知道了"
        }).then((confirmed) => {
          if (confirmed) {
            appStore.switchTab(src_constants_index.PAGE_PATHS.PERSONAL);
          }
        });
        return;
      }
      appStore.navigateTo(src_constants_index.PAGE_PATHS.STUDY_CATEGORY);
    };
    const goToLogin = () => {
      appStore.redirectTo(src_constants_index.PAGE_PATHS.LOGIN);
    };
    const handleVipClick = () => {
      appStore.showModal({
        title: "VIP功能即将上线",
        content: "VIP会员功能正在开发中，上线后将为您提供更丰富的学习功能和特权。",
        confirmText: "期待上线",
        showCancel: false
      });
    };
    const viewAllPractices = () => {
      appStore.showToast("练习历史功能开发中");
    };
    const viewPracticeDetail = (practice) => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.STUDY_SUMMARY, { sessionId: practice.id });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + "px",
        b: common_vendor.t(common_vendor.unref(studyStore).dailyPracticeCount),
        c: common_vendor.t(common_vendor.unref(maxDailyPractice)),
        d: common_vendor.p({
          showAction: true
        }),
        e: common_vendor.t(studyStats.value.totalQuestions),
        f: common_vendor.t(studyStats.value.accuracy),
        g: common_vendor.t(studyStats.value.totalSessions),
        h: common_vendor.p({
          name: "book",
          color: "#4A90E2",
          size: "80"
        }),
        i: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "32"
        }),
        j: common_vendor.o(handleTextbookClick),
        k: common_vendor.p({
          name: "edit-pen",
          color: "#FF9500",
          size: "80"
        }),
        l: !common_vendor.unref(studyStore).canPracticeToday
      }, !common_vendor.unref(studyStore).canPracticeToday ? {} : {}, {
        m: common_vendor.t(getQuestionBankInfo()),
        n: common_vendor.p({
          name: "arrow-right",
          color: "#4A90E2",
          size: "32"
        }),
        o: common_vendor.o(handleQuestionBankClick),
        p: common_vendor.p({
          permission: "auth",
          showFallback: false
        }),
        q: common_vendor.p({
          name: "account",
          color: "#2E8B57",
          size: "80"
        }),
        r: common_vendor.p({
          name: "arrow-right",
          color: "#2E8B57",
          size: "32"
        }),
        s: common_vendor.o(goToLogin),
        t: common_vendor.p({
          permission: "auth",
          showFallback: true
        }),
        v: common_vendor.unref(userStore).isLoggedIn
      }, common_vendor.unref(userStore).isLoggedIn ? {
        w: common_vendor.p({
          name: "diamond",
          color: "#FFD700",
          size: "60"
        }),
        x: common_vendor.p({
          name: "checkmark-circle",
          color: "#4CAF50",
          size: "32"
        }),
        y: common_vendor.p({
          name: "checkmark-circle",
          color: "#4CAF50",
          size: "32"
        }),
        z: common_vendor.p({
          name: "checkmark-circle",
          color: "#4CAF50",
          size: "32"
        }),
        A: common_vendor.o(handleVipClick),
        B: common_vendor.p({
          type: "warning"
        })
      } : {}, {
        C: common_vendor.unref(userStore).isLoggedIn && common_vendor.unref(studyStore).practiceHistory.length > 0
      }, common_vendor.unref(userStore).isLoggedIn && common_vendor.unref(studyStore).practiceHistory.length > 0 ? {
        D: common_vendor.o(viewAllPractices),
        E: common_vendor.f(recentPractices.value, (practice, k0, i0) => {
          return {
            a: common_vendor.t(getCategoryName(practice.categoryId)),
            b: common_vendor.t(common_vendor.unref(src_utils_index.formatRelativeTime)(practice.endTime || practice.startTime)),
            c: common_vendor.t(practice.score || 0),
            d: common_vendor.n(getScoreClass(practice.score || 0)),
            e: common_vendor.t(practice.correctCount || 0),
            f: common_vendor.t(practice.totalCount),
            g: practice.id,
            h: common_vendor.o(($event) => viewPracticeDetail(practice), practice.id)
          };
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3f273c1e"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/study/study.js.map
