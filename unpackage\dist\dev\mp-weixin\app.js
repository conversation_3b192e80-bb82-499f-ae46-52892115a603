"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const common_vendor = require("./common/vendor.js");
const src_stores_user = require("./src/stores/user.js");
const src_stores_app = require("./src/stores/app.js");
const src_stores_study = require("./src/stores/study.js");
const src_utils_routerGuard = require("./src/utils/router-guard.js");
const uni_modules_uviewPlus_index = require("./uni_modules/uview-plus/index.js");
if (!Math) {
  "./pages/login/login.js";
  "./pages/profile/profile.js";
  "./pages/info/info.js";
  "./pages/info/detail.js";
  "./pages/study/study.js";
  "./pages/study/category.js";
  "./pages/study/practice.js";
  "./pages/study/summary.js";
  "./pages/exam/exam.js";
  "./pages/personal/personal.js";
  "./subpages/exam/online/reading.js";
  "./subpages/exam/online/face-verify.js";
  "./subpages/exam/online/answer.js";
  "./subpages/exam/offline/detail.js";
  "./subpages/exam/history/history.js";
  "./subpages/personal/info/info.js";
  "./subpages/personal/certificate/certificate.js";
  "./subpages/personal/feedback/feedback.js";
  "./subpages/personal/about/about.js";
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "App",
  setup(__props) {
    const userStore = src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const studyStore = src_stores_study.useStudyStore();
    common_vendor.onLaunch(() => {
      common_vendor.index.__f__("log", "at App.vue:14", "疾控医护考试系统启动");
      initApp();
    });
    common_vendor.onShow(() => {
      common_vendor.index.__f__("log", "at App.vue:21", "App Show");
      appStore.checkNetworkStatus();
    });
    common_vendor.onHide(() => {
      common_vendor.index.__f__("log", "at App.vue:28", "App Hide");
    });
    const initApp = () => __async(this, null, function* () {
      try {
        yield appStore.initApp();
        userStore.initFromStorage();
        studyStore.initStudyStore();
        src_utils_routerGuard.initRouterGuard();
        common_vendor.index.__f__("log", "at App.vue:46", "应用初始化完成");
      } catch (error) {
        common_vendor.index.__f__("error", "at App.vue:48", "应用初始化失败:", error);
      }
    });
    return () => {
    };
  }
});
function createApp() {
  const app = common_vendor.createSSRApp(_sfc_main);
  const pinia = common_vendor.createPinia();
  app.use(pinia);
  app.use(uni_modules_uviewPlus_index.uviewPlus);
  app.config.globalProperties.$appName = "疾控医护考试系统";
  app.config.globalProperties.$version = "1.0.0";
  return {
    app,
    Pinia: common_vendor.createPinia
  };
}
createApp().app.mount("#app");
exports.createApp = createApp;
//# sourceMappingURL=../.sourcemap/mp-weixin/app.js.map
