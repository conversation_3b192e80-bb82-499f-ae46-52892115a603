{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-icon/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 图标类名\n        name: {\n            type: String,\n            default: () => defProps.icon.name\n        },\n        // 图标颜色，可接受主题色\n        color: {\n            type: String,\n            default: () => defProps.icon.color\n        },\n        // 字体大小，单位px\n        size: {\n            type: [String, Number],\n            default: () => defProps.icon.size\n        },\n        // 是否显示粗体\n        bold: {\n            type: Boolean,\n            default: () => defProps.icon.bold\n        },\n        // 点击图标的时候传递事件出去的index（用于区分点击了哪一个）\n        index: {\n            type: [String, Number],\n            default: () => defProps.icon.index\n        },\n        // 触摸图标时的类名\n        hoverClass: {\n            type: String,\n            default: () => defProps.icon.hoverClass\n        },\n        // 自定义扩展前缀，方便用户扩展自己的图标库\n        customPrefix: {\n            type: String,\n            default: () => defProps.icon.customPrefix\n        },\n        // 图标右边或者下面的文字\n        label: {\n            type: [String, Number],\n            default: () => defProps.icon.label\n        },\n        // label的位置，只能右边或者下边\n        labelPos: {\n            type: String,\n            default: () => defProps.icon.labelPos\n        },\n        // label的大小\n        labelSize: {\n            type: [String, Number],\n            default: () => defProps.icon.labelSize\n        },\n        // label的颜色\n        labelColor: {\n            type: String,\n            default: () => defProps.icon.labelColor\n        },\n        // label与图标的距离\n        space: {\n            type: [String, Number],\n            default: () => defProps.icon.space\n        },\n        // 图片的mode\n        imgMode: {\n            type: String,\n            default: () => defProps.icon.imgMode\n        },\n        // 用于显示图片小图标时，图片的宽度\n        width: {\n            type: [String, Number],\n            default: () => defProps.icon.width\n        },\n        // 用于显示图片小图标时，图片的高度\n        height: {\n            type: [String, Number],\n            default: () => defProps.icon.height\n        },\n        // 用于解决某些情况下，让图标垂直居中的用途\n        top: {\n            type: [String, Number],\n            default: () => defProps.icon.top\n        },\n        // 是否阻止事件传播\n        stop: {\n            type: Boolean,\n            default: () => defProps.icon.stop\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,KAAK;AAAA,MACD,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA,EACJ;AACL,CAAC;;"}