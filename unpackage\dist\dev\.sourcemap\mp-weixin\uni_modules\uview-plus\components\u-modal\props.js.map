{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-modal/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 是否展示modal\n        show: {\n            type: Boolean,\n            default: () => defProps.modal.show\n        },\n        // 标题\n        title: {\n            type: [String],\n            default: () => defProps.modal.title\n        },\n        // 弹窗内容\n        content: {\n            type: String,\n            default: () => defProps.modal.content\n        },\n        // 确认文案\n        confirmText: {\n            type: String,\n            default: () => defProps.modal.confirmText\n        },\n        // 取消文案\n        cancelText: {\n            type: String,\n            default: () => defProps.modal.cancelText\n        },\n        // 是否显示确认按钮\n        showConfirmButton: {\n            type: Boolean,\n            default: () => defProps.modal.showConfirmButton\n        },\n        // 是否显示取消按钮\n        showCancelButton: {\n            type: Boolean,\n            default: () => defProps.modal.showCancelButton\n        },\n        // 确认按钮颜色\n        confirmColor: {\n            type: String,\n            default: () => defProps.modal.confirmColor\n        },\n        // 取消文字颜色\n        cancelColor: {\n            type: String,\n            default: () => defProps.modal.cancelColor\n        },\n        // 对调确认和取消的位置\n        buttonReverse: {\n            type: Boolean,\n            default: () => defProps.modal.buttonReverse\n        },\n        // 是否开启缩放效果\n        zoom: {\n            type: Boolean,\n            default: () => defProps.modal.zoom\n        },\n        // 是否异步关闭，只对确定按钮有效\n        asyncClose: {\n            type: Boolean,\n            default: () => defProps.modal.asyncClose\n        },\n        // 是否允许点击遮罩关闭modal\n        closeOnClickOverlay: {\n            type: Boolean,\n            default: () => defProps.modal.closeOnClickOverlay\n        },\n        // 给一个负的margin-top，往上偏移，避免和键盘重合的情况\n        negativeTop: {\n            type: [String, Number],\n            default: () => defProps.modal.negativeTop\n        },\n        // modal宽度，不支持百分比，可以数值，px，rpx单位\n        width: {\n            type: [String, Number],\n            default: () => defProps.modal.width\n        },\n        // 确认按钮的样式，circle-圆形，square-方形，如设置，将不会显示取消按钮\n        confirmButtonShape: {\n            type: String,\n            default: () => defProps.modal.confirmButtonShape\n        },\n        // 弹窗动画过度时间\n        duration: {\n            type: [Number],\n            default: defProps.modal.duration\n        },\n        // 文案对齐方式\n        contentTextAlign: {\n            type: String,\n            default: () => defProps.modal.contentTextAlign\n        },\n        // 异步确定时如果点击了取消时候的提示文案\n        asyncCloseTip: {\n            type: String,\n            default: () => defProps.modal.asyncCloseTip\n        },\n        // 是否异步关闭，只对取消按钮有效\n        asyncCancelClose: {\n            type: Boolean,\n            default: () => defProps.modal.asyncCancelClose\n        },\n        // 内容样式\n        contentStyle: {\n            type: Object,\n            default: () => defProps.modal.contentStyle\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,MAAM;AAAA,MACb,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,mBAAmB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,qBAAqB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,oBAAoB;AAAA,MAChB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,MACb,SAASA,wCAAAA,MAAS,MAAM;AAAA,IAC3B;AAAA;AAAA,IAED,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IACjC;AAAA,EACJ;AACL,CAAC;;"}