{"version": 3, "file": "u-status-bar.js", "sources": ["uni_modules/uview-plus/components/u-status-bar/u-status-bar.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovcHJvamVjdC9BQ0RDZXhhbS91bmlfbW9kdWxlcy91dmlldy1wbHVzL2NvbXBvbmVudHMvdS1zdGF0dXMtYmFyL3Utc3RhdHVzLWJhci52dWU"], "sourcesContent": ["<template>\n\t<view\n\t    :style=\"[style]\"\n\t    class=\"u-status-bar\"\n\t\t:class=\"[isH5 && 'u-safe-area-inset-top']\"\n\t>\n\t\t<slot />\n\t</view>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addUnit, addStyle, deepMerge, getWindowInfo } from '../../libs/function/index';\n\t/**\n\t * StatbusBar 状态栏占位\n\t * @description 本组件主要用于状态填充，比如在自定导航栏的时候，它会自动适配一个恰当的状态栏高度。\n\t * @tutorial https://uview-plus.jiangruyi.com/components/statusBar.html\n\t * @property {String}\t\t\tbgColor\t\t\t背景色 (默认 'transparent' )\n\t * @property {String | Object}\tcustomStyle\t\t自定义样式 \n\t * @example <u-status-bar></u-status-bar>\n\t */\n\texport default {\n\t\tname: 'u-status-bar',\n\t\tmixins: [mpMixin, mixin, props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisH5: false\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\t// #ifdef H5\n\t\t\tthis.isH5 = true\n\t\t\t// #endif\n\t\t},\n\t\temits: ['update:height'],\n\t\tcomputed: {\n\t\t\tstyle() {\n\t\t\t\tconst style = {}\n\t\t\t\t// 状态栏高度，由于某些安卓和微信开发工具无法识别css的顶部状态栏变量，所以使用js获取的方式\n\t\t\t\tlet sheight = getWindowInfo().statusBarHeight\n\t\t\t\tthis.$emit('update:height', sheight)\n\t\t\t\tif (sheight == 0) {\n\t\t\t\t\tthis.isH5 = true\n\t\t\t\t} else {\n\t\t\t\t\tstyle.height = addUnit(sheight, 'px')\n\t\t\t\t}\n\t\t\t\tstyle.backgroundColor = this.bgColor\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\n\t\t\t}\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t.u-status-bar {\n\t\t// nvue会默认100%，如果nvue下，显式写100%的话，会导致宽度不为100%而异常\n\t\t/* #ifndef APP-NVUE */\n\t\twidth: 100%;\n\t\t/* #endif */\n\t}\n</style>\n", "import Component from 'E:/project/ACDCexam/uni_modules/uview-plus/components/u-status-bar/u-status-bar.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "getWindowInfo", "addUnit", "deepMerge", "addStyle"], "mappings": ";;;;;;AAuBC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAAEC,uDAAK;AAAA,EAC9B,OAAO;AACN,WAAO;AAAA,MACN,MAAM;AAAA,IACP;AAAA,EACA;AAAA,EACD,UAAU;AAAA,EAIT;AAAA,EACD,OAAO,CAAC,eAAe;AAAA,EACvB,UAAU;AAAA,IACT,QAAQ;AACP,YAAM,QAAQ,CAAC;AAEf,UAAI,UAAUC,0CAAa,cAAA,EAAG;AAC9B,WAAK,MAAM,iBAAiB,OAAO;AACnC,UAAI,WAAW,GAAG;AACjB,aAAK,OAAO;AAAA,aACN;AACN,cAAM,SAASC,kDAAQ,SAAS,IAAI;AAAA,MACrC;AACA,YAAM,kBAAkB,KAAK;AAC7B,aAAOC,0CAAS,UAAC,OAAOC,0CAAQ,SAAC,KAAK,WAAW,CAAC;AAAA,IACnD;AAAA,EACA;AACF;;;;;;;;ACnDD,GAAG,gBAAgB,SAAS;"}