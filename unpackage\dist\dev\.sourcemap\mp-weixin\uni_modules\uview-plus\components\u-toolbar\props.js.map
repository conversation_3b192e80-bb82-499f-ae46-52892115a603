{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-toolbar/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 是否展示工具条\n        show: {\n            type: Boolean,\n            default: () => defProps.toolbar.show\n        },\n        // 取消按钮的文字\n        cancelText: {\n            type: String,\n            default: () => defProps.toolbar.cancelText\n        },\n        // 确认按钮的文字\n        confirmText: {\n            type: String,\n            default: () => defProps.toolbar.confirmText\n        },\n        // 取消按钮的颜色\n        cancelColor: {\n            type: String,\n            default: () => defProps.toolbar.cancelColor\n        },\n        // 确认按钮的颜色\n        confirmColor: {\n            type: String,\n            default: () => defProps.toolbar.confirmColor\n        },\n        // 标题文字\n        title: {\n            type: String,\n            default: () => defProps.toolbar.title\n        },\n        // 开启右侧插槽\n        rightSlot: {\n            type: Boolean,\n            default: false\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,QAAQ;AAAA,IACnC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS;AAAA,IACZ;AAAA,EACJ;AACL,CAAC;;"}