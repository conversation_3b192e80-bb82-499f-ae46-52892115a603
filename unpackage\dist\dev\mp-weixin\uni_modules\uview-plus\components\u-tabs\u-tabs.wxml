<view class="{{['u-tabs', 'data-v-02b0c54f', j]}}"><view class="u-tabs__wrapper data-v-02b0c54f"><slot name="left"/><view class="u-tabs__wrapper__scroll-view-wrapper data-v-02b0c54f"><scroll-view scroll-x="{{h}}" scroll-left="{{i}}" scroll-with-animation class="u-tabs__wrapper__scroll-view data-v-02b0c54f" show-scrollbar="{{false}}" ref="u-tabs__wrapper__scroll-view"><view class="u-tabs__wrapper__nav data-v-02b0c54f" ref="u-tabs__wrapper__nav"><view wx:for="{{a}}" wx:for-item="item" wx:key="o" bindtap="{{item.p}}" bindlongpress="{{item.q}}" ref="{{item.r}}" style="{{e + ';' + f}}" class="{{['u-tabs__wrapper__nav__item', 'data-v-02b0c54f', item.s, item.t, item.v]}}"><slot wx:if="{{b}}" name="{{item.a}}"/><block wx:else><view wx:if="{{item.c}}" class="u-tabs__wrapper__nav__item__prefix-icon data-v-02b0c54f"><up-icon wx:if="{{item.e}}" class="data-v-02b0c54f" u-i="{{item.d}}" bind:__l="__l" u-p="{{item.e}}"></up-icon></view></block><slot wx:if="{{c}}" name="{{item.f}}"/><slot wx:elif="{{d}}" name="{{item.h}}"/><text wx:else class="{{[item.k, 'u-tabs__wrapper__nav__item__text', 'data-v-02b0c54f']}}" style="{{item.l}}">{{item.j}}</text><u-badge wx:if="{{item.n}}" class="data-v-02b0c54f" u-i="{{item.m}}" bind:__l="__l" u-p="{{item.n}}"></u-badge></view><view class="u-tabs__wrapper__nav__line data-v-02b0c54f" ref="u-tabs__wrapper__nav__line" style="{{g}}"></view></view></scroll-view></view><slot name="right"/></view></view>