{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-checkbox/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // checkbox的名称\n        name: {\n            type: [String, Number, Boolean],\n            default: () => defProps.checkbox.name\n        },\n        // 形状，square为方形，circle为圆型\n        shape: {\n            type: String,\n            default: () => defProps.checkbox.shape\n        },\n        // 整体的大小\n        size: {\n            type: [String, Number],\n            default: () => defProps.checkbox.size\n        },\n        // 是否默认选中\n        checked: {\n            type: Boolean,\n            default: () => defProps.checkbox.checked\n        },\n        // 是否禁用\n        disabled: {\n            type: [String, Boolean],\n            default: () => defProps.checkbox.disabled\n        },\n        // 选中状态下的颜色，如设置此值，将会覆盖parent的activeColor值\n        activeColor: {\n            type: String,\n            default: () => defProps.checkbox.activeColor\n        },\n        // 未选中的颜色\n        inactiveColor: {\n            type: String,\n            default: () => defProps.checkbox.inactiveColor\n        },\n        // 图标的大小，单位px\n        iconSize: {\n            type: [String, Number],\n            default: () => defProps.checkbox.iconSize\n        },\n        // 图标颜色\n        iconColor: {\n            type: String,\n            default: () => defProps.checkbox.iconColor\n        },\n        // label提示文字，因为nvue下，直接slot进来的文字，由于特殊的结构，无法修改样式\n        label: {\n            type: [String, Number],\n            default: () => defProps.checkbox.label\n        },\n        // label的字体大小，px单位\n        labelSize: {\n            type: [String, Number],\n            default: () => defProps.checkbox.labelSize\n        },\n        // label的颜色\n        labelColor: {\n            type: String,\n            default: () => defProps.checkbox.labelColor\n        },\n        // 是否禁止点击提示语选中复选框\n        labelDisabled: {\n            type: [String, Boolean],\n            default: () => defProps.checkbox.labelDisabled\n        },\n\t\t// 是否独立使用\n        usedAlone: {\n            type: [Boolean],\n            default: () => false\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,QAAQ,OAAO;AAAA,MAC9B,SAAS,MAAMC,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,OAAO;AAAA,MACtB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM,CAAC,QAAQ,OAAO;AAAA,MACtB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM,CAAC,OAAO;AAAA,MACd,SAAS,MAAM;AAAA,IAClB;AAAA,EACJ;AACL,CAAC;;"}