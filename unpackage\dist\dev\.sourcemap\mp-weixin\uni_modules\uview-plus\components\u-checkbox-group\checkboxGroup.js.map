{"version": 3, "file": "checkboxGroup.js", "sources": ["uni_modules/uview-plus/components/u-checkbox-group/checkboxGroup.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:54:47\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/checkboxGroup.js\n */\nexport default {\n    // checkbox-group组件\n    checkboxGroup: {\n        name: '',\n        value: [],\n        shape: 'square',\n        disabled: false,\n        activeColor: '#2979ff',\n        inactiveColor: '#c8c9cc',\n        size: 18,\n        placement: 'row',\n        labelSize: 14,\n        labelColor: '#303133',\n        labelDisabled: false,\n        iconColor: '#ffffff',\n        iconSize: 12,\n        iconPlacement: 'left',\n        borderBottom: false\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,gBAAA;AAAA;AAAA,EAEX,eAAe;AAAA,IACX,MAAM;AAAA,IACN,OAAO,CAAE;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,aAAa;AAAA,IACb,eAAe;AAAA,IACf,MAAM;AAAA,IACN,WAAW;AAAA,IACX,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,WAAW;AAAA,IACX,UAAU;AAAA,IACV,eAAe;AAAA,IACf,cAAc;AAAA,EACjB;AACL;;"}