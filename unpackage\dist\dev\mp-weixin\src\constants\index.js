"use strict";
const API_CONFIG = {
  BASE_URL: "https://api.acdc-exam.com",
  TIMEOUT: 1e4,
  RETRY_COUNT: 3
};
const STORAGE_KEYS = {
  TOKEN: "acdc_token",
  USER_INFO: "acdc_user_info",
  PRACTICE_CACHE: "acdc_practice_cache",
  EXAM_CACHE: "acdc_exam_cache",
  SETTINGS: "acdc_settings"
};
const USER_STATUS_TEXT = {
  not_submitted: "未提交资料",
  pending: "审核中",
  approved: "已认证",
  rejected: "审核未通过"
};
const EXAM_STATUS_TEXT = {
  not_started: "未开始",
  in_progress: "进行中",
  completed: "已完成",
  expired: "已过期"
};
const EXAM_RECORD_STATUS_TEXT = {
  not_started: "未开始",
  in_progress: "答题中",
  submitted: "已提交",
  passed: "已通过",
  failed: "未通过"
};
const CERTIFICATE_STATUS_TEXT = {
  pending: "审批中",
  active: "有效",
  expired: "已过期",
  revoked: "已撤销"
};
const UPLOAD_CONFIG = {
  MAX_SIZE: 200 * 1024,
  // 200KB
  ALLOWED_TYPES: ["image/jpeg", "image/png", "image/jpg"],
  ALLOWED_EXTENSIONS: [".jpg", ".jpeg", ".png"]
};
const PRACTICE_CONFIG = {
  QUESTIONS_PER_SESSION: 10,
  // 每组题目数量
  FREE_SESSIONS_PER_DAY: 3,
  // 免费用户每日练习次数
  SESSION_TIMEOUT: 30 * 60 * 1e3
  // 练习超时时间（毫秒）
};
const EXAM_CONFIG = {
  FACE_VERIFY_MAX_RETRY: 3,
  // 人脸识别最大重试次数
  AUTO_SUBMIT_BEFORE_END: 30,
  // 考试结束前自动提交时间（秒）
  ANTI_CHEAT_CHECK_INTERVAL: 1e4,
  // 防作弊检查间隔（毫秒）
  MAX_SWITCH_COUNT: 5
  // 最大切屏次数
};
const PAGE_PATHS = {
  LOGIN: "/pages/login/login",
  PROFILE: "/pages/profile/profile",
  INFO: "/pages/info/info",
  INFO_DETAIL: "/pages/info/detail",
  STUDY: "/pages/study/study",
  STUDY_CATEGORY: "/pages/study/category",
  STUDY_PRACTICE: "/pages/study/practice",
  STUDY_SUMMARY: "/pages/study/summary",
  EXAM: "/pages/exam/exam",
  PERSONAL: "/pages/personal/personal",
  // 分包页面
  EXAM_ONLINE_READING: "/subpages/exam/online/reading",
  EXAM_ONLINE_FACE_VERIFY: "/subpages/exam/online/face-verify",
  EXAM_ONLINE_ANSWER: "/subpages/exam/online/answer",
  EXAM_OFFLINE_DETAIL: "/subpages/exam/offline/detail",
  EXAM_HISTORY: "/subpages/exam/history/history",
  PERSONAL_INFO: "/subpages/personal/info/info",
  PERSONAL_CERTIFICATE: "/subpages/personal/certificate/certificate",
  PERSONAL_FEEDBACK: "/subpages/personal/feedback/feedback",
  PERSONAL_ABOUT: "/subpages/personal/about/about"
};
const ERROR_CODES = {
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  SERVER_ERROR: 500,
  NETWORK_ERROR: -1,
  TIMEOUT: -2
};
const ERROR_MESSAGES = {
  [ERROR_CODES.UNAUTHORIZED]: "登录已过期，请重新登录",
  [ERROR_CODES.FORBIDDEN]: "没有权限访问",
  [ERROR_CODES.NOT_FOUND]: "请求的资源不存在",
  [ERROR_CODES.SERVER_ERROR]: "服务器错误，请稍后重试",
  [ERROR_CODES.NETWORK_ERROR]: "网络连接失败，请检查网络",
  [ERROR_CODES.TIMEOUT]: "请求超时，请重试"
};
const REGEX = {
  PHONE: /^1[3-9]\d{9}$/,
  // 手机号
  ID_CARD: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
  // 身份证号
  EMAIL: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
  // 邮箱
};
const EMPTY_IMAGES = {
  NO_DATA: "/static/images/empty-data.png",
  NO_NETWORK: "/static/images/empty-network.png",
  NO_PERMISSION: "/static/images/empty-permission.png"
};
exports.API_CONFIG = API_CONFIG;
exports.CERTIFICATE_STATUS_TEXT = CERTIFICATE_STATUS_TEXT;
exports.EMPTY_IMAGES = EMPTY_IMAGES;
exports.ERROR_MESSAGES = ERROR_MESSAGES;
exports.EXAM_CONFIG = EXAM_CONFIG;
exports.EXAM_RECORD_STATUS_TEXT = EXAM_RECORD_STATUS_TEXT;
exports.EXAM_STATUS_TEXT = EXAM_STATUS_TEXT;
exports.PAGE_PATHS = PAGE_PATHS;
exports.PRACTICE_CONFIG = PRACTICE_CONFIG;
exports.REGEX = REGEX;
exports.STORAGE_KEYS = STORAGE_KEYS;
exports.UPLOAD_CONFIG = UPLOAD_CONFIG;
exports.USER_STATUS_TEXT = USER_STATUS_TEXT;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/src/constants/index.js.map
