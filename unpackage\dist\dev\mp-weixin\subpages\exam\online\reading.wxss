/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件
 * 疾控医护考试系统
 */
/* 全局重置样式 */
*.data-v-ea8408ec {
  box-sizing: border-box;
}
page.data-v-ea8408ec {
  background-color: #F8F9FA;
  color: #262626;
  font-size: 28rpx;
  line-height: 1.6;
}
/* 通用布局类 */
.container.data-v-ea8408ec {
  padding: 24rpx;
  min-height: 100vh;
}
.page-container.data-v-ea8408ec {
  padding: 32rpx 24rpx;
  background-color: #F8F9FA;
  min-height: 100vh;
}
.content-container.data-v-ea8408ec {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
}
/* Flex布局 */
.flex.data-v-ea8408ec {
  display: flex;
}
.flex-column.data-v-ea8408ec {
  display: flex;
  flex-direction: column;
}
.flex-center.data-v-ea8408ec {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between.data-v-ea8408ec {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.flex-around.data-v-ea8408ec {
  display: flex;
  align-items: center;
  justify-content: space-around;
}
.flex-start.data-v-ea8408ec {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.flex-end.data-v-ea8408ec {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.flex-1.data-v-ea8408ec {
  flex: 1;
}
.flex-wrap.data-v-ea8408ec {
  flex-wrap: wrap;
}
/* 文字样式 */
.text-primary.data-v-ea8408ec {
  color: #262626;
}
.text-secondary.data-v-ea8408ec {
  color: #595959;
}
.text-disabled.data-v-ea8408ec {
  color: #BFBFBF;
}
.text-success.data-v-ea8408ec {
  color: #52C41A;
}
.text-warning.data-v-ea8408ec {
  color: #FAAD14;
}
.text-error.data-v-ea8408ec {
  color: #F5222D;
}
.text-primary-color.data-v-ea8408ec {
  color: #2E8B57;
}
.text-center.data-v-ea8408ec {
  text-align: center;
}
.text-left.data-v-ea8408ec {
  text-align: left;
}
.text-right.data-v-ea8408ec {
  text-align: right;
}
.text-bold.data-v-ea8408ec {
  font-weight: bold;
}
.text-normal.data-v-ea8408ec {
  font-weight: normal;
}
/* 字体大小 */
.text-xs.data-v-ea8408ec {
  font-size: 20rpx;
}
.text-sm.data-v-ea8408ec {
  font-size: 24rpx;
}
.text-base.data-v-ea8408ec {
  font-size: 28rpx;
}
.text-lg.data-v-ea8408ec {
  font-size: 32rpx;
}
.text-xl.data-v-ea8408ec {
  font-size: 36rpx;
}
.text-2xl.data-v-ea8408ec {
  font-size: 40rpx;
}
.text-3xl.data-v-ea8408ec {
  font-size: 48rpx;
}
/* 间距 */
.m-0.data-v-ea8408ec {
  margin: 0;
}
.m-1.data-v-ea8408ec {
  margin: 8rpx;
}
.m-2.data-v-ea8408ec {
  margin: 16rpx;
}
.m-3.data-v-ea8408ec {
  margin: 24rpx;
}
.m-4.data-v-ea8408ec {
  margin: 32rpx;
}
.m-5.data-v-ea8408ec {
  margin: 40rpx;
}
.mt-0.data-v-ea8408ec {
  margin-top: 0;
}
.mt-1.data-v-ea8408ec {
  margin-top: 8rpx;
}
.mt-2.data-v-ea8408ec {
  margin-top: 16rpx;
}
.mt-3.data-v-ea8408ec {
  margin-top: 24rpx;
}
.mt-4.data-v-ea8408ec {
  margin-top: 32rpx;
}
.mt-5.data-v-ea8408ec {
  margin-top: 40rpx;
}
.mb-0.data-v-ea8408ec {
  margin-bottom: 0;
}
.mb-1.data-v-ea8408ec {
  margin-bottom: 8rpx;
}
.mb-2.data-v-ea8408ec {
  margin-bottom: 16rpx;
}
.mb-3.data-v-ea8408ec {
  margin-bottom: 24rpx;
}
.mb-4.data-v-ea8408ec {
  margin-bottom: 32rpx;
}
.mb-5.data-v-ea8408ec {
  margin-bottom: 40rpx;
}
.ml-0.data-v-ea8408ec {
  margin-left: 0;
}
.ml-1.data-v-ea8408ec {
  margin-left: 8rpx;
}
.ml-2.data-v-ea8408ec {
  margin-left: 16rpx;
}
.ml-3.data-v-ea8408ec {
  margin-left: 24rpx;
}
.ml-4.data-v-ea8408ec {
  margin-left: 32rpx;
}
.ml-5.data-v-ea8408ec {
  margin-left: 40rpx;
}
.mr-0.data-v-ea8408ec {
  margin-right: 0;
}
.mr-1.data-v-ea8408ec {
  margin-right: 8rpx;
}
.mr-2.data-v-ea8408ec {
  margin-right: 16rpx;
}
.mr-3.data-v-ea8408ec {
  margin-right: 24rpx;
}
.mr-4.data-v-ea8408ec {
  margin-right: 32rpx;
}
.mr-5.data-v-ea8408ec {
  margin-right: 40rpx;
}
.p-0.data-v-ea8408ec {
  padding: 0;
}
.p-1.data-v-ea8408ec {
  padding: 8rpx;
}
.p-2.data-v-ea8408ec {
  padding: 16rpx;
}
.p-3.data-v-ea8408ec {
  padding: 24rpx;
}
.p-4.data-v-ea8408ec {
  padding: 32rpx;
}
.p-5.data-v-ea8408ec {
  padding: 40rpx;
}
.pt-0.data-v-ea8408ec {
  padding-top: 0;
}
.pt-1.data-v-ea8408ec {
  padding-top: 8rpx;
}
.pt-2.data-v-ea8408ec {
  padding-top: 16rpx;
}
.pt-3.data-v-ea8408ec {
  padding-top: 24rpx;
}
.pt-4.data-v-ea8408ec {
  padding-top: 32rpx;
}
.pt-5.data-v-ea8408ec {
  padding-top: 40rpx;
}
.pb-0.data-v-ea8408ec {
  padding-bottom: 0;
}
.pb-1.data-v-ea8408ec {
  padding-bottom: 8rpx;
}
.pb-2.data-v-ea8408ec {
  padding-bottom: 16rpx;
}
.pb-3.data-v-ea8408ec {
  padding-bottom: 24rpx;
}
.pb-4.data-v-ea8408ec {
  padding-bottom: 32rpx;
}
.pb-5.data-v-ea8408ec {
  padding-bottom: 40rpx;
}
.pl-0.data-v-ea8408ec {
  padding-left: 0;
}
.pl-1.data-v-ea8408ec {
  padding-left: 8rpx;
}
.pl-2.data-v-ea8408ec {
  padding-left: 16rpx;
}
.pl-3.data-v-ea8408ec {
  padding-left: 24rpx;
}
.pl-4.data-v-ea8408ec {
  padding-left: 32rpx;
}
.pl-5.data-v-ea8408ec {
  padding-left: 40rpx;
}
.pr-0.data-v-ea8408ec {
  padding-right: 0;
}
.pr-1.data-v-ea8408ec {
  padding-right: 8rpx;
}
.pr-2.data-v-ea8408ec {
  padding-right: 16rpx;
}
.pr-3.data-v-ea8408ec {
  padding-right: 24rpx;
}
.pr-4.data-v-ea8408ec {
  padding-right: 32rpx;
}
.pr-5.data-v-ea8408ec {
  padding-right: 40rpx;
}
/* 宽高 */
.w-full.data-v-ea8408ec {
  width: 100%;
}
.h-full.data-v-ea8408ec {
  height: 100%;
}
.w-screen.data-v-ea8408ec {
  width: 100vw;
}
.h-screen.data-v-ea8408ec {
  height: 100vh;
}
/* 圆角 */
.rounded-none.data-v-ea8408ec {
  border-radius: 0;
}
.rounded-sm.data-v-ea8408ec {
  border-radius: 4rpx;
}
.rounded.data-v-ea8408ec {
  border-radius: 8rpx;
}
.rounded-lg.data-v-ea8408ec {
  border-radius: 16rpx;
}
.rounded-xl.data-v-ea8408ec {
  border-radius: 24rpx;
}
.rounded-full.data-v-ea8408ec {
  border-radius: 50%;
}
/* 阴影 */
.shadow-sm.data-v-ea8408ec {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.shadow.data-v-ea8408ec {
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
}
.shadow-lg.data-v-ea8408ec {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
/* 边框 */
.border.data-v-ea8408ec {
  border: 1rpx solid #E8E8E8;
}
.border-t.data-v-ea8408ec {
  border-top: 1rpx solid #E8E8E8;
}
.border-b.data-v-ea8408ec {
  border-bottom: 1rpx solid #E8E8E8;
}
.border-l.data-v-ea8408ec {
  border-left: 1rpx solid #E8E8E8;
}
.border-r.data-v-ea8408ec {
  border-right: 1rpx solid #E8E8E8;
}
/* 背景色 */
.bg-primary.data-v-ea8408ec {
  background-color: #2E8B57;
}
.bg-light.data-v-ea8408ec {
  background-color: #FFFFFF;
}
.bg-gray.data-v-ea8408ec {
  background-color: #F8F9FA;
}
.bg-success.data-v-ea8408ec {
  background-color: #52C41A;
}
.bg-warning.data-v-ea8408ec {
  background-color: #FAAD14;
}
.bg-error.data-v-ea8408ec {
  background-color: #F5222D;
}
/* 通用组件样式 */
.card.data-v-ea8408ec {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}
.divider.data-v-ea8408ec {
  height: 1rpx;
  background-color: #F0F0F0;
  margin: 24rpx 0;
}
.safe-area-bottom.data-v-ea8408ec {
  padding-bottom: env(safe-area-inset-bottom);
}
/* 状态样式 */
.status-pending.data-v-ea8408ec {
  color: #FAAD14;
}
.status-approved.data-v-ea8408ec {
  color: #52C41A;
}
.status-rejected.data-v-ea8408ec {
  color: #F5222D;
}
.status-not-submitted.data-v-ea8408ec {
  color: #595959;
}
/* 按钮样式扩展 */
.btn-primary.data-v-ea8408ec {
  background-color: #2E8B57;
  color: white;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
  border: none;
}
.btn-secondary.data-v-ea8408ec {
  background-color: transparent;
  color: #2E8B57;
  border: 1rpx solid #2E8B57;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
.btn-ghost.data-v-ea8408ec {
  background-color: transparent;
  color: #595959;
  border: 1rpx solid #E8E8E8;
  border-radius: 8rpx;
  padding: 24rpx 48rpx;
  font-size: 28rpx;
}
/* 动画 */
.fade-in.data-v-ea8408ec {
  animation: fadeIn-ea8408ec 0.3s ease-in-out;
}
@keyframes fadeIn-ea8408ec {
from {
    opacity: 0;
}
to {
    opacity: 1;
}
}
.slide-up.data-v-ea8408ec {
  animation: slideUp-ea8408ec 0.3s ease-out;
}
@keyframes slideUp-ea8408ec {
from {
    transform: translateY(100%);
}
to {
    transform: translateY(0);
}
}
/* 滚动条样式 */
.data-v-ea8408ec::-webkit-scrollbar {
  width: 0;
  height: 0;
  color: transparent;
}
/* 禁用长按选择 */
.no-select.data-v-ea8408ec {
  -webkit-user-select: none;
  user-select: none;
}
/* Vue过渡动画 */
.fade-enter-active.data-v-ea8408ec,
.fade-leave-active.data-v-ea8408ec {
  transition: opacity 0.3s ease;
}
.fade-enter-from.data-v-ea8408ec,
.fade-leave-to.data-v-ea8408ec {
  opacity: 0;
}
.slide-up-enter-active.data-v-ea8408ec,
.slide-up-leave-active.data-v-ea8408ec {
  transition: transform 0.3s ease;
}
.slide-up-enter-from.data-v-ea8408ec,
.slide-up-leave-to.data-v-ea8408ec {
  transform: translateY(100%);
}
.reading-container.data-v-ea8408ec {
  min-height: 100vh;
  background: #F8F9FA;
  padding-bottom: 200rpx;
}
.exam-info-card.data-v-ea8408ec {
  margin: 24rpx;
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.exam-info-card .exam-header.data-v-ea8408ec {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}
.exam-info-card .exam-header .exam-title.data-v-ea8408ec {
  flex: 1;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.exam-info-card .exam-header .exam-type-tag.data-v-ea8408ec {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  color: #fff;
}
.exam-info-card .exam-header .exam-type-tag.online.data-v-ea8408ec {
  background: #4A90E2;
}
.exam-info-card .exam-details .detail-row.data-v-ea8408ec {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}
.exam-info-card .exam-details .detail-row.data-v-ea8408ec:last-child {
  margin-bottom: 0;
}
.exam-info-card .exam-details .detail-row .detail-label.data-v-ea8408ec {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #666;
}
.exam-info-card .exam-details .detail-row .detail-value.data-v-ea8408ec {
  margin-left: 8rpx;
  font-size: 26rpx;
  font-weight: bold;
  color: #4A90E2;
}
.reading-content.data-v-ea8408ec {
  margin: 24rpx;
}
.reading-content .content-section.data-v-ea8408ec {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.reading-content .content-section .section-header.data-v-ea8408ec {
  display: flex;
  align-items: center;
  margin-bottom: 32rpx;
}
.reading-content .content-section .section-header .section-title.data-v-ea8408ec {
  margin-left: 16rpx;
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}
.notice-list .notice-item.data-v-ea8408ec {
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  margin-bottom: 16rpx;
  border-radius: 12rpx;
}
.notice-list .notice-item.important.data-v-ea8408ec {
  background: #fff7e6;
  border: 2rpx solid #ffd591;
}
.notice-list .notice-item text.data-v-ea8408ec {
  flex: 1;
  margin-left: 16rpx;
  font-size: 26rpx;
  line-height: 1.5;
  color: #333;
}
.auth-requirements .requirement-item.data-v-ea8408ec {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}
.auth-requirements .requirement-item.data-v-ea8408ec:last-child {
  margin-bottom: 0;
}
.auth-requirements .requirement-item .requirement-icon.data-v-ea8408ec {
  width: 80rpx;
  height: 80rpx;
  background: rgba(74, 144, 226, 0.1);
  border-radius: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.auth-requirements .requirement-item .requirement-content.data-v-ea8408ec {
  flex: 1;
}
.auth-requirements .requirement-item .requirement-content .requirement-title.data-v-ea8408ec {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 8rpx;
}
.auth-requirements .requirement-item .requirement-content .requirement-desc.data-v-ea8408ec {
  font-size: 24rpx;
  color: #666;
  line-height: 1.5;
}
.rules-list .rule-item.data-v-ea8408ec {
  display: flex;
  align-items: flex-start;
  margin-bottom: 24rpx;
}
.rules-list .rule-item.data-v-ea8408ec:last-child {
  margin-bottom: 0;
}
.rules-list .rule-item .rule-number.data-v-ea8408ec {
  width: 40rpx;
  height: 40rpx;
  background: #4A90E2;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  color: #fff;
  margin-right: 20rpx;
  flex-shrink: 0;
}
.rules-list .rule-item .rule-text.data-v-ea8408ec {
  flex: 1;
  font-size: 26rpx;
  line-height: 1.5;
  color: #333;
}
.device-requirements .device-item.data-v-ea8408ec {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}
.device-requirements .device-item.data-v-ea8408ec:last-child {
  border-bottom: none;
}
.device-requirements .device-item .device-content.data-v-ea8408ec {
  flex: 1;
  margin-left: 20rpx;
}
.device-requirements .device-item .device-content .device-title.data-v-ea8408ec {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 4rpx;
}
.device-requirements .device-item .device-content .device-desc.data-v-ea8408ec {
  font-size: 24rpx;
  color: #666;
}
.device-requirements .device-item .device-status.data-v-ea8408ec {
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-size: 24rpx;
  background: #fff2f0;
  color: #f56c6c;
}
.device-requirements .device-item .device-status.good.data-v-ea8408ec {
  background: #f6ffed;
  color: #4CAF50;
}
.confirm-section.data-v-ea8408ec {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
}
.confirm-section .confirm-checkbox.data-v-ea8408ec {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}
.confirm-section .confirm-checkbox .checkbox-text.data-v-ea8408ec {
  flex: 1;
  font-size: 26rpx;
  line-height: 1.5;
  color: #333;
}
.confirm-section .start-exam-btn.data-v-ea8408ec {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: bold;
}