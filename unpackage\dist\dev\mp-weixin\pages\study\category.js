"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const src_stores_user = require("../../src/stores/user.js");
const src_stores_app = require("../../src/stores/app.js");
const src_stores_study = require("../../src/stores/study.js");
const src_constants_index = require("../../src/constants/index.js");
const src_api_index = require("../../src/api/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  (_easycom_u_navbar2 + _easycom_u_icon2)();
}
const _easycom_u_navbar = () => "../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
if (!Math) {
  (_easycom_u_navbar + _easycom_u_icon + LoadingSpinner + EmptyState)();
}
const LoadingSpinner = () => "../../src/components/common/LoadingSpinner.js";
const EmptyState = () => "../../src/components/common/EmptyState.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "category",
  setup(__props) {
    const userStore = src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const studyStore = src_stores_study.useStudyStore();
    const isLoading = common_vendor.ref(true);
    const error = common_vendor.ref("");
    const categories = common_vendor.computed(() => studyStore.categories);
    common_vendor.onMounted(() => {
      loadCategories();
    });
    const loadCategories = () => __async(this, null, function* () {
      isLoading.value = true;
      error.value = "";
      try {
        const response = yield src_api_index.api.study.getCategories();
        studyStore.setCategories(response.data);
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/study/category.vue:148", "加载题库分类失败:", err);
        error.value = err.message || "加载失败";
      } finally {
        isLoading.value = false;
      }
    });
    const getCategoryIcon = (categoryName) => {
      const iconMap = {
        "疾病预防": "shield",
        "健康教育": "book",
        "流行病学": "search",
        "环境卫生": "leaf",
        "职业卫生": "briefcase",
        "营养与食品卫生": "apple",
        "儿童保健": "baby",
        "妇女保健": "female",
        "慢性病防控": "heart",
        "传染病防控": "virus"
      };
      return iconMap[categoryName] || "file-text";
    };
    const getPracticeInfo = (category) => {
      if (!userStore.isLoggedIn) {
        return "登录后可练习";
      }
      if (userStore.isAuthenticated) {
        return "无限练习";
      }
      if (!studyStore.canPracticeToday) {
        return "今日已达上限";
      }
      return "可免费练习";
    };
    const selectCategory = (category) => __async(this, null, function* () {
      if (!userStore.isLoggedIn) {
        appStore.showModal({
          title: "需要登录",
          content: "请先登录后再进行题库练习",
          confirmText: "立即登录",
          cancelText: "取消"
        }).then((confirmed) => {
          if (confirmed) {
            appStore.redirectTo(src_constants_index.PAGE_PATHS.LOGIN);
          }
        });
        return;
      }
      if (!userStore.isAuthenticated && !studyStore.canPracticeToday) {
        appStore.showModal({
          title: "今日练习已达上限",
          content: `免费用户每天可练习${src_constants_index.PRACTICE_CONFIG.FREE_SESSIONS_PER_DAY}组题目。完善个人资料并通过机构审核后可享受无限练习特权。`,
          confirmText: "完善资料",
          cancelText: "我知道了"
        }).then((confirmed) => {
          if (confirmed) {
            appStore.switchTab(src_constants_index.PAGE_PATHS.PERSONAL);
          }
        });
        return;
      }
      if (category.questionCount < src_constants_index.PRACTICE_CONFIG.QUESTIONS_PER_SESSION) {
        appStore.showToast(`该分类题目不足${src_constants_index.PRACTICE_CONFIG.QUESTIONS_PER_SESSION}道，暂时无法练习`);
        return;
      }
      try {
        appStore.showLoading("准备题目...");
        const response = yield src_api_index.api.study.getQuestions(category.id, src_constants_index.PRACTICE_CONFIG.QUESTIONS_PER_SESSION);
        if (response.data.length < src_constants_index.PRACTICE_CONFIG.QUESTIONS_PER_SESSION) {
          appStore.hideLoading();
          appStore.showToast("题目数量不足，请稍后再试");
          return;
        }
        const session = studyStore.startPracticeSession(category.id, response.data);
        appStore.hideLoading();
        appStore.navigateTo(src_constants_index.PAGE_PATHS.STUDY_PRACTICE, {
          sessionId: session.id,
          categoryName: category.name
        });
      } catch (err) {
        appStore.hideLoading();
        common_vendor.index.__f__("error", "at pages/study/category.vue:253", "开始练习失败:", err);
        appStore.showToast(err.message || "开始练习失败，请重试");
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "选择题库",
          autoBack: true,
          background: {
            background: "linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: common_vendor.unref(userStore).isLoggedIn
      }, common_vendor.unref(userStore).isLoggedIn ? common_vendor.e({
        c: common_vendor.p({
          name: "info-circle",
          color: "#4A90E2",
          size: "32"
        }),
        d: common_vendor.unref(userStore).isAuthenticated
      }, common_vendor.unref(userStore).isAuthenticated ? {} : common_vendor.unref(studyStore).canPracticeToday ? {
        f: common_vendor.t(common_vendor.unref(studyStore).remainingPracticeCount)
      } : {}, {
        e: common_vendor.unref(studyStore).canPracticeToday
      }) : {}, {
        g: isLoading.value
      }, isLoading.value ? {
        h: common_vendor.p({
          text: "加载题库分类..."
        })
      } : error.value ? {
        j: common_vendor.o(loadCategories),
        k: common_vendor.p({
          type: "no-network",
          title: error.value,
          description: "请检查网络连接后重试",
          showButton: true,
          buttonText: "重新加载"
        })
      } : common_vendor.e({
        l: categories.value.length === 0
      }, categories.value.length === 0 ? {
        m: common_vendor.p({
          type: "no-data",
          title: "暂无题库",
          description: "题库内容正在建设中",
          showButton: false
        })
      } : {
        n: common_vendor.f(categories.value, (category, k0, i0) => {
          return {
            a: "ba6cefc0-5-" + i0,
            b: common_vendor.p({
              name: getCategoryIcon(category.name),
              color: "#4A90E2",
              size: "60"
            }),
            c: common_vendor.t(category.name),
            d: common_vendor.t(category.description || "专业知识练习"),
            e: common_vendor.t(category.questionCount),
            f: "ba6cefc0-6-" + i0,
            g: common_vendor.t(getPracticeInfo()),
            h: category.id,
            i: common_vendor.o(($event) => selectCategory(category), category.id)
          };
        }),
        o: common_vendor.p({
          name: "arrow-right",
          color: "#4A90E2",
          size: "32"
        })
      }), {
        i: error.value,
        p: common_vendor.p({
          name: "lightbulb",
          color: "#FF9500",
          size: "32"
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ba6cefc0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/study/category.js.map
