"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const src_stores_user = require("../../src/stores/user.js");
const src_stores_app = require("../../src/stores/app.js");
const src_utils_index = require("../../src/utils/index.js");
const src_constants_index = require("../../src/constants/index.js");
const src_api_index = require("../../src/api/index.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_icon2 + _easycom_u_button2)();
}
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_button + LoadingSpinner + EmptyState + StatusTag + PermissionWrapper)();
}
const PermissionWrapper = () => "../../src/components/common/PermissionWrapper.js";
const LoadingSpinner = () => "../../src/components/common/LoadingSpinner.js";
const EmptyState = () => "../../src/components/common/EmptyState.js";
const StatusTag = () => "../../src/components/common/StatusTag.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "exam",
  setup(__props) {
    const userStore = src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const statusBarHeight = common_vendor.ref(0);
    const isLoading = common_vendor.ref(true);
    const currentExams = common_vendor.ref([]);
    const examStats = common_vendor.ref({
      total: 0,
      passed: 0,
      pending: 0
    });
    common_vendor.onMounted(() => {
      const systemInfo = common_vendor.index.getSystemInfoSync();
      statusBarHeight.value = systemInfo.statusBarHeight || 0;
      if (userStore.isAuthenticated) {
        loadExamData();
      }
    });
    common_vendor.onShow(() => {
      if (userStore.isAuthenticated) {
        loadExamData();
      }
    });
    const loadExamData = () => __async(this, null, function* () {
      isLoading.value = true;
      try {
        const response = yield src_api_index.api.exam.getCurrentExams();
        currentExams.value = response.data;
        examStats.value = {
          total: 12,
          passed: 10,
          pending: currentExams.value.length
        };
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/exam/exam.vue:218", "加载考试数据失败:", error);
        appStore.showToast(error.message || "加载失败，请重试");
      } finally {
        isLoading.value = false;
      }
    });
    const goToAuth = () => {
      var _a;
      if (((_a = userStore.userInfo) == null ? void 0 : _a.status) === "not_submitted") {
        appStore.redirectTo(src_constants_index.PAGE_PATHS.PROFILE);
      } else {
        appStore.switchTab(src_constants_index.PAGE_PATHS.PERSONAL);
      }
    };
    const viewCalendar = () => {
      appStore.showToast("考试日历功能开发中");
    };
    const formatExamTime = (exam) => {
      const start = new Date(exam.startTime);
      const end = new Date(exam.endTime);
      if (exam.type === "online") {
        return `${src_utils_index.formatDate(start, "MM月DD日 HH:mm")}-${src_utils_index.formatDate(end, "HH:mm")}`;
      } else {
        return `${src_utils_index.formatDate(start, "MM月DD日 HH:mm")}`;
      }
    };
    const getVenueText = (exam) => {
      return "待安排";
    };
    const getButtonType = (status) => {
      const typeMap = {
        "not_started": "primary",
        "in_progress": "warning",
        "completed": "info",
        "expired": "info"
      };
      return typeMap[status] || "default";
    };
    const canAction = (exam) => {
      return !["completed", "expired"].includes(exam.status);
    };
    const getActionText = (exam) => {
      const actionMap = {
        "not_started": "准备考试",
        "in_progress": "继续考试",
        "completed": "查看成绩",
        "expired": "已过期"
      };
      return actionMap[exam.status] || "查看详情";
    };
    const handleExamClick = (exam) => {
      if (exam.type === "online") {
        handleOnlineExam(exam);
      } else {
        handleOfflineExam(exam);
      }
    };
    const handleAction = (exam) => {
      if (exam.type === "online") {
        handleOnlineExam(exam);
      } else {
        handleOfflineExam(exam);
      }
    };
    const handleOnlineExam = (exam) => {
      if (exam.status === "not_started") {
        appStore.navigateTo(src_constants_index.PAGE_PATHS.EXAM_ONLINE_READING, { examId: exam.id });
      } else if (exam.status === "in_progress") {
        appStore.showModal({
          title: "继续考试",
          content: "检测到您有未完成的考试，是否继续？",
          confirmText: "继续考试",
          cancelText: "取消"
        }).then((confirmed) => {
          if (confirmed) {
            appStore.navigateTo(src_constants_index.PAGE_PATHS.EXAM_ONLINE_ANSWER, { examId: exam.id });
          }
        });
      } else if (exam.status === "completed") {
        appStore.navigateTo(src_constants_index.PAGE_PATHS.EXAM_HISTORY, { examId: exam.id });
      }
    };
    const handleOfflineExam = (exam) => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.EXAM_OFFLINE_DETAIL, { examId: exam.id });
    };
    const viewHistory = () => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.EXAM_HISTORY);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: statusBarHeight.value + "px",
        b: common_vendor.o(viewCalendar),
        c: common_vendor.p({
          name: "calendar",
          color: "#fff",
          size: "44"
        }),
        d: common_vendor.p({
          name: "lock",
          color: "#f56c6c",
          size: "80"
        }),
        e: common_vendor.o(goToAuth),
        f: common_vendor.p({
          type: "primary",
          size: "medium"
        }),
        g: common_vendor.t(examStats.value.total),
        h: common_vendor.t(examStats.value.passed),
        i: common_vendor.t(examStats.value.pending),
        j: isLoading.value
      }, isLoading.value ? {
        k: common_vendor.p({
          text: "加载考试信息..."
        })
      } : currentExams.value.length === 0 ? {
        m: common_vendor.p({
          type: "no-data",
          title: "暂无待参加的考试",
          description: "当前没有可参加的考试，请关注最新考试通知",
          showButton: false
        })
      } : {
        n: common_vendor.f(currentExams.value, (exam, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(exam.type === "online" ? "线上考试" : "线下考试"),
            b: common_vendor.n(exam.type),
            c: common_vendor.t(exam.name),
            d: common_vendor.t(exam.description),
            e: "970fed46-6-" + i0 + ",970fed46-1",
            f: common_vendor.t(formatExamTime(exam)),
            g: "970fed46-7-" + i0 + ",970fed46-1",
            h: common_vendor.t(exam.duration),
            i: exam.type === "offline"
          }, exam.type === "offline" ? {
            j: "970fed46-8-" + i0 + ",970fed46-1",
            k: common_vendor.p({
              name: "location",
              color: "#666",
              size: "24"
            }),
            l: common_vendor.t(getVenueText())
          } : {}, {
            m: "970fed46-9-" + i0 + ",970fed46-1",
            n: common_vendor.p({
              type: "exam",
              status: exam.status
            }),
            o: common_vendor.t(getActionText(exam)),
            p: common_vendor.o(($event) => handleAction(exam), exam.id),
            q: "970fed46-10-" + i0 + ",970fed46-1",
            r: common_vendor.p({
              type: getButtonType(exam.status),
              size: "small",
              disabled: !canAction(exam)
            }),
            s: exam.id,
            t: common_vendor.o(($event) => handleExamClick(exam), exam.id)
          });
        }),
        o: common_vendor.p({
          name: "clock",
          color: "#666",
          size: "24"
        }),
        p: common_vendor.p({
          name: "time",
          color: "#666",
          size: "24"
        })
      }, {
        l: currentExams.value.length === 0,
        q: common_vendor.p({
          name: "file-text",
          color: "#4A90E2",
          size: "60"
        }),
        r: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "32"
        }),
        s: common_vendor.o(viewHistory),
        t: common_vendor.p({
          permission: "authenticated",
          showFallback: true
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-970fed46"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/exam/exam.js.map
