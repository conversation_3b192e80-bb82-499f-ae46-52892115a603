{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-form-item/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // input的label提示语\n        label: {\n            type: String,\n            default: () => defProps.formItem.label\n        },\n        // 绑定的值\n        prop: {\n            type: String,\n            default: () => defProps.formItem.prop\n        },\n        // 绑定的规则\n        rules: {\n            type: Array,\n            default: () => defProps.formItem.rules\n        },\n        // 是否显示表单域的下划线边框\n        borderBottom: {\n            type: [String, Boolean],\n            default: () => defProps.formItem.borderBottom\n        },\n        // label的位置，left-左边，top-上边\n        labelPosition: {\n            type: String,\n            default: () => defProps.formItem.labelPosition\n        },\n        // label的宽度，单位px\n        labelWidth: {\n            type: [String, Number],\n            default: () => defProps.formItem.labelWidth\n        },\n        // 右侧图标\n        rightIcon: {\n            type: String,\n            default: () => defProps.formItem.rightIcon\n        },\n        // 左侧图标\n        leftIcon: {\n            type: String,\n            default: () => defProps.formItem.leftIcon\n        },\n        // 是否显示左边的必填星号，只作显示用，具体校验必填的逻辑，请在rules中配置\n        required: {\n            type: Boolean,\n            default: () => defProps.formItem.required\n        },\n        leftIconStyle: {\n            type: [String, Object],\n            default: () => defProps.formItem.leftIconStyle,\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM,CAAC,QAAQ,OAAO;AAAA,MACtB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA,IACD,eAAe;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACpC;AAAA,EACJ;AACL,CAAC;;"}