{"version": 3, "file": "mixin.js", "sources": ["uni_modules/uview-plus/libs/mixin/mixin.js"], "sourcesContent": ["import { defineMixin } from '../vue'\nimport { deepMerge, $parent, sleep } from '../function/index'\nimport test from '../function/test'\nimport route from '../util/route'\n// #ifdef APP-NVUE\n// 由于weex为阿里的KPI业绩考核的产物，所以不支持百分比单位，这里需要通过dom查询组件的宽度\nconst dom = uni.requireNativePlugin('dom')\n// #endif\n\nexport const mixin = defineMixin({\n    // 定义每个组件都可能需要用到的外部样式以及类名\n    props: {\n        // 每个组件都有的父组件传递的样式，可以为字符串或者对象形式\n        customStyle: {\n            type: [Object, String],\n            default: () => ({})\n        },\n        customClass: {\n            type: String,\n            default: ''\n        },\n        // 跳转的页面路径\n        url: {\n            type: String,\n            default: ''\n        },\n        // 页面跳转的类型\n        linkType: {\n            type: String,\n            default: 'navigateTo'\n        }\n    },\n    data() {\n        return {}\n    },\n    onLoad() {\n        // getRect挂载到$u上，因为这方法需要使用in(this)，所以无法把它独立成一个单独的文件导出\n        this.$u.getRect = this.$uGetRect\n    },\n    created() {\n        // 组件当中，只有created声明周期，为了能在组件使用，故也在created中将方法挂载到$u\n        this.$u.getRect = this.$uGetRect\n    },\n    computed: {\n        // 在2.x版本中，将会把$u挂载到uni对象下，导致在模板中无法使用uni.$u.xxx形式\n        // 所以这里通过computed计算属性将其附加到this.$u上，就可以在模板或者js中使用uni.$u.xxx\n        // 只在nvue环境通过此方式引入完整的$u，其他平台会出现性能问题，非nvue则按需引入（主要原因是props过大）\n        $u() {\n            // #ifndef APP-NVUE\n            // 在非nvue端，移除props，http，mixin等对象，避免在小程序setData时数据过大影响性能\n            return deepMerge(uni.$u, {\n                props: undefined,\n                http: undefined,\n                mixin: undefined\n            })\n            // #endif\n            // #ifdef APP-NVUE\n            return uni.$u\n            // #endif\n        },\n        /**\n         * 生成bem规则类名\n         * 由于微信小程序，H5，nvue之间绑定class的差异，无法通过:class=\"[bem()]\"的形式进行同用\n         * 故采用如下折中做法，最后返回的是数组（一般平台）或字符串（支付宝和字节跳动平台），类似['a', 'b', 'c']或'a b c'的形式\n         * @param {String} name 组件名称\n         * @param {Array} fixed 一直会存在的类名\n         * @param {Array} change 会根据变量值为true或者false而出现或者隐藏的类名\n         * @returns {Array|string}\n         */\n        bem() {\n            return function (name, fixed, change) {\n                // 类名前缀\n                const prefix = `u-${name}--`\n                const classes = {}\n                if (fixed) {\n                    fixed.map((item) => {\n                        // 这里的类名，会一直存在\n                        classes[prefix + this[item]] = true\n                    })\n                }\n                if (change) {\n                    change.map((item) => {\n                        // 这里的类名，会根据this[item]的值为true或者false，而进行添加或者移除某一个类\n                        this[item] ? (classes[prefix + item] = this[item]) : (delete classes[prefix + item])\n                    })\n                }\n                return Object.keys(classes)\n                    // 支付宝，头条小程序无法动态绑定一个数组类名，否则解析出来的结果会带有\",\"，而导致失效\n                    // #ifdef MP-ALIPAY || MP-TOUTIAO || MP-LARK\n                    .join(' ')\n                    // #endif\n            }\n        }\n    },\n    methods: {\n        // 跳转某一个页面\n        openPage(urlKey = 'url') {\n            const url = this[urlKey]\n            if (url) {\n                // h5官方回应：发行h5会自动摇树优化，所有使用uni的地方，都会被直接转换成具体的API调用 https://ask.dcloud.net.cn/question/161523?notification_id-1201922__rf-false__item_id-226372\n                // 使用封装的 route 进行跳转（直接调用方法），不使用 uni 对象\n                route({ type: this.linkType, url })\n                // 执行类似uni.navigateTo的方法\n                // uni[this.linkType]({\n                //     url\n                // })\n            }\n        },\n        navTo(url = '', linkType = 'navigateTo') {\n            route({ type: this.linkType, url })\n        },\n        // 查询节点信息\n        // 目前此方法在支付宝小程序中无法获取组件跟接点的尺寸，为支付宝的bug(2020-07-21)\n        // 解决办法为在组件根部再套一个没有任何作用的view元素\n        $uGetRect(selector, all) {\n            return new Promise((resolve) => {\n                // #ifndef APP-NVUE\n                uni.createSelectorQuery()\n                    .in(this)[all ? 'selectAll' : 'select'](selector)\n                    .boundingClientRect((rect) => {\n                        if (all && Array.isArray(rect) && rect.length) {\n                            resolve(rect)\n                        }\n                        if (!all && rect) {\n                            resolve(rect)\n                        }\n                    })\n                    .exec()\n                // #endif\n                \n                // #ifdef APP-NVUE\n                sleep(30).then(() => {\n                    let selectorNvue = selector.substring(1) // 去掉开头的#或者.\n                    let selectorRef = this.$refs[selectorNvue]\n                    if (!selectorRef) {\n                        // console.log('不存在元素，请检查是否设置了ref属性' + selectorNvue + '。')\n                        resolve({\n                            with: 0,\n                            height: 0,\n                            left: 0,\n                            right: 0,\n                            top: 0,\n                            bottom: 0\n                        }) \n                    }\n                    dom.getComponentRect(selectorRef, res => {\n                        // console.log(res)\n                        resolve(res.size)\n                    })\n                })\n                // #endif\n            })\n        },\n        getParentData(parentName = '') {\n            // 避免在created中去定义parent变量\n            if (!this.parent) this.parent = {}\n            // 这里的本质原理是，通过获取父组件实例(也即类似u-radio的父组件u-radio-group的this)\n            // 将父组件this中对应的参数，赋值给本组件(u-radio的this)的parentData对象中对应的属性\n            // 之所以需要这么做，是因为所有端中，头条小程序不支持通过this.parent.xxx去监听父组件参数的变化\n            // 此处并不会自动更新子组件的数据，而是依赖父组件u-radio-group去监听data的变化，手动调用更新子组件的方法去重新获取\n            this.parent = $parent.call(this, parentName)\n            if (this.parent.children) {\n                // 如果父组件的children不存在本组件的实例，才将本实例添加到父组件的children中\n                this.parent.children.indexOf(this) === -1 && this.parent.children.push(this)\n            }\n            if (this.parent && this.parentData) {\n                // 历遍parentData中的属性，将parent中的同名属性赋值给parentData\n                Object.keys(this.parentData).map((key) => {\n                    this.parentData[key] = this.parent[key]\n                })\n            }\n        },\n        // 阻止事件冒泡\n        preventEvent(e) {\n            e && typeof (e.stopPropagation) === 'function' && e.stopPropagation()\n        },\n        // 空操作\n        noop(e) {\n            this.preventEvent(e)\n        }\n    },\n    onReachBottom() {\n        uni.$emit('uOnReachBottom')\n\t},\n\tbeforeUnmount() {\n        // 判断当前页面是否存在parent和chldren，一般在checkbox和checkbox-group父子联动的场景会有此情况\n        // 组件销毁时，移除子组件在父组件children数组中的实例，释放资源，避免数据混乱\n        if (this.parent && test.array(this.parent.children)) {\n            // 组件销毁时，移除父组件中的children数组中对应的实例\n            const childrenList = this.parent.children\n            childrenList.map((child, index) => {\n                // 如果相等，则移除\n                if (child === this) {\n                    childrenList.splice(index, 1)\n                }\n            })\n        }\n    }\n})\n\nexport default mixin\n"], "names": ["defineMixin", "deepMerge", "uni", "route", "$parent", "test"], "mappings": ";;;;;;AASY,MAAC,QAAQA,+BAAAA,YAAY;AAAA;AAAA,EAE7B,OAAO;AAAA;AAAA,IAEH,aAAa;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,OAAO,CAAA;AAAA,IACnB;AAAA,IACD,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS;AAAA,IACZ;AAAA;AAAA,IAED,KAAK;AAAA,MACD,MAAM;AAAA,MACN,SAAS;AAAA,IACZ;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS;AAAA,IACZ;AAAA,EACJ;AAAA,EACD,OAAO;AACH,WAAO,CAAE;AAAA,EACZ;AAAA,EACD,SAAS;AAEL,SAAK,GAAG,UAAU,KAAK;AAAA,EAC1B;AAAA,EACD,UAAU;AAEN,SAAK,GAAG,UAAU,KAAK;AAAA,EAC1B;AAAA,EACD,UAAU;AAAA;AAAA;AAAA;AAAA,IAIN,KAAK;AAGD,aAAOC,0CAAS,UAACC,cAAG,MAAC,IAAI;AAAA,QACrB,OAAO;AAAA,QACP,MAAM;AAAA,QACN,OAAO;AAAA,MACvB,CAAa;AAAA,IAKJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAUD,MAAM;AACF,aAAO,SAAU,MAAM,OAAO,QAAQ;AAElC,cAAM,SAAS,KAAK,IAAI;AACxB,cAAM,UAAU,CAAE;AAClB,YAAI,OAAO;AACP,gBAAM,IAAI,CAAC,SAAS;AAEhB,oBAAQ,SAAS,KAAK,IAAI,CAAC,IAAI;AAAA,UACvD,CAAqB;AAAA,QACJ;AACD,YAAI,QAAQ;AACR,iBAAO,IAAI,CAAC,SAAS;AAEjB,iBAAK,IAAI,IAAK,QAAQ,SAAS,IAAI,IAAI,KAAK,IAAI,IAAM,OAAO,QAAQ,SAAS,IAAI;AAAA,UAC1G,CAAqB;AAAA,QACJ;AACD,eAAO,OAAO,KAAK,OAAO;AAAA,MAK7B;AAAA,IACJ;AAAA,EACJ;AAAA,EACD,SAAS;AAAA;AAAA,IAEL,SAAS,SAAS,OAAO;AACrB,YAAM,MAAM,KAAK,MAAM;AACvB,UAAI,KAAK;AAGLC,8CAAAA,MAAM,EAAE,MAAM,KAAK,UAAU,IAAG,CAAE;AAAA,MAKrC;AAAA,IACJ;AAAA,IACD,MAAM,MAAM,IAAI,WAAW,cAAc;AACrCA,4CAAAA,MAAM,EAAE,MAAM,KAAK,UAAU,IAAG,CAAE;AAAA,IACrC;AAAA;AAAA;AAAA;AAAA,IAID,UAAU,UAAU,KAAK;AACrB,aAAO,IAAI,QAAQ,CAAC,YAAY;AAE5BD,sBAAAA,MAAI,oBAAqB,EACpB,GAAG,IAAI,EAAE,MAAM,cAAc,QAAQ,EAAE,QAAQ,EAC/C,mBAAmB,CAAC,SAAS;AAC1B,cAAI,OAAO,MAAM,QAAQ,IAAI,KAAK,KAAK,QAAQ;AAC3C,oBAAQ,IAAI;AAAA,UACf;AACD,cAAI,CAAC,OAAO,MAAM;AACd,oBAAQ,IAAI;AAAA,UACf;AAAA,QACzB,CAAqB,EACA,KAAM;AAAA,MAwB3B,CAAa;AAAA,IACJ;AAAA,IACD,cAAc,aAAa,IAAI;AAE3B,UAAI,CAAC,KAAK;AAAQ,aAAK,SAAS,CAAE;AAKlC,WAAK,SAASE,0CAAAA,QAAQ,KAAK,MAAM,UAAU;AAC3C,UAAI,KAAK,OAAO,UAAU;AAEtB,aAAK,OAAO,SAAS,QAAQ,IAAI,MAAM,MAAM,KAAK,OAAO,SAAS,KAAK,IAAI;AAAA,MAC9E;AACD,UAAI,KAAK,UAAU,KAAK,YAAY;AAEhC,eAAO,KAAK,KAAK,UAAU,EAAE,IAAI,CAAC,QAAQ;AACtC,eAAK,WAAW,GAAG,IAAI,KAAK,OAAO,GAAG;AAAA,QAC1D,CAAiB;AAAA,MACJ;AAAA,IACJ;AAAA;AAAA,IAED,aAAa,GAAG;AACZ,WAAK,OAAQ,EAAE,oBAAqB,cAAc,EAAE,gBAAiB;AAAA,IACxE;AAAA;AAAA,IAED,KAAK,GAAG;AACJ,WAAK,aAAa,CAAC;AAAA,IACtB;AAAA,EACJ;AAAA,EACD,gBAAgB;AACZF,kBAAG,MAAC,MAAM,gBAAgB;AAAA,EAChC;AAAA,EACD,gBAAgB;AAGT,QAAI,KAAK,UAAUG,yCAAI,KAAC,MAAM,KAAK,OAAO,QAAQ,GAAG;AAEjD,YAAM,eAAe,KAAK,OAAO;AACjC,mBAAa,IAAI,CAAC,OAAO,UAAU;AAE/B,YAAI,UAAU,MAAM;AAChB,uBAAa,OAAO,OAAO,CAAC;AAAA,QAC/B;AAAA,MACjB,CAAa;AAAA,IACJ;AAAA,EACJ;AACL,CAAC;;"}