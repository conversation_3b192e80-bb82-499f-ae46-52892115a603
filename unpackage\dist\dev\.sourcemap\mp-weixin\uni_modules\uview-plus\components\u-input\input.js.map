{"version": 3, "file": "input.js", "sources": ["uni_modules/uview-plus/components/u-input/input.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:13:55\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/input.js\n */\nexport default {\n\t// index 组件\n\tinput: {\n\t\tvalue: '',\n\t\ttype: 'text',\n\t\tfixed: false,\n\t\tdisabled: false,\n\t\tdisabledColor: '#f5f7fa',\n\t\tclearable: false,\n\t\tpassword: false,\n\t\tmaxlength: 140,\n\t\tplaceholder: null,\n\t\tplaceholderClass: 'input-placeholder',\n\t\tplaceholderStyle: 'color: #c0c4cc',\n\t\tshowWordLimit: false,\n\t\tconfirmType: 'done',\n\t\tconfirmHold: false,\n\t\tholdKeyboard: false,\n\t\tfocus: false,\n\t\tautoBlur: false,\n\t\tdisableDefaultPadding: false,\n\t\tcursor: -1,\n\t\tcursorSpacing: 30,\n\t\tselectionStart: -1,\n\t\tselectionEnd: -1,\n\t\tadjustPosition: true,\n\t\tinputAlign: 'left',\n\t\tfontSize: '15px',\n\t\tcolor: '#303133',\n\t\tprefixIcon: '',\n\t\tprefixIconStyle: '',\n\t\tsuffixIcon: '',\n\t\tsuffixIconStyle: '',\n\t\tborder: 'surround',\n\t\treadonly: false,\n\t\tshape: 'square',\n\t\tformatter: null\n\t}\n}\n"], "names": [], "mappings": ";AASA,MAAe,QAAA;AAAA;AAAA,EAEd,OAAO;AAAA,IACN,OAAO;AAAA,IACP,MAAM;AAAA,IACN,OAAO;AAAA,IACP,UAAU;AAAA,IACV,eAAe;AAAA,IACf,WAAW;AAAA,IACX,UAAU;AAAA,IACV,WAAW;AAAA,IACX,aAAa;AAAA,IACb,kBAAkB;AAAA,IAClB,kBAAkB;AAAA,IAClB,eAAe;AAAA,IACf,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,IACd,OAAO;AAAA,IACP,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB,QAAQ;AAAA,IACR,eAAe;AAAA,IACf,gBAAgB;AAAA,IAChB,cAAc;AAAA,IACd,gBAAgB;AAAA,IAChB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,OAAO;AAAA,IACP,WAAW;AAAA,EACX;AACF;;"}