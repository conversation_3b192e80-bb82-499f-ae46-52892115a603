/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-91b05052,
.u-empty__wrap.data-v-91b05052,
.u-tabs.data-v-91b05052,
.u-tabs__wrapper.data-v-91b05052,
.u-tabs__wrapper__scroll-view-wrapper.data-v-91b05052,
.u-tabs__wrapper__scroll-view.data-v-91b05052,
.u-tabs__wrapper__nav.data-v-91b05052,
.u-tabs__wrapper__nav__line.data-v-91b05052,
.up-empty.data-v-91b05052,
.up-empty__wrap.data-v-91b05052,
.up-tabs.data-v-91b05052,
.up-tabs__wrapper.data-v-91b05052,
.up-tabs__wrapper__scroll-view-wrapper.data-v-91b05052,
.up-tabs__wrapper__scroll-view.data-v-91b05052,
.up-tabs__wrapper__nav.data-v-91b05052,
.up-tabs__wrapper__nav__line.data-v-91b05052 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-picker.data-v-91b05052 {
  position: relative;
}
.u-picker-input.data-v-91b05052 {
  position: relative;
}
.u-picker-input .input-cover.data-v-91b05052 {
  opacity: 0;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
}
.u-picker__view__column.data-v-91b05052 {
  display: flex;
  flex-direction: row;
  flex: 1;
  justify-content: center;
}
.u-picker__view__column__item.data-v-91b05052 {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  font-size: 16px;
  text-align: center;
  display: block;
  color: #303133;
}
.u-picker__view__column__item--disabled.data-v-91b05052 {
  cursor: not-allowed;
  opacity: 0.35;
}
.u-picker--loading.data-v-91b05052 {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.87);
  z-index: 1000;
}