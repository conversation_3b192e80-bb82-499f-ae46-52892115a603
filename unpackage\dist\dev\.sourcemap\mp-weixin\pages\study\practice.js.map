{"version": 3, "file": "practice.js", "sources": ["pages/study/practice.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc3R1ZHkvcHJhY3RpY2UudnVl"], "sourcesContent": ["<template>\n  <view class=\"practice-container\">\n    <!-- 自定义导航栏 -->\n    <u-navbar \n      :title=\"navTitle\" \n      :autoBack=\"true\"\n      :background=\"{ background: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)' }\"\n      titleStyle=\"color: #fff; font-weight: bold;\"\n      @leftClick=\"handleBack\"\n    />\n    \n    <!-- 练习进度 -->\n    <view class=\"practice-progress\">\n      <view class=\"progress-info\">\n        <text class=\"current-question\">第 {{ currentQuestionIndex + 1 }} 题</text>\n        <text class=\"total-questions\">共 {{ totalQuestions }} 题</text>\n      </view>\n      <view class=\"progress-bar\">\n        <view \n          class=\"progress-fill\" \n          :style=\"{ width: progressPercentage + '%' }\"\n        ></view>\n      </view>\n      <view class=\"time-info\">\n        <u-icon name=\"clock\" color=\"#666\" size=\"24\" />\n        <text class=\"elapsed-time\">{{ formatTime(elapsedTime) }}</text>\n      </view>\n    </view>\n    \n    <!-- 题目内容 -->\n    <view v-if=\"currentQuestion\" class=\"question-container\">\n      <view class=\"question-header\">\n        <view class=\"question-type\">\n          <StatusTag :type=\"'question'\" :status=\"currentQuestion.type\" />\n        </view>\n        <view v-if=\"currentQuestion.difficulty\" class=\"question-difficulty\">\n          <text :class=\"getDifficultyClass(currentQuestion.difficulty)\">\n            {{ getDifficultyText(currentQuestion.difficulty) }}\n          </text>\n        </view>\n      </view>\n      \n      <view class=\"question-content\">\n        <text class=\"question-title\">{{ currentQuestion.title }}</text>\n      </view>\n      \n      <!-- 选择题选项 -->\n      <view v-if=\"isChoiceQuestion\" class=\"question-options\">\n        <view \n          v-for=\"(option, index) in currentQuestion.options\" \n          :key=\"index\"\n          class=\"option-item\"\n          :class=\"getOptionClass(index)\"\n          @click=\"selectOption(index)\"\n        >\n          <view class=\"option-indicator\">\n            <text class=\"option-label\">{{ getOptionLabel(index) }}</text>\n            <u-icon \n              v-if=\"isOptionSelected(index)\"\n              :name=\"currentQuestion.type === 'multiple' ? 'checkbox-mark' : 'checkmark-circle-fill'\"\n              color=\"#4A90E2\"\n              size=\"32\"\n            />\n          </view>\n          <text class=\"option-text\">{{ option }}</text>\n        </view>\n      </view>\n      \n      <!-- 判断题选项 -->\n      <view v-else-if=\"currentQuestion.type === 'judge'\" class=\"judge-options\">\n        <view \n          class=\"judge-option\"\n          :class=\"{ active: currentAnswer === true }\"\n          @click=\"selectJudge(true)\"\n        >\n          <u-icon \n            :name=\"currentAnswer === true ? 'checkmark-circle-fill' : 'checkmark-circle'\"\n            :color=\"currentAnswer === true ? '#4CAF50' : '#ccc'\"\n            size=\"48\"\n          />\n          <text>正确</text>\n        </view>\n        <view \n          class=\"judge-option\"\n          :class=\"{ active: currentAnswer === false }\"\n          @click=\"selectJudge(false)\"\n        >\n          <u-icon \n            :name=\"currentAnswer === false ? 'close-circle-fill' : 'close-circle'\"\n            :color=\"currentAnswer === false ? '#f56c6c' : '#ccc'\"\n            size=\"48\"\n          />\n          <text>错误</text>\n        </view>\n      </view>\n      \n      <!-- 问答题输入 -->\n      <view v-else-if=\"currentQuestion.type === 'essay'\" class=\"essay-input\">\n        <u-textarea \n          v-model=\"currentAnswer\"\n          placeholder=\"请输入您的答案...\"\n          :maxlength=\"500\"\n          :showWordLimit=\"true\"\n          height=\"300\"\n          autoHeight\n        />\n      </view>\n    </view>\n    \n    <!-- 操作按钮 -->\n    <view class=\"action-buttons\">\n      <u-button \n        v-if=\"currentQuestionIndex > 0\"\n        class=\"prev-btn\"\n        type=\"info\"\n        plain\n        @click=\"prevQuestion\"\n      >\n        上一题\n      </u-button>\n      \n      <u-button \n        class=\"next-btn\"\n        type=\"primary\"\n        :disabled=\"!hasAnswer\"\n        @click=\"nextQuestion\"\n      >\n        {{ isLastQuestion ? '完成练习' : '下一题' }}\n      </u-button>\n    </view>\n    \n    <!-- 答题卡 -->\n    <view class=\"answer-sheet-btn\" @click=\"showAnswerSheet = true\">\n      <u-icon name=\"grid\" color=\"#4A90E2\" size=\"32\" />\n      <text>答题卡</text>\n    </view>\n    \n    <!-- 答题卡弹窗 -->\n    <u-popup \n      v-model=\"showAnswerSheet\" \n      mode=\"bottom\" \n      height=\"60%\"\n      :closeOnClickOverlay=\"true\"\n    >\n      <view class=\"answer-sheet-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">答题卡</text>\n          <u-icon name=\"close\" size=\"32\" @click=\"showAnswerSheet = false\" />\n        </view>\n        <view class=\"answer-grid\">\n          <view \n            v-for=\"(question, index) in questions\" \n            :key=\"question.id\"\n            class=\"answer-item\"\n            :class=\"getAnswerItemClass(index)\"\n            @click=\"jumpToQuestion(index)\"\n          >\n            <text>{{ index + 1 }}</text>\n          </view>\n        </view>\n        <view class=\"answer-legend\">\n          <view class=\"legend-item\">\n            <view class=\"legend-color answered\"></view>\n            <text>已答</text>\n          </view>\n          <view class=\"legend-item\">\n            <view class=\"legend-color current\"></view>\n            <text>当前</text>\n          </view>\n          <view class=\"legend-item\">\n            <view class=\"legend-color unanswered\"></view>\n            <text>未答</text>\n          </view>\n        </view>\n      </view>\n    </u-popup>\n    \n    <!-- 退出确认弹窗 -->\n    <u-modal \n      v-model=\"showExitModal\"\n      title=\"确认退出\"\n      content=\"退出后当前练习进度将丢失，确定要退出吗？\"\n      showCancelButton\n      @confirm=\"confirmExit\"\n      @cancel=\"showExitModal = false\"\n    />\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\nimport { useStudyStore } from '../../src/stores/study'\nimport { useAppStore } from '../../src/stores/app'\nimport { PAGE_PATHS } from '../../src/constants'\nimport { formatDuration } from '../../src/utils'\nimport type { Question } from '../../src/types'\n\n// 导入组件\nimport StatusTag from '../../src/components/common/StatusTag.vue'\n\n// Store\nconst studyStore = useStudyStore()\nconst appStore = useAppStore()\n\n// 页面参数\nconst props = defineProps<{\n  sessionId: string\n  categoryName?: string\n}>()\n\n// 响应式数据\nconst showAnswerSheet = ref(false)\nconst showExitModal = ref(false)\nconst elapsedTime = ref(0)\nlet timer: number | null = null\n\n// 计算属性\nconst currentSession = computed(() => studyStore.currentSession)\nconst questions = computed(() => currentSession.value?.questions || [])\nconst currentQuestionIndex = computed(() => studyStore.currentQuestionIndex)\nconst currentQuestion = computed(() => studyStore.currentQuestion)\nconst totalQuestions = computed(() => questions.value.length)\nconst isLastQuestion = computed(() => currentQuestionIndex.value === totalQuestions.value - 1)\nconst progressPercentage = computed(() => {\n  if (totalQuestions.value === 0) return 0\n  return ((currentQuestionIndex.value + 1) / totalQuestions.value) * 100\n})\n\nconst navTitle = computed(() => {\n  return props.categoryName || '题库练习'\n})\n\nconst currentAnswer = computed({\n  get: () => {\n    if (!currentQuestion.value || !currentSession.value) return null\n    return currentSession.value.answers[currentQuestion.value.id] || null\n  },\n  set: (value) => {\n    if (currentQuestion.value) {\n      studyStore.answerQuestion(currentQuestion.value.id, value)\n    }\n  }\n})\n\nconst hasAnswer = computed(() => {\n  if (!currentQuestion.value) return false\n  \n  const answer = currentAnswer.value\n  if (answer === null || answer === undefined) return false\n  \n  if (currentQuestion.value.type === 'multiple') {\n    return Array.isArray(answer) && answer.length > 0\n  }\n  \n  if (currentQuestion.value.type === 'essay') {\n    return typeof answer === 'string' && answer.trim().length > 0\n  }\n  \n  return true\n})\n\nconst isChoiceQuestion = computed(() => {\n  return currentQuestion.value?.type === 'single' || currentQuestion.value?.type === 'multiple'\n})\n\nonMounted(() => {\n  // 检查会话是否存在\n  if (!currentSession.value || currentSession.value.id !== props.sessionId) {\n    appStore.showToast('练习会话不存在')\n    appStore.navigateBack()\n    return\n  }\n  \n  // 开始计时\n  startTimer()\n})\n\nonUnmounted(() => {\n  // 清理计时器\n  if (timer) {\n    clearInterval(timer)\n  }\n})\n\n// 开始计时\nconst startTimer = () => {\n  timer = setInterval(() => {\n    elapsedTime.value++\n  }, 1000)\n}\n\n// 格式化时间\nconst formatTime = (seconds: number) => {\n  return formatDuration(seconds)\n}\n\n// 获取选项标签\nconst getOptionLabel = (index: number) => {\n  return String.fromCharCode(65 + index) // A, B, C, D...\n}\n\n// 获取选项样式类\nconst getOptionClass = (index: number) => {\n  const classes = ['option']\n  if (isOptionSelected(index)) {\n    classes.push('selected')\n  }\n  return classes.join(' ')\n}\n\n// 检查选项是否被选中\nconst isOptionSelected = (index: number) => {\n  if (!currentQuestion.value) return false\n  \n  const answer = currentAnswer.value\n  if (currentQuestion.value.type === 'multiple') {\n    return Array.isArray(answer) && answer.includes(index)\n  } else {\n    return answer === index\n  }\n}\n\n// 选择选项\nconst selectOption = (index: number) => {\n  if (!currentQuestion.value) return\n  \n  if (currentQuestion.value.type === 'multiple') {\n    let answer = Array.isArray(currentAnswer.value) ? [...currentAnswer.value] : []\n    const selectedIndex = answer.indexOf(index)\n    \n    if (selectedIndex > -1) {\n      answer.splice(selectedIndex, 1)\n    } else {\n      answer.push(index)\n    }\n    \n    currentAnswer.value = answer\n  } else {\n    currentAnswer.value = index\n  }\n}\n\n// 选择判断题答案\nconst selectJudge = (value: boolean) => {\n  currentAnswer.value = value\n}\n\n// 获取难度样式类\nconst getDifficultyClass = (difficulty: string) => {\n  const classMap = {\n    easy: 'difficulty-easy',\n    medium: 'difficulty-medium',\n    hard: 'difficulty-hard'\n  }\n  return classMap[difficulty] || 'difficulty-medium'\n}\n\n// 获取难度文本\nconst getDifficultyText = (difficulty: string) => {\n  const textMap = {\n    easy: '基础',\n    medium: '进阶',\n    hard: '高级'\n  }\n  return textMap[difficulty] || '进阶'\n}\n\n// 上一题\nconst prevQuestion = () => {\n  if (currentQuestionIndex.value > 0) {\n    // 这里需要在store中实现跳转到指定题目的方法\n    jumpToQuestion(currentQuestionIndex.value - 1)\n  }\n}\n\n// 下一题\nconst nextQuestion = () => {\n  if (isLastQuestion.value) {\n    // 完成练习\n    finishPractice()\n  } else {\n    jumpToQuestion(currentQuestionIndex.value + 1)\n  }\n}\n\n// 跳转到指定题目\nconst jumpToQuestion = (index: number) => {\n  // 这里需要在store中实现跳转逻辑\n  showAnswerSheet.value = false\n}\n\n// 获取答题卡项目样式类\nconst getAnswerItemClass = (index: number) => {\n  const classes = ['answer-item']\n  \n  if (index === currentQuestionIndex.value) {\n    classes.push('current')\n  } else if (currentSession.value?.answers[questions.value[index]?.id]) {\n    classes.push('answered')\n  } else {\n    classes.push('unanswered')\n  }\n  \n  return classes.join(' ')\n}\n\n// 完成练习\nconst finishPractice = () => {\n  if (!currentSession.value) return\n  \n  // 停止计时\n  if (timer) {\n    clearInterval(timer)\n    timer = null\n  }\n  \n  // 完成会话\n  const completedSession = studyStore.completeSession()\n  \n  if (completedSession) {\n    // 跳转到总结页面\n    appStore.redirectTo(PAGE_PATHS.STUDY_SUMMARY, { \n      sessionId: completedSession.id \n    })\n  }\n}\n\n// 处理返回\nconst handleBack = () => {\n  showExitModal.value = true\n}\n\n// 确认退出\nconst confirmExit = () => {\n  // 清理当前会话\n  studyStore.clearCurrentSession()\n  \n  // 停止计时\n  if (timer) {\n    clearInterval(timer)\n    timer = null\n  }\n  \n  appStore.navigateBack()\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../src/styles/global.scss';\n\n.practice-container {\n  min-height: 100vh;\n  background: $acdc-bg-primary;\n  padding-bottom: 120rpx;\n}\n\n.practice-progress {\n  background: #fff;\n  padding: 32rpx;\n  margin: 24rpx;\n  border-radius: 16rpx;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);\n  \n  .progress-info {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 16rpx;\n    \n    .current-question {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #4A90E2;\n    }\n    \n    .total-questions {\n      font-size: 26rpx;\n      color: #666;\n    }\n  }\n  \n  .progress-bar {\n    height: 8rpx;\n    background: #f0f0f0;\n    border-radius: 4rpx;\n    overflow: hidden;\n    margin-bottom: 16rpx;\n    \n    .progress-fill {\n      height: 100%;\n      background: linear-gradient(90deg, #4A90E2 0%, #357ABD 100%);\n      transition: width 0.3s ease;\n    }\n  }\n  \n  .time-info {\n    display: flex;\n    align-items: center;\n    gap: 8rpx;\n    \n    .elapsed-time {\n      font-size: 24rpx;\n      color: #666;\n    }\n  }\n}\n\n.question-container {\n  background: #fff;\n  margin: 24rpx;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  \n  .question-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 32rpx;\n    \n    .question-difficulty {\n      .difficulty-easy {\n        color: #4CAF50;\n        font-size: 24rpx;\n        font-weight: bold;\n      }\n      \n      .difficulty-medium {\n        color: #FF9500;\n        font-size: 24rpx;\n        font-weight: bold;\n      }\n      \n      .difficulty-hard {\n        color: #f56c6c;\n        font-size: 24rpx;\n        font-weight: bold;\n      }\n    }\n  }\n  \n  .question-content {\n    margin-bottom: 40rpx;\n    \n    .question-title {\n      font-size: 32rpx;\n      line-height: 1.6;\n      color: #333;\n      font-weight: 500;\n    }\n  }\n  \n  .question-options {\n    .option-item {\n      display: flex;\n      align-items: center;\n      padding: 24rpx;\n      margin-bottom: 16rpx;\n      border: 2rpx solid #f0f0f0;\n      border-radius: 16rpx;\n      transition: all 0.3s ease;\n      \n      &.selected {\n        border-color: #4A90E2;\n        background: rgba(74, 144, 226, 0.05);\n      }\n      \n      .option-indicator {\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        width: 60rpx;\n        margin-right: 24rpx;\n        \n        .option-label {\n          font-size: 28rpx;\n          font-weight: bold;\n          color: #4A90E2;\n        }\n      }\n      \n      .option-text {\n        flex: 1;\n        font-size: 28rpx;\n        line-height: 1.5;\n        color: #333;\n      }\n    }\n  }\n  \n  .judge-options {\n    display: flex;\n    gap: 40rpx;\n    justify-content: center;\n    \n    .judge-option {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      gap: 16rpx;\n      padding: 40rpx;\n      border: 2rpx solid #f0f0f0;\n      border-radius: 20rpx;\n      flex: 1;\n      transition: all 0.3s ease;\n      \n      &.active {\n        border-color: #4A90E2;\n        background: rgba(74, 144, 226, 0.05);\n      }\n      \n      text {\n        font-size: 28rpx;\n        font-weight: bold;\n        color: #333;\n      }\n    }\n  }\n  \n  .essay-input {\n    margin-top: 20rpx;\n  }\n}\n\n.action-buttons {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #fff;\n  padding: 24rpx 30rpx;\n  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);\n  display: flex;\n  gap: 24rpx;\n  \n  .prev-btn {\n    flex: 1;\n  }\n  \n  .next-btn {\n    flex: 2;\n  }\n}\n\n.answer-sheet-btn {\n  position: fixed;\n  right: 30rpx;\n  bottom: 200rpx;\n  width: 120rpx;\n  height: 120rpx;\n  background: #fff;\n  border-radius: 60rpx;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  gap: 8rpx;\n  \n  text {\n    font-size: 20rpx;\n    color: #4A90E2;\n    font-weight: bold;\n  }\n}\n\n.answer-sheet-modal {\n  padding: 40rpx;\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  \n  .modal-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 40rpx;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n    }\n  }\n  \n  .answer-grid {\n    display: grid;\n    grid-template-columns: repeat(5, 1fr);\n    gap: 20rpx;\n    flex: 1;\n    margin-bottom: 40rpx;\n    \n    .answer-item {\n      aspect-ratio: 1;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      border-radius: 12rpx;\n      font-size: 28rpx;\n      font-weight: bold;\n      \n      &.answered {\n        background: #4CAF50;\n        color: #fff;\n      }\n      \n      &.current {\n        background: #4A90E2;\n        color: #fff;\n      }\n      \n      &.unanswered {\n        background: #f0f0f0;\n        color: #999;\n      }\n    }\n  }\n  \n  .answer-legend {\n    display: flex;\n    justify-content: center;\n    gap: 40rpx;\n    \n    .legend-item {\n      display: flex;\n      align-items: center;\n      gap: 12rpx;\n      \n      .legend-color {\n        width: 24rpx;\n        height: 24rpx;\n        border-radius: 4rpx;\n        \n        &.answered {\n          background: #4CAF50;\n        }\n        \n        &.current {\n          background: #4A90E2;\n        }\n        \n        &.unanswered {\n          background: #f0f0f0;\n        }\n      }\n      \n      text {\n        font-size: 24rpx;\n        color: #666;\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/study/practice.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useStudyStore", "useAppStore", "ref", "computed", "onMounted", "onUnmounted", "formatDuration", "PAGE_PATHS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAsMA,MAAA,YAAsB,MAAA;;;;;;;;AAGtB,UAAM,aAAaA,iBAAAA;AACnB,UAAM,WAAWC,eAAAA;AAGjB,UAAM,QAAQ;AAMR,UAAA,kBAAkBC,kBAAI,KAAK;AAC3B,UAAA,gBAAgBA,kBAAI,KAAK;AACzB,UAAA,cAAcA,kBAAI,CAAC;AACzB,QAAI,QAAuB;AAG3B,UAAM,iBAAiBC,cAAA,SAAS,MAAM,WAAW,cAAc;AAC/D,UAAM,YAAYA,cAAAA,SAAS,MAAM;;AAAA,mCAAe,UAAf,mBAAsB,cAAa,CAAA;AAAA,KAAE;AACtE,UAAM,uBAAuBA,cAAA,SAAS,MAAM,WAAW,oBAAoB;AAC3E,UAAM,kBAAkBA,cAAA,SAAS,MAAM,WAAW,eAAe;AACjE,UAAM,iBAAiBA,cAAAA,SAAS,MAAM,UAAU,MAAM,MAAM;AAC5D,UAAM,iBAAiBA,cAAS,SAAA,MAAM,qBAAqB,UAAU,eAAe,QAAQ,CAAC;AACvF,UAAA,qBAAqBA,cAAAA,SAAS,MAAM;AACxC,UAAI,eAAe,UAAU;AAAU,eAAA;AACvC,cAAS,qBAAqB,QAAQ,KAAK,eAAe,QAAS;AAAA,IAAA,CACpE;AAEK,UAAA,WAAWA,cAAAA,SAAS,MAAM;AAC9B,aAAO,MAAM,gBAAgB;AAAA,IAAA,CAC9B;AAED,UAAM,gBAAgBA,cAAAA,SAAS;AAAA,MAC7B,KAAK,MAAM;AACT,YAAI,CAAC,gBAAgB,SAAS,CAAC,eAAe;AAAc,iBAAA;AAC5D,eAAO,eAAe,MAAM,QAAQ,gBAAgB,MAAM,EAAE,KAAK;AAAA,MACnE;AAAA,MACA,KAAK,CAAC,UAAU;AACd,YAAI,gBAAgB,OAAO;AACzB,qBAAW,eAAe,gBAAgB,MAAM,IAAI,KAAK;AAAA,QAC3D;AAAA,MACF;AAAA,IAAA,CACD;AAEK,UAAA,YAAYA,cAAAA,SAAS,MAAM;AAC/B,UAAI,CAAC,gBAAgB;AAAc,eAAA;AAEnC,YAAM,SAAS,cAAc;AACzB,UAAA,WAAW,QAAQ,WAAW;AAAkB,eAAA;AAEhD,UAAA,gBAAgB,MAAM,SAAS,YAAY;AAC7C,eAAO,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS;AAAA,MAClD;AAEI,UAAA,gBAAgB,MAAM,SAAS,SAAS;AAC1C,eAAO,OAAO,WAAW,YAAY,OAAO,OAAO,SAAS;AAAA,MAC9D;AAEO,aAAA;AAAA,IAAA,CACR;AAEK,UAAA,mBAAmBA,cAAAA,SAAS,MAAM;;AACtC,eAAO,qBAAgB,UAAhB,mBAAuB,UAAS,cAAY,qBAAgB,UAAhB,mBAAuB,UAAS;AAAA,IAAA,CACpF;AAEDC,kBAAAA,UAAU,MAAM;AAEd,UAAI,CAAC,eAAe,SAAS,eAAe,MAAM,OAAO,MAAM,WAAW;AACxE,iBAAS,UAAU,SAAS;AAC5B,iBAAS,aAAa;AACtB;AAAA,MACF;AAGW;IAAA,CACZ;AAEDC,kBAAAA,YAAY,MAAM;AAEhB,UAAI,OAAO;AACT,sBAAc,KAAK;AAAA,MACrB;AAAA,IAAA,CACD;AAGD,UAAM,aAAa,MAAM;AACvB,cAAQ,YAAY,MAAM;AACZ,oBAAA;AAAA,SACX,GAAI;AAAA,IAAA;AAIH,UAAA,aAAa,CAAC,YAAoB;AACtC,aAAOC,gBAAAA,eAAe,OAAO;AAAA,IAAA;AAIzB,UAAA,iBAAiB,CAAC,UAAkB;AACjC,aAAA,OAAO,aAAa,KAAK,KAAK;AAAA,IAAA;AAIjC,UAAA,iBAAiB,CAAC,UAAkB;AAClC,YAAA,UAAU,CAAC,QAAQ;AACrB,UAAA,iBAAiB,KAAK,GAAG;AAC3B,gBAAQ,KAAK,UAAU;AAAA,MACzB;AACO,aAAA,QAAQ,KAAK,GAAG;AAAA,IAAA;AAInB,UAAA,mBAAmB,CAAC,UAAkB;AAC1C,UAAI,CAAC,gBAAgB;AAAc,eAAA;AAEnC,YAAM,SAAS,cAAc;AACzB,UAAA,gBAAgB,MAAM,SAAS,YAAY;AAC7C,eAAO,MAAM,QAAQ,MAAM,KAAK,OAAO,SAAS,KAAK;AAAA,MAAA,OAChD;AACL,eAAO,WAAW;AAAA,MACpB;AAAA,IAAA;AAII,UAAA,eAAe,CAAC,UAAkB;AACtC,UAAI,CAAC,gBAAgB;AAAO;AAExB,UAAA,gBAAgB,MAAM,SAAS,YAAY;AACzC,YAAA,SAAS,MAAM,QAAQ,cAAc,KAAK,IAAI,CAAC,GAAG,cAAc,KAAK,IAAI;AACvE,cAAA,gBAAgB,OAAO,QAAQ,KAAK;AAE1C,YAAI,gBAAgB,IAAI;AACf,iBAAA,OAAO,eAAe,CAAC;AAAA,QAAA,OACzB;AACL,iBAAO,KAAK,KAAK;AAAA,QACnB;AAEA,sBAAc,QAAQ;AAAA,MAAA,OACjB;AACL,sBAAc,QAAQ;AAAA,MACxB;AAAA,IAAA;AAII,UAAA,cAAc,CAAC,UAAmB;AACtC,oBAAc,QAAQ;AAAA,IAAA;AAIlB,UAAA,qBAAqB,CAAC,eAAuB;AACjD,YAAM,WAAW;AAAA,QACf,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MAAA;AAED,aAAA,SAAS,UAAU,KAAK;AAAA,IAAA;AAI3B,UAAA,oBAAoB,CAAC,eAAuB;AAChD,YAAM,UAAU;AAAA,QACd,MAAM;AAAA,QACN,QAAQ;AAAA,QACR,MAAM;AAAA,MAAA;AAED,aAAA,QAAQ,UAAU,KAAK;AAAA,IAAA;AAIhC,UAAM,eAAe,MAAM;AACrB,UAAA,qBAAqB,QAAQ,GAAG;AAEnB,uBAAA,qBAAqB,QAAQ,CAAC;AAAA,MAC/C;AAAA,IAAA;AAIF,UAAM,eAAe,MAAM;AACzB,UAAI,eAAe,OAAO;AAET;MAAA,OACV;AACU,uBAAA,qBAAqB,QAAQ,CAAC;AAAA,MAC/C;AAAA,IAAA;AAII,UAAA,iBAAiB,CAAC,UAAkB;AAExC,sBAAgB,QAAQ;AAAA,IAAA;AAIpB,UAAA,qBAAqB,CAAC,UAAkB;;AACtC,YAAA,UAAU,CAAC,aAAa;AAE1B,UAAA,UAAU,qBAAqB,OAAO;AACxC,gBAAQ,KAAK,SAAS;AAAA,MAAA,YACb,oBAAe,UAAf,mBAAsB,SAAQ,eAAU,MAAM,KAAK,MAArB,mBAAwB,KAAK;AACpE,gBAAQ,KAAK,UAAU;AAAA,MAAA,OAClB;AACL,gBAAQ,KAAK,YAAY;AAAA,MAC3B;AAEO,aAAA,QAAQ,KAAK,GAAG;AAAA,IAAA;AAIzB,UAAM,iBAAiB,MAAM;AAC3B,UAAI,CAAC,eAAe;AAAO;AAG3B,UAAI,OAAO;AACT,sBAAc,KAAK;AACX,gBAAA;AAAA,MACV;AAGM,YAAA,mBAAmB,WAAW;AAEpC,UAAI,kBAAkB;AAEX,iBAAA,WAAWC,+BAAW,eAAe;AAAA,UAC5C,WAAW,iBAAiB;AAAA,QAAA,CAC7B;AAAA,MACH;AAAA,IAAA;AAIF,UAAM,aAAa,MAAM;AACvB,oBAAc,QAAQ;AAAA,IAAA;AAIxB,UAAM,cAAc,MAAM;AAExB,iBAAW,oBAAoB;AAG/B,UAAI,OAAO;AACT,sBAAc,KAAK;AACX,gBAAA;AAAA,MACV;AAEA,eAAS,aAAa;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC1bxB,GAAG,WAAW,eAAe;"}