/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.status-tag.data-v-4f0e1aea {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8rpx 16rpx;
  border-radius: 24rpx;
  font-size: 24rpx;
  font-weight: 500;
  line-height: 1;
}
.status-text.data-v-4f0e1aea {
  font-size: inherit;
  font-weight: inherit;
}
.status-primary.data-v-4f0e1aea {
  background-color: rgba(46, 139, 87, 0.1);
  color: #2E8B57;
}
.status-success.data-v-4f0e1aea {
  background-color: rgba(82, 196, 26, 0.1);
  color: #52C41A;
}
.status-warning.data-v-4f0e1aea {
  background-color: rgba(250, 173, 20, 0.1);
  color: #FAAD14;
}
.status-error.data-v-4f0e1aea {
  background-color: rgba(245, 34, 45, 0.1);
  color: #F5222D;
}
.status-gray.data-v-4f0e1aea {
  background-color: rgba(138, 138, 138, 0.1);
  color: #8A8A8A;
}