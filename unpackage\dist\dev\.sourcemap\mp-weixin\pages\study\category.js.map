{"version": 3, "file": "category.js", "sources": ["pages/study/category.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc3R1ZHkvY2F0ZWdvcnkudnVl"], "sourcesContent": ["<template>\n  <view class=\"category-container\">\n    <!-- 自定义导航栏 -->\n    <u-navbar \n      title=\"选择题库\" \n      :autoBack=\"true\"\n      :background=\"{ background: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)' }\"\n      titleStyle=\"color: #fff; font-weight: bold;\"\n    />\n    \n    <!-- 练习次数提示 -->\n    <view v-if=\"userStore.isLoggedIn\" class=\"practice-limit-banner\">\n      <view class=\"banner-content\">\n        <u-icon name=\"info-circle\" color=\"#4A90E2\" size=\"32\" />\n        <view class=\"banner-text\">\n          <text v-if=\"userStore.isAuthenticated\" class=\"limit-text unlimited\">\n            认证用户可无限练习\n          </text>\n          <text v-else-if=\"studyStore.canPracticeToday\" class=\"limit-text\">\n            今日还可练习 {{ studyStore.remainingPracticeCount }} 组\n          </text>\n          <text v-else class=\"limit-text limited\">\n            今日练习次数已用完，明日可继续\n          </text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 加载状态 -->\n    <LoadingSpinner v-if=\"isLoading\" text=\"加载题库分类...\" />\n    \n    <!-- 错误状态 -->\n    <EmptyState \n      v-else-if=\"error\"\n      type=\"no-network\"\n      :title=\"error\"\n      description=\"请检查网络连接后重试\"\n      :showButton=\"true\"\n      buttonText=\"重新加载\"\n      @buttonClick=\"loadCategories\"\n    />\n    \n    <!-- 分类列表 -->\n    <view v-else class=\"category-list\">\n      <!-- 空状态 -->\n      <EmptyState \n        v-if=\"categories.length === 0\"\n        type=\"no-data\"\n        title=\"暂无题库\"\n        description=\"题库内容正在建设中\"\n        :showButton=\"false\"\n      />\n      \n      <!-- 分类卡片 -->\n      <view v-else>\n        <view \n          v-for=\"category in categories\" \n          :key=\"category.id\"\n          class=\"category-card\"\n          @click=\"selectCategory(category)\"\n        >\n          <view class=\"card-header\">\n            <view class=\"category-icon\">\n              <u-icon :name=\"getCategoryIcon(category.name)\" color=\"#4A90E2\" size=\"60\" />\n            </view>\n            <view class=\"category-info\">\n              <text class=\"category-name\">{{ category.name }}</text>\n              <text class=\"category-desc\">{{ category.description || '专业知识练习' }}</text>\n            </view>\n            <view class=\"category-meta\">\n              <text class=\"question-count\">{{ category.questionCount }}题</text>\n              <u-icon name=\"arrow-right\" color=\"#4A90E2\" size=\"32\" />\n            </view>\n          </view>\n          \n          <view class=\"card-footer\">\n            <view class=\"difficulty-tags\">\n              <view class=\"tag easy\">\n                <text>基础</text>\n              </view>\n              <view class=\"tag medium\">\n                <text>进阶</text>\n              </view>\n              <view class=\"tag hard\">\n                <text>高级</text>\n              </view>\n            </view>\n            <view class=\"practice-info\">\n              <text class=\"practice-text\">{{ getPracticeInfo(category) }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部说明 -->\n    <view class=\"bottom-tips\">\n      <view class=\"tips-content\">\n        <u-icon name=\"lightbulb\" color=\"#FF9500\" size=\"32\" />\n        <view class=\"tips-text\">\n          <text class=\"tips-title\">练习说明</text>\n          <text class=\"tips-desc\">每组练习包含10道题目，完成后可查看答案解析。建议先从基础题目开始练习。</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport { useUserStore } from '../../src/stores/user'\nimport { useAppStore } from '../../src/stores/app'\nimport { useStudyStore } from '../../src/stores/study'\nimport { permissionManager } from '../../src/utils/permission'\nimport { PAGE_PATHS, PRACTICE_CONFIG } from '../../src/constants'\nimport api from '../../src/api'\nimport type { QuestionCategory } from '../../src/types'\n\n// 导入组件\nimport LoadingSpinner from '../../src/components/common/LoadingSpinner.vue'\nimport EmptyState from '../../src/components/common/EmptyState.vue'\n\n// Store\nconst userStore = useUserStore()\nconst appStore = useAppStore()\nconst studyStore = useStudyStore()\n\n// 响应式数据\nconst isLoading = ref(true)\nconst error = ref('')\n\n// 计算属性\nconst categories = computed(() => studyStore.categories)\n\nonMounted(() => {\n  loadCategories()\n})\n\n// 加载分类数据\nconst loadCategories = async () => {\n  isLoading.value = true\n  error.value = ''\n  \n  try {\n    const response = await api.study.getCategories()\n    studyStore.setCategories(response.data)\n  } catch (err: any) {\n    uni.__f__('error','at pages/study/category.vue:148','加载题库分类失败:', err)\n    error.value = err.message || '加载失败'\n  } finally {\n    isLoading.value = false\n  }\n}\n\n// 获取分类图标\nconst getCategoryIcon = (categoryName: string) => {\n  const iconMap: Record<string, string> = {\n    '疾病预防': 'shield',\n    '健康教育': 'book',\n    '流行病学': 'search',\n    '环境卫生': 'leaf',\n    '职业卫生': 'briefcase',\n    '营养与食品卫生': 'apple',\n    '儿童保健': 'baby',\n    '妇女保健': 'female',\n    '慢性病防控': 'heart',\n    '传染病防控': 'virus'\n  }\n  \n  return iconMap[categoryName] || 'file-text'\n}\n\n// 获取练习信息\nconst getPracticeInfo = (category: QuestionCategory) => {\n  if (!userStore.isLoggedIn) {\n    return '登录后可练习'\n  }\n  \n  if (userStore.isAuthenticated) {\n    return '无限练习'\n  }\n  \n  if (!studyStore.canPracticeToday) {\n    return '今日已达上限'\n  }\n  \n  return '可免费练习'\n}\n\n// 选择分类\nconst selectCategory = async (category: QuestionCategory) => {\n  // 检查登录状态\n  if (!userStore.isLoggedIn) {\n    appStore.showModal({\n      title: '需要登录',\n      content: '请先登录后再进行题库练习',\n      confirmText: '立即登录',\n      cancelText: '取消'\n    }).then((confirmed) => {\n      if (confirmed) {\n        appStore.redirectTo(PAGE_PATHS.LOGIN)\n      }\n    })\n    return\n  }\n  \n  // 检查练习次数\n  if (!userStore.isAuthenticated && !studyStore.canPracticeToday) {\n    appStore.showModal({\n      title: '今日练习已达上限',\n      content: `免费用户每天可练习${PRACTICE_CONFIG.FREE_SESSIONS_PER_DAY}组题目。完善个人资料并通过机构审核后可享受无限练习特权。`,\n      confirmText: '完善资料',\n      cancelText: '我知道了'\n    }).then((confirmed) => {\n      if (confirmed) {\n        appStore.switchTab(PAGE_PATHS.PERSONAL)\n      }\n    })\n    return\n  }\n  \n  // 检查题目数量\n  if (category.questionCount < PRACTICE_CONFIG.QUESTIONS_PER_SESSION) {\n    appStore.showToast(`该分类题目不足${PRACTICE_CONFIG.QUESTIONS_PER_SESSION}道，暂时无法练习`)\n    return\n  }\n  \n  // 开始练习\n  try {\n    appStore.showLoading('准备题目...')\n    \n    // 获取题目\n    const response = await api.study.getQuestions(category.id, PRACTICE_CONFIG.QUESTIONS_PER_SESSION)\n    \n    if (response.data.length < PRACTICE_CONFIG.QUESTIONS_PER_SESSION) {\n      appStore.hideLoading()\n      appStore.showToast('题目数量不足，请稍后再试')\n      return\n    }\n    \n    // 创建练习会话\n    const session = studyStore.startPracticeSession(category.id, response.data)\n    \n    appStore.hideLoading()\n    \n    // 跳转到练习页面\n    appStore.navigateTo(PAGE_PATHS.STUDY_PRACTICE, { \n      sessionId: session.id,\n      categoryName: category.name \n    })\n  } catch (err: any) {\n    appStore.hideLoading()\n    uni.__f__('error','at pages/study/category.vue:253','开始练习失败:', err)\n    appStore.showToast(err.message || '开始练习失败，请重试')\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../src/styles/global.scss';\n\n.category-container {\n  min-height: 100vh;\n  background: $acdc-bg-primary;\n}\n\n.practice-limit-banner {\n  margin: 24rpx;\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 24rpx 32rpx;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);\n  \n  .banner-content {\n    display: flex;\n    align-items: center;\n    gap: 16rpx;\n    \n    .banner-text {\n      flex: 1;\n      \n      .limit-text {\n        font-size: 26rpx;\n        \n        &.unlimited {\n          color: #4CAF50;\n          font-weight: bold;\n        }\n        \n        &.limited {\n          color: #f56c6c;\n        }\n      }\n    }\n  }\n}\n\n.category-list {\n  padding: 24rpx;\n  \n  .category-card {\n    background: #fff;\n    border-radius: 24rpx;\n    margin-bottom: 24rpx;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n    overflow: hidden;\n    \n    .card-header {\n      display: flex;\n      align-items: center;\n      padding: 40rpx;\n      \n      .category-icon {\n        width: 120rpx;\n        height: 120rpx;\n        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);\n        border-radius: 20rpx;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin-right: 32rpx;\n      }\n      \n      .category-info {\n        flex: 1;\n        \n        .category-name {\n          display: block;\n          font-size: 32rpx;\n          font-weight: bold;\n          color: #333;\n          margin-bottom: 12rpx;\n        }\n        \n        .category-desc {\n          font-size: 26rpx;\n          color: #666;\n          line-height: 1.4;\n        }\n      }\n      \n      .category-meta {\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        gap: 8rpx;\n        \n        .question-count {\n          font-size: 24rpx;\n          color: #4A90E2;\n          font-weight: bold;\n        }\n      }\n    }\n    \n    .card-footer {\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      padding: 24rpx 40rpx;\n      background: #f8f9fa;\n      border-top: 2rpx solid #f0f0f0;\n      \n      .difficulty-tags {\n        display: flex;\n        gap: 12rpx;\n        \n        .tag {\n          padding: 8rpx 16rpx;\n          border-radius: 12rpx;\n          font-size: 20rpx;\n          \n          &.easy {\n            background: #f6ffed;\n            color: #4CAF50;\n            border: 1rpx solid #b7eb8f;\n          }\n          \n          &.medium {\n            background: #fff7e6;\n            color: #FF9500;\n            border: 1rpx solid #ffd591;\n          }\n          \n          &.hard {\n            background: #fff2f0;\n            color: #f56c6c;\n            border: 1rpx solid #ffccc7;\n          }\n        }\n      }\n      \n      .practice-info {\n        .practice-text {\n          font-size: 24rpx;\n          color: #4A90E2;\n          font-weight: bold;\n        }\n      }\n    }\n  }\n}\n\n.bottom-tips {\n  margin: 24rpx;\n  background: #fff;\n  border-radius: 16rpx;\n  padding: 32rpx;\n  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);\n  \n  .tips-content {\n    display: flex;\n    align-items: flex-start;\n    gap: 16rpx;\n    \n    .tips-text {\n      flex: 1;\n      \n      .tips-title {\n        display: block;\n        font-size: 28rpx;\n        font-weight: bold;\n        color: #333;\n        margin-bottom: 12rpx;\n      }\n      \n      .tips-desc {\n        font-size: 26rpx;\n        color: #666;\n        line-height: 1.5;\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/study/category.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useAppStore", "useStudyStore", "ref", "computed", "onMounted", "api", "uni", "PAGE_PATHS", "PRACTICE_CONFIG"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuHA,MAAA,iBAA2B,MAAA;AAC3B,MAAA,aAAuB,MAAA;;;;AAGvB,UAAM,YAAYA,gBAAAA;AAClB,UAAM,WAAWC,eAAAA;AACjB,UAAM,aAAaC,iBAAAA;AAGb,UAAA,YAAYC,kBAAI,IAAI;AACpB,UAAA,QAAQA,kBAAI,EAAE;AAGpB,UAAM,aAAaC,cAAA,SAAS,MAAM,WAAW,UAAU;AAEvDC,kBAAAA,UAAU,MAAM;AACC;IAAA,CAChB;AAGD,UAAM,iBAAiB,MAAY;AACjC,gBAAU,QAAQ;AAClB,YAAM,QAAQ;AAEV,UAAA;AACF,cAAM,WAAW,MAAMC,cAAAA,IAAI,MAAM,cAAc;AACpC,mBAAA,cAAc,SAAS,IAAI;AAAA,eAC/B,KAAU;AACjBC,sBAAA,MAAI,MAAM,SAAQ,mCAAkC,aAAa,GAAG;AAC9D,cAAA,QAAQ,IAAI,WAAW;AAAA,MAAA,UAC7B;AACA,kBAAU,QAAQ;AAAA,MACpB;AAAA,IAAA;AAII,UAAA,kBAAkB,CAAC,iBAAyB;AAChD,YAAM,UAAkC;AAAA,QACtC,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,WAAW;AAAA,QACX,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,SAAS;AAAA,MAAA;AAGJ,aAAA,QAAQ,YAAY,KAAK;AAAA,IAAA;AAI5B,UAAA,kBAAkB,CAAC,aAA+B;AAClD,UAAA,CAAC,UAAU,YAAY;AAClB,eAAA;AAAA,MACT;AAEA,UAAI,UAAU,iBAAiB;AACtB,eAAA;AAAA,MACT;AAEI,UAAA,CAAC,WAAW,kBAAkB;AACzB,eAAA;AAAA,MACT;AAEO,aAAA;AAAA,IAAA;AAIH,UAAA,iBAAiB,CAAO,aAA+B;AAEvD,UAAA,CAAC,UAAU,YAAY;AACzB,iBAAS,UAAU;AAAA,UACjB,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,QAAA,CACb,EAAE,KAAK,CAAC,cAAc;AACrB,cAAI,WAAW;AACJ,qBAAA,WAAWC,+BAAW,KAAK;AAAA,UACtC;AAAA,QAAA,CACD;AACD;AAAA,MACF;AAGA,UAAI,CAAC,UAAU,mBAAmB,CAAC,WAAW,kBAAkB;AAC9D,iBAAS,UAAU;AAAA,UACjB,OAAO;AAAA,UACP,SAAS,YAAYC,oCAAgB,qBAAqB;AAAA,UAC1D,aAAa;AAAA,UACb,YAAY;AAAA,QAAA,CACb,EAAE,KAAK,CAAC,cAAc;AACrB,cAAI,WAAW;AACJ,qBAAA,UAAUD,+BAAW,QAAQ;AAAA,UACxC;AAAA,QAAA,CACD;AACD;AAAA,MACF;AAGI,UAAA,SAAS,gBAAgBC,oBAAA,gBAAgB,uBAAuB;AAClE,iBAAS,UAAU,UAAUA,oBAAgB,gBAAA,qBAAqB,UAAU;AAC5E;AAAA,MACF;AAGI,UAAA;AACF,iBAAS,YAAY,SAAS;AAGxB,cAAA,WAAW,MAAMH,cAAI,IAAA,MAAM,aAAa,SAAS,IAAIG,oCAAgB,qBAAqB;AAEhG,YAAI,SAAS,KAAK,SAASA,oBAAAA,gBAAgB,uBAAuB;AAChE,mBAAS,YAAY;AACrB,mBAAS,UAAU,cAAc;AACjC;AAAA,QACF;AAGA,cAAM,UAAU,WAAW,qBAAqB,SAAS,IAAI,SAAS,IAAI;AAE1E,iBAAS,YAAY;AAGZ,iBAAA,WAAWD,+BAAW,gBAAgB;AAAA,UAC7C,WAAW,QAAQ;AAAA,UACnB,cAAc,SAAS;AAAA,QAAA,CACxB;AAAA,eACM,KAAU;AACjB,iBAAS,YAAY;AACrBD,sBAAA,MAAI,MAAM,SAAQ,mCAAkC,WAAW,GAAG;AACzD,iBAAA,UAAU,IAAI,WAAW,YAAY;AAAA,MAChD;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7PF,GAAG,WAAW,eAAe;"}