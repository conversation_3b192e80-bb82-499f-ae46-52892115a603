"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const src_constants_index = require("../constants/index.js");
class Request {
  constructor() {
    this.responseInterceptors = [];
    this.baseURL = src_constants_index.API_CONFIG.BASE_URL;
    this.timeout = src_constants_index.API_CONFIG.TIMEOUT;
    this.defaultHeaders = {
      "Content-Type": "application/json"
    };
  }
  // 添加响应拦截器
  addResponseInterceptor(interceptor) {
    this.responseInterceptors.push(interceptor);
  }
  // 获取请求头
  getHeaders(customHeaders) {
    const headers = __spreadValues(__spreadValues({}, this.defaultHeaders), customHeaders);
    const token = common_vendor.index.getStorageSync(src_constants_index.STORAGE_KEYS.TOKEN);
    if (token) {
      headers.Authorization = `Bearer ${token}`;
    }
    return headers;
  }
  // 处理响应
  handleResponse(response) {
    return __async(this, null, function* () {
      let processedResponse = response;
      for (const interceptor of this.responseInterceptors) {
        if (interceptor.onSuccess) {
          processedResponse = yield interceptor.onSuccess(processedResponse);
        }
      }
      const { statusCode, data } = processedResponse;
      if (statusCode >= 200 && statusCode < 300) {
        if (data.code === 0 || data.code === 200) {
          return data;
        } else {
          throw new Error(data.message || "请求失败");
        }
      } else {
        throw new Error(this.getErrorMessage(statusCode));
      }
    });
  }
  // 处理错误
  handleError(error) {
    return __async(this, null, function* () {
      for (const interceptor of this.responseInterceptors) {
        if (interceptor.onError) {
          yield interceptor.onError(error);
        }
      }
      throw error;
    });
  }
  // 获取错误信息
  getErrorMessage(statusCode) {
    return src_constants_index.ERROR_MESSAGES[statusCode] || `请求失败 (${statusCode})`;
  }
  // 显示加载提示
  showLoading(text = "加载中...") {
    common_vendor.index.showLoading({
      title: text,
      mask: true
    });
  }
  // 隐藏加载提示
  hideLoading() {
    common_vendor.index.hideLoading();
  }
  // 显示错误提示
  showError(message) {
    common_vendor.index.showToast({
      title: message,
      icon: "none",
      duration: 2e3
    });
  }
  // 通用请求方法
  request(config) {
    return __async(this, null, function* () {
      const {
        url,
        method = "GET",
        data,
        header,
        timeout = this.timeout,
        showLoading = false,
        loadingText = "加载中...",
        showError = true
      } = config;
      if (showLoading) {
        this.showLoading(loadingText);
      }
      try {
        const response = yield common_vendor.index.request({
          url: this.baseURL + url,
          method,
          data,
          header: this.getHeaders(header),
          timeout
        });
        const result = yield this.handleResponse(response);
        return result;
      } catch (error) {
        if (showError) {
          this.showError(error.message || "网络请求失败");
        }
        return this.handleError(error);
      } finally {
        if (showLoading) {
          this.hideLoading();
        }
      }
    });
  }
  // GET请求
  get(url, params, config) {
    let fullUrl = url;
    if (params) {
      const query = Object.keys(params).map((key) => `${key}=${encodeURIComponent(params[key])}`).join("&");
      fullUrl += `?${query}`;
    }
    return this.request(__spreadValues({
      url: fullUrl,
      method: "GET"
    }, config));
  }
  // POST请求
  post(url, data, config) {
    return this.request(__spreadValues({
      url,
      method: "POST",
      data
    }, config));
  }
  // PUT请求
  put(url, data, config) {
    return this.request(__spreadValues({
      url,
      method: "PUT",
      data
    }, config));
  }
  // DELETE请求
  delete(url, config) {
    return this.request(__spreadValues({
      url,
      method: "DELETE"
    }, config));
  }
  // 文件上传
  upload(config) {
    return __async(this, null, function* () {
      const {
        url,
        filePath,
        name,
        formData,
        showLoading = true,
        loadingText = "上传中..."
      } = config;
      if (showLoading) {
        this.showLoading(loadingText);
      }
      try {
        const response = yield common_vendor.index.uploadFile({
          url: this.baseURL + url,
          filePath,
          name,
          formData,
          header: this.getHeaders()
        });
        const result = JSON.parse(response.data);
        if (result.code === 0 || result.code === 200) {
          return result;
        } else {
          throw new Error(result.message || "上传失败");
        }
      } catch (error) {
        this.showError(error.message || "上传失败");
        throw error;
      } finally {
        if (showLoading) {
          this.hideLoading();
        }
      }
    });
  }
}
const request = new Request();
request.addResponseInterceptor({
  onError: (error) => __async(exports, null, function* () {
    var _a, _b;
    if (((_a = error.message) == null ? void 0 : _a.includes("401")) || ((_b = error.message) == null ? void 0 : _b.includes("token"))) {
      common_vendor.index.removeStorageSync(src_constants_index.STORAGE_KEYS.TOKEN);
      common_vendor.index.removeStorageSync(src_constants_index.STORAGE_KEYS.USER_INFO);
      common_vendor.index.reLaunch({
        url: "/pages/login/login"
      });
    }
  })
});
exports.request = request;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/src/utils/request.js.map
