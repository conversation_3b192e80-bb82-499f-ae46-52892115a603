"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
const src_stores_app = require("../../../src/stores/app.js");
const src_api_index = require("../../../src/api/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_textarea2 = common_vendor.resolveComponent("u-textarea");
  const _easycom_u_input2 = common_vendor.resolveComponent("u-input");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  (_easycom_u_navbar2 + _easycom_u_icon2 + _easycom_u_textarea2 + _easycom_u_input2 + _easycom_u_button2 + _easycom_u_modal2)();
}
const _easycom_u_navbar = () => "../../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_textarea = () => "../../../uni_modules/uview-plus/components/u-textarea/u-textarea.js";
const _easycom_u_input = () => "../../../uni_modules/uview-plus/components/u-input/u-input.js";
const _easycom_u_button = () => "../../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_u_modal = () => "../../../uni_modules/uview-plus/components/u-modal/u-modal.js";
if (!Math) {
  (_easycom_u_navbar + _easycom_u_icon + _easycom_u_textarea + _easycom_u_input + _easycom_u_button + _easycom_u_modal)();
}
const maxImages = 3;
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "feedback",
  setup(__props) {
    const appStore = src_stores_app.useAppStore();
    const formData = common_vendor.ref({
      type: "",
      content: "",
      contact: "",
      images: []
    });
    const isSubmitting = common_vendor.ref(false);
    const showSuccessModal = common_vendor.ref(false);
    const expandedFaq = common_vendor.ref("");
    const feedbackTypes = [
      { key: "bug", label: "功能异常", icon: "error-circle" },
      { key: "suggestion", label: "功能建议", icon: "lightbulb" },
      { key: "content", label: "内容问题", icon: "file-text" },
      { key: "other", label: "其他问题", icon: "help-circle" }
    ];
    const faqList = [
      {
        id: "login",
        question: "无法登录怎么办？",
        answer: "请检查网络连接，确保微信授权正常。如仍无法登录，请联系技术支持。"
      },
      {
        id: "practice",
        question: "为什么无法练习题目？",
        answer: "练习功能需要完善个人资料并通过审核。未认证用户每日有练习次数限制。"
      },
      {
        id: "exam",
        question: "考试时遇到技术问题怎么办？",
        answer: "考试过程中如遇技术问题，请立即联系考务人员或拨打技术支持电话。"
      },
      {
        id: "certificate",
        question: "证书什么时候能下载？",
        answer: "考试通过后，证书会在5个工作日内生成并可下载。"
      }
    ];
    const canSubmit = common_vendor.computed(() => {
      return formData.value.type && formData.value.content.trim().length >= 10;
    });
    const chooseImage = () => {
      const remainingCount = maxImages - formData.value.images.length;
      common_vendor.index.chooseImage({
        count: remainingCount,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const validImages = [];
          res.tempFilePaths.forEach((path) => {
            common_vendor.index.getFileInfo({
              filePath: path,
              success: (info) => {
                if (info.size <= 5 * 1024 * 1024) {
                  validImages.push(path);
                } else {
                  appStore.showToast("图片大小不能超过5MB");
                }
              }
            });
          });
          setTimeout(() => {
            formData.value.images.push(...validImages);
          }, 100);
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at subpages/personal/feedback/feedback.vue:243", "选择图片失败:", error);
          appStore.showToast("选择图片失败");
        }
      });
    };
    const removeImage = (index) => {
      formData.value.images.splice(index, 1);
    };
    const toggleFaq = (faqId) => {
      expandedFaq.value = expandedFaq.value === faqId ? "" : faqId;
    };
    const submitFeedback = () => __async(this, null, function* () {
      if (!canSubmit.value) {
        appStore.showToast("请选择反馈类型并填写问题描述");
        return;
      }
      if (formData.value.content.trim().length < 10) {
        appStore.showToast("问题描述至少需要10个字符");
        return;
      }
      isSubmitting.value = true;
      try {
        const imageUrls = [];
        for (const imagePath of formData.value.images) {
          try {
            imageUrls.push(imagePath);
          } catch (uploadError) {
            common_vendor.index.__f__("error", "at subpages/personal/feedback/feedback.vue:285", "图片上传失败:", uploadError);
          }
        }
        yield src_api_index.api.feedback.submitFeedback({
          type: formData.value.type,
          content: formData.value.content,
          contact: formData.value.contact,
          images: imageUrls
        });
        showSuccessModal.value = true;
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/personal/feedback/feedback.vue:299", "提交反馈失败:", error);
        appStore.showToast(error.message || "提交失败，请重试");
      } finally {
        isSubmitting.value = false;
      }
    });
    const handleSuccessConfirm = () => {
      showSuccessModal.value = false;
      formData.value = {
        type: "",
        content: "",
        contact: "",
        images: []
      };
      setTimeout(() => {
        appStore.navigateBack();
      }, 500);
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "意见反馈",
          autoBack: true,
          background: {
            background: "linear-gradient(135deg, #2E8B57 0%, #228B22 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: common_vendor.f(feedbackTypes, (type, k0, i0) => {
          return {
            a: "3a7560c1-1-" + i0,
            b: common_vendor.p({
              name: type.icon,
              color: formData.value.type === type.key ? "#fff" : "#2E8B57",
              size: "32"
            }),
            c: common_vendor.t(type.label),
            d: type.key,
            e: formData.value.type === type.key ? 1 : "",
            f: common_vendor.o(($event) => formData.value.type = type.key, type.key)
          };
        }),
        c: common_vendor.o(($event) => formData.value.content = $event),
        d: common_vendor.p({
          placeholder: "请详细描述您遇到的问题或建议，我们会认真处理每一条反馈",
          maxlength: 500,
          showWordLimit: true,
          height: "300",
          autoHeight: true,
          modelValue: formData.value.content
        }),
        e: common_vendor.o(($event) => formData.value.contact = $event),
        f: common_vendor.p({
          placeholder: "请输入手机号或邮箱，便于我们联系您",
          border: false,
          customStyle: {
            backgroundColor: "#f8f9fa",
            padding: "24rpx"
          },
          modelValue: formData.value.contact
        }),
        g: common_vendor.f(formData.value.images, (image, index, i0) => {
          return {
            a: image,
            b: "3a7560c1-4-" + i0,
            c: common_vendor.o(($event) => removeImage(index), index),
            d: index
          };
        }),
        h: common_vendor.p({
          name: "close",
          color: "#fff",
          size: "24"
        }),
        i: formData.value.images.length < maxImages
      }, formData.value.images.length < maxImages ? {
        j: common_vendor.p({
          name: "camera",
          color: "#2E8B57",
          size: "40"
        }),
        k: common_vendor.t(formData.value.images.length),
        l: common_vendor.t(maxImages),
        m: common_vendor.o(chooseImage)
      } : {}, {
        n: common_vendor.t(maxImages),
        o: common_vendor.f(faqList, (faq, k0, i0) => {
          return common_vendor.e({
            a: common_vendor.t(faq.question),
            b: "3a7560c1-6-" + i0,
            c: common_vendor.p({
              name: expandedFaq.value === faq.id ? "arrow-up" : "arrow-down",
              color: "#666",
              size: "24"
            }),
            d: expandedFaq.value === faq.id
          }, expandedFaq.value === faq.id ? {
            e: common_vendor.t(faq.answer)
          } : {}, {
            f: faq.id,
            g: common_vendor.o(($event) => toggleFaq(faq.id), faq.id)
          });
        }),
        p: common_vendor.o(submitFeedback),
        q: common_vendor.p({
          type: "primary",
          loading: isSubmitting.value,
          loadingText: "提交中..."
        }),
        r: common_vendor.o(handleSuccessConfirm),
        s: common_vendor.o(($event) => showSuccessModal.value = $event),
        t: common_vendor.p({
          title: "提交成功",
          content: "感谢您的反馈！我们会在3个工作日内处理并回复您。",
          showCancelButton: false,
          confirmText: "我知道了",
          modelValue: showSuccessModal.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-3a7560c1"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpages/personal/feedback/feedback.js.map
