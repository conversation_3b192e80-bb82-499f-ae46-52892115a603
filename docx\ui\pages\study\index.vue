<template>
  <view class="study-center-container">
    <!-- 状态栏安全区域 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 自定义头部 -->
    <view class="header">
      <view class="header-content">
        <text class="page-title">学习中心</text>
        <view class="header-actions">
          <view class="study-stats">
            <text class="stats-text">今日已练习 {{ todayPracticeCount }}/3 组</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 学习统计卡片 -->
      <view class="stats-card">
        <view class="stats-item">
          <text class="stats-number">{{ totalPracticeCount }}</text>
          <text class="stats-label">累计练习</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ correctRate }}%</text>
          <text class="stats-label">正确率</text>
        </view>
        <view class="stats-divider"></view>
        <view class="stats-item">
          <text class="stats-number">{{ studyDays }}</text>
          <text class="stats-label">学习天数</text>
        </view>
      </view>
      
      <!-- 学习模块 -->
      <view class="study-modules">
        <!-- 教材学习模块（预留） -->
        <view class="module-card disabled" @click="handleTextbookClick">
          <view class="module-icon textbook">
            <u-icon name="book" color="#4A90E2" size="80" />
            <view class="coming-soon-badge">
              <text>即将上线</text>
            </view>
          </view>
          <view class="module-content">
            <text class="module-title">教材学习</text>
            <text class="module-desc">系统性学习专业教材内容</text>
            <text class="module-status">功能建设中，敬请期待</text>
          </view>
          <u-icon name="arrow-right" color="#c0c4cc" size="32" />
        </view>
        
        <!-- 题库练习模块 -->
        <view class="module-card" @click="handleQuestionBankClick">
          <view class="module-icon question-bank">
            <u-icon name="edit-pen" color="#FF9500" size="80" />
            <view v-if="todayPracticeCount >= 3" class="limit-badge">
              <text>今日已达上限</text>
            </view>
          </view>
          <view class="module-content">
            <text class="module-title">题库练习</text>
            <text class="module-desc">分类题库专项练习，即时反馈</text>
            <text class="module-info">{{ getQuestionBankInfo() }}</text>
          </view>
          <u-icon name="arrow-right" color="#4A90E2" size="32" />
        </view>
      </view>
      
      <!-- VIP特权预告（预留） -->
      <view class="vip-preview">
        <view class="vip-card">
          <view class="vip-header">
            <view class="vip-icon">
              <u-icon name="diamond" color="#FFD700" size="60" />
            </view>
            <view class="vip-content">
              <text class="vip-title">VIP会员特权</text>
              <text class="vip-desc">无限练习次数 · 更多题库内容 · 专属学习报告</text>
            </view>
          </view>
          <view class="vip-features">
            <view class="feature-item">
              <u-icon name="checkmark-circle" color="#4CAF50" size="32" />
              <text>无限制刷题练习</text>
            </view>
            <view class="feature-item">
              <u-icon name="checkmark-circle" color="#4CAF50" size="32" />
              <text>专享VIP题库</text>
            </view>
            <view class="feature-item">
              <u-icon name="checkmark-circle" color="#4CAF50" size="32" />
              <text>详细学习报告</text>
            </view>
          </view>
          <u-button 
            class="vip-btn" 
            type="warning"
            @click="handleVipClick"
          >
            即将开放，敬请期待
          </u-button>
        </view>
      </view>
      
      <!-- 最近练习记录 -->
      <view v-if="recentPractices.length > 0" class="recent-practices">
        <view class="section-header">
          <text class="section-title">最近练习</text>
          <text class="section-more" @click="viewAllPractices">查看全部</text>
        </view>
        
        <view class="practice-list">
          <view 
            v-for="practice in recentPractices" 
            :key="practice.id"
            class="practice-item"
            @click="viewPracticeDetail(practice)"
          >
            <view class="practice-info">
              <text class="practice-category">{{ practice.categoryName }}</text>
              <text class="practice-time">{{ formatTime(practice.completedAt) }}</text>
            </view>
            <view class="practice-result">
              <text class="score" :class="{ 'good': practice.score >= 80, 'normal': practice.score >= 60, 'poor': practice.score < 60 }">
                {{ practice.score }}分
              </text>
              <text class="accuracy">{{ practice.correctCount }}/{{ practice.totalCount }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'

// 系统信息
const statusBarHeight = ref(0)

// 学习统计数据
const todayPracticeCount = ref(2) // 今日已练习次数
const totalPracticeCount = ref(156) // 累计练习题数
const correctRate = ref(87) // 总体正确率
const studyDays = ref(15) // 学习天数

// 最近练习记录
const recentPractices = ref([
  {
    id: 1,
    categoryName: '接种门诊',
    completedAt: '2024-03-15 14:30:00',
    score: 90,
    correctCount: 9,
    totalCount: 10
  },
  {
    id: 2,
    categoryName: '产科',
    completedAt: '2024-03-15 10:15:00',
    score: 70,
    correctCount: 7,
    totalCount: 10
  },
  {
    id: 3,
    categoryName: '犬伤门诊',
    completedAt: '2024-03-14 16:45:00',
    score: 95,
    correctCount: 9,
    totalCount: 10
  }
])

onMounted(() => {
  // 获取系统信息 - 使用新的API
  try {
    // 优先使用新API
    if (uni.canIUse('getWindowInfo')) {
      const windowInfo = uni.getWindowInfo()
      statusBarHeight.value = windowInfo.statusBarHeight || 0
    } else {
      // 兼容旧版本
      const systemInfo = uni.getSystemInfoSync()
      statusBarHeight.value = systemInfo.statusBarHeight || 0
    }
  } catch (error) {
    console.error('获取系统信息失败:', error)
    statusBarHeight.value = 0
  }

  // 加载学习数据
  loadStudyData()
})

// 获取题库信息
const getQuestionBankInfo = () => {
  if (todayPracticeCount.value >= 3) {
    return '免费练习已用完，明日可继续'
  }
  return `免费练习 ${3 - todayPracticeCount.value} 组剩余`
}

// 教材点击事件
const handleTextbookClick = () => {
  uni.showToast({
    title: '教材功能正在建设中，敬请期待',
    icon: 'none',
    duration: 2000
  })
}

// 题库练习点击事件
const handleQuestionBankClick = () => {
  if (todayPracticeCount.value >= 3) {
    uni.showModal({
      title: '今日练习已达上限',
      content: '免费用户每天可练习3组题目，明天可继续练习。升级VIP可享受无限练习特权。',
      confirmText: '我知道了',
      showCancel: false
    })
    return
  }
  
  // 跳转到题库分类页面
  uni.navigateTo({
    url: '/pages/study/categories'
  })
}

// VIP点击事件
const handleVipClick = () => {
  uni.showModal({
    title: 'VIP功能即将上线',
    content: 'VIP会员功能正在开发中，上线后将为您提供更丰富的学习功能和特权。',
    confirmText: '期待上线',
    showCancel: false
  })
}

// 查看所有练习记录
const viewAllPractices = () => {
  uni.navigateTo({
    url: '/pages/study/history'
  })
}

// 查看练习详情
const viewPracticeDetail = (practice: any) => {
  uni.navigateTo({
    url: `/pages/study/practice-detail?id=${practice.id}`
  })
}

// 加载学习数据
const loadStudyData = async () => {
  try {
    // 这里应该调用真实的API获取学习数据
    // const response = await getStudyStats()
    // todayPracticeCount.value = response.todayCount
    // totalPracticeCount.value = response.totalCount
    // correctRate.value = response.correctRate
    // studyDays.value = response.studyDays
    
    console.log('学习数据加载完成')
  } catch (error) {
    console.error('加载学习数据失败:', error)
  }
}

// 格式化时间
const formatTime = (timeStr: string) => {
  const time = new Date(timeStr)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const targetDay = new Date(time.getFullYear(), time.getMonth(), time.getDate())
  
  if (targetDay.getTime() === today.getTime()) {
    return `今天 ${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`
  } else if (targetDay.getTime() === today.getTime() - 24 * 60 * 60 * 1000) {
    return `昨天 ${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`
  } else {
    return `${time.getMonth() + 1}-${time.getDate()} ${time.getHours().toString().padStart(2, '0')}:${time.getMinutes().toString().padStart(2, '0')}`
  }
}
</script>

<style lang="scss" scoped>
.study-center-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.status-bar {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.header {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  padding: 20rpx 30rpx 40rpx;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .page-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
    }
    
    .header-actions {
      .study-stats {
        .stats-text {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}

.main-content {
  padding: 30rpx;
  padding-bottom: 120rpx; // 为底部导航留空间
}

.stats-card {
  background: #fff;
  border-radius: 24rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  
  .stats-item {
    flex: 1;
    text-align: center;
    
    .stats-number {
      display: block;
      font-size: 48rpx;
      font-weight: bold;
      color: #4A90E2;
      margin-bottom: 8rpx;
    }
    
    .stats-label {
      font-size: 24rpx;
      color: #666;
    }
  }
  
  .stats-divider {
    width: 2rpx;
    height: 60rpx;
    background: #f0f0f0;
  }
}

.study-modules {
  margin-bottom: 40rpx;
  
  .module-card {
    background: #fff;
    border-radius: 24rpx;
    padding: 40rpx;
    margin-bottom: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    
    &.disabled {
      opacity: 0.6;
      
      .module-content {
        .module-status {
          color: #999;
          font-size: 24rpx;
        }
      }
    }
    
    .module-icon {
      position: relative;
      margin-right: 40rpx;
      
      &.textbook {
        padding: 24rpx;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 20rpx;
      }
      
      &.question-bank {
        padding: 24rpx;
        background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
        border-radius: 20rpx;
      }
      
      .coming-soon-badge {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        background: #FF9500;
        border-radius: 12rpx;
        padding: 4rpx 12rpx;
        
        text {
          font-size: 18rpx;
          color: #fff;
          font-weight: bold;
        }
      }
      
      .limit-badge {
        position: absolute;
        top: -10rpx;
        right: -10rpx;
        background: #f56c6c;
        border-radius: 12rpx;
        padding: 4rpx 12rpx;
        
        text {
          font-size: 18rpx;
          color: #fff;
          font-weight: bold;
        }
      }
    }
    
    .module-content {
      flex: 1;
      
      .module-title {
        display: block;
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 12rpx;
      }
      
      .module-desc {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 8rpx;
      }
      
      .module-info {
        display: block;
        font-size: 24rpx;
        color: #4A90E2;
      }
      
      .module-status {
        display: block;
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.vip-preview {
  margin-bottom: 40rpx;
  
  .vip-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 24rpx;
    padding: 40rpx;
    color: #fff;
    
    .vip-header {
      display: flex;
      align-items: center;
      margin-bottom: 40rpx;
      
      .vip-icon {
        margin-right: 24rpx;
      }
      
      .vip-content {
        flex: 1;
        
        .vip-title {
          display: block;
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 12rpx;
        }
        
        .vip-desc {
          font-size: 24rpx;
          opacity: 0.8;
        }
      }
    }
    
    .vip-features {
      margin-bottom: 40rpx;
      
      .feature-item {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        text {
          margin-left: 16rpx;
          font-size: 26rpx;
        }
      }
    }
    
    .vip-btn {
      width: 100%;
      height: 80rpx;
      border-radius: 40rpx;
      background: rgba(255, 255, 255, 0.2);
      color: #fff;
      border: 2rpx solid rgba(255, 255, 255, 0.3);
    }
  }
}

.recent-practices {
  .section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;
    
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
    
    .section-more {
      font-size: 26rpx;
      color: #4A90E2;
    }
  }
  
  .practice-list {
    .practice-item {
      background: #fff;
      border-radius: 16rpx;
      padding: 32rpx;
      margin-bottom: 16rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .practice-info {
        flex: 1;
        
        .practice-category {
          display: block;
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 8rpx;
        }
        
        .practice-time {
          font-size: 24rpx;
          color: #999;
        }
      }
      
      .practice-result {
        text-align: right;
        
        .score {
          display: block;
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 4rpx;
          
          &.good {
            color: #4CAF50;
          }
          
          &.normal {
            color: #FF9500;
          }
          
          &.poor {
            color: #f56c6c;
          }
        }
        
        .accuracy {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
}
</style> 