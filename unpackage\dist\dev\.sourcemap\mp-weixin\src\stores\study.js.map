{"version": 3, "file": "study.js", "sources": ["src/stores/study.ts"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport { ref, computed } from 'vue'\nimport type { QuestionCategory, Question, PracticeSession } from '../types'\nimport { STORAGE_KEYS, PRACTICE_CONFIG } from '../constants'\n\nexport const useStudyStore = defineStore('study', () => {\n  // 状态\n  const categories = ref<QuestionCategory[]>([])\n  const currentSession = ref<PracticeSession | null>(null)\n  const practiceHistory = ref<PracticeSession[]>([])\n  const dailyPracticeCount = ref<number>(0)\n  const lastPracticeDate = ref<string>('')\n\n  // 计算属性\n  const canPracticeToday = computed(() => {\n    const today = new Date().toDateString()\n    if (lastPracticeDate.value !== today) {\n      return true // 新的一天，重置次数\n    }\n    return dailyPracticeCount.value < PRACTICE_CONFIG.FREE_SESSIONS_PER_DAY\n  })\n\n  const remainingPracticeCount = computed(() => {\n    const today = new Date().toDateString()\n    if (lastPracticeDate.value !== today) {\n      return PRACTICE_CONFIG.FREE_SESSIONS_PER_DAY // 新的一天\n    }\n    return Math.max(0, PRACTICE_CONFIG.FREE_SESSIONS_PER_DAY - dailyPracticeCount.value)\n  })\n\n  const currentQuestionIndex = computed(() => {\n    if (!currentSession.value) return 0\n    return Object.keys(currentSession.value.answers).length\n  })\n\n  const currentQuestion = computed(() => {\n    if (!currentSession.value || currentQuestionIndex.value >= currentSession.value.questions.length) {\n      return null\n    }\n    return currentSession.value.questions[currentQuestionIndex.value]\n  })\n\n  const isSessionCompleted = computed(() => {\n    if (!currentSession.value) return false\n    return currentQuestionIndex.value >= currentSession.value.questions.length\n  })\n\n  // 方法\n  const setCategories = (newCategories: QuestionCategory[]) => {\n    categories.value = newCategories\n  }\n\n  const startPracticeSession = (categoryId: string, questions: Question[]) => {\n    const session: PracticeSession = {\n      id: Date.now().toString(),\n      categoryId,\n      questions,\n      answers: {},\n      totalCount: questions.length,\n      startTime: new Date().toISOString(),\n    }\n    \n    currentSession.value = session\n    saveCurrentSession()\n    return session\n  }\n\n  const answerQuestion = (questionId: string, answer: any) => {\n    if (!currentSession.value) return\n\n    currentSession.value.answers[questionId] = answer\n    saveCurrentSession()\n  }\n\n  const completeSession = () => {\n    if (!currentSession.value || !isSessionCompleted.value) return\n\n    // 计算得分\n    let correctCount = 0\n    currentSession.value.questions.forEach(question => {\n      const userAnswer = currentSession.value!.answers[question.id]\n      if (isAnswerCorrect(question, userAnswer)) {\n        correctCount++\n      }\n    })\n\n    currentSession.value.correctCount = correctCount\n    currentSession.value.score = Math.round((correctCount / currentSession.value.totalCount) * 100)\n    currentSession.value.endTime = new Date().toISOString()\n\n    // 添加到历史记录\n    practiceHistory.value.unshift({ ...currentSession.value })\n    \n    // 更新每日练习次数\n    updateDailyPracticeCount()\n    \n    // 保存数据\n    savePracticeHistory()\n    saveCurrentSession()\n\n    return currentSession.value\n  }\n\n  const isAnswerCorrect = (question: Question, userAnswer: any): boolean => {\n    if (!userAnswer) return false\n\n    switch (question.type) {\n      case 'single':\n      case 'judge':\n        return userAnswer === question.answer\n      case 'multiple':\n        if (!Array.isArray(userAnswer) || !Array.isArray(question.answer)) return false\n        return userAnswer.length === question.answer.length && \n               userAnswer.every(ans => question.answer.includes(ans))\n      case 'essay':\n        // 问答题需要人工评判，这里暂时返回true\n        return true\n      default:\n        return false\n    }\n  }\n\n  const updateDailyPracticeCount = () => {\n    const today = new Date().toDateString()\n    \n    if (lastPracticeDate.value !== today) {\n      // 新的一天，重置计数\n      dailyPracticeCount.value = 1\n      lastPracticeDate.value = today\n    } else {\n      // 同一天，增加计数\n      dailyPracticeCount.value++\n    }\n    \n    saveDailyPracticeData()\n  }\n\n  const clearCurrentSession = () => {\n    currentSession.value = null\n    uni.removeStorageSync(STORAGE_KEYS.PRACTICE_CACHE)\n  }\n\n  const saveCurrentSession = () => {\n    if (currentSession.value) {\n      try {\n        uni.setStorageSync(STORAGE_KEYS.PRACTICE_CACHE, JSON.stringify(currentSession.value))\n      } catch (error) {\n        console.error('Failed to save current session:', error)\n      }\n    }\n  }\n\n  const loadCurrentSession = () => {\n    try {\n      const cached = uni.getStorageSync(STORAGE_KEYS.PRACTICE_CACHE)\n      if (cached) {\n        currentSession.value = JSON.parse(cached)\n      }\n    } catch (error) {\n      console.error('Failed to load current session:', error)\n    }\n  }\n\n  const savePracticeHistory = () => {\n    try {\n      // 只保存最近50条记录\n      const historyToSave = practiceHistory.value.slice(0, 50)\n      uni.setStorageSync('practice_history', JSON.stringify(historyToSave))\n    } catch (error) {\n      console.error('Failed to save practice history:', error)\n    }\n  }\n\n  const loadPracticeHistory = () => {\n    try {\n      const history = uni.getStorageSync('practice_history')\n      if (history) {\n        practiceHistory.value = JSON.parse(history)\n      }\n    } catch (error) {\n      console.error('Failed to load practice history:', error)\n    }\n  }\n\n  const saveDailyPracticeData = () => {\n    try {\n      const data = {\n        count: dailyPracticeCount.value,\n        date: lastPracticeDate.value,\n      }\n      uni.setStorageSync('daily_practice', JSON.stringify(data))\n    } catch (error) {\n      console.error('Failed to save daily practice data:', error)\n    }\n  }\n\n  const loadDailyPracticeData = () => {\n    try {\n      const data = uni.getStorageSync('daily_practice')\n      if (data) {\n        const parsed = JSON.parse(data)\n        dailyPracticeCount.value = parsed.count || 0\n        lastPracticeDate.value = parsed.date || ''\n      }\n    } catch (error) {\n      console.error('Failed to load daily practice data:', error)\n    }\n  }\n\n  const initStudyStore = () => {\n    loadCurrentSession()\n    loadPracticeHistory()\n    loadDailyPracticeData()\n  }\n\n  const getSessionStats = () => {\n    const totalSessions = practiceHistory.value.length\n    const totalQuestions = practiceHistory.value.reduce((sum, session) => sum + session.totalCount, 0)\n    const totalCorrect = practiceHistory.value.reduce((sum, session) => sum + (session.correctCount || 0), 0)\n    const averageScore = totalSessions > 0 ? \n      practiceHistory.value.reduce((sum, session) => sum + (session.score || 0), 0) / totalSessions : 0\n\n    return {\n      totalSessions,\n      totalQuestions,\n      totalCorrect,\n      averageScore: Math.round(averageScore),\n      accuracy: totalQuestions > 0 ? Math.round((totalCorrect / totalQuestions) * 100) : 0,\n    }\n  }\n\n  return {\n    // 状态\n    categories,\n    currentSession,\n    practiceHistory,\n    dailyPracticeCount,\n    lastPracticeDate,\n    \n    // 计算属性\n    canPracticeToday,\n    remainingPracticeCount,\n    currentQuestionIndex,\n    currentQuestion,\n    isSessionCompleted,\n    \n    // 方法\n    setCategories,\n    startPracticeSession,\n    answerQuestion,\n    completeSession,\n    isAnswerCorrect,\n    updateDailyPracticeCount,\n    clearCurrentSession,\n    saveCurrentSession,\n    loadCurrentSession,\n    savePracticeHistory,\n    loadPracticeHistory,\n    saveDailyPracticeData,\n    loadDailyPracticeData,\n    initStudyStore,\n    getSessionStats,\n  }\n})\n"], "names": ["defineStore", "ref", "computed", "PRACTICE_CONFIG", "uni", "STORAGE_KEYS"], "mappings": ";;;;;;;;;;;;;;;;;;;AAKa,MAAA,gBAAgBA,cAAAA,YAAY,SAAS,MAAM;AAEhD,QAAA,aAAaC,kBAAwB,CAAA,CAAE;AACvC,QAAA,iBAAiBA,kBAA4B,IAAI;AACjD,QAAA,kBAAkBA,kBAAuB,CAAA,CAAE;AAC3C,QAAA,qBAAqBA,kBAAY,CAAC;AAClC,QAAA,mBAAmBA,kBAAY,EAAE;AAGjC,QAAA,mBAAmBC,cAAAA,SAAS,MAAM;AACtC,UAAM,SAAQ,oBAAI,KAAK,GAAE,aAAa;AAClC,QAAA,iBAAiB,UAAU,OAAO;AAC7B,aAAA;AAAA,IACT;AACO,WAAA,mBAAmB,QAAQC,oBAAgB,gBAAA;AAAA,EAAA,CACnD;AAEK,QAAA,yBAAyBD,cAAAA,SAAS,MAAM;AAC5C,UAAM,SAAQ,oBAAI,KAAK,GAAE,aAAa;AAClC,QAAA,iBAAiB,UAAU,OAAO;AACpC,aAAOC,oBAAAA,gBAAgB;AAAA,IACzB;AACA,WAAO,KAAK,IAAI,GAAGA,oBAAgB,gBAAA,wBAAwB,mBAAmB,KAAK;AAAA,EAAA,CACpF;AAEK,QAAA,uBAAuBD,cAAAA,SAAS,MAAM;AAC1C,QAAI,CAAC,eAAe;AAAc,aAAA;AAClC,WAAO,OAAO,KAAK,eAAe,MAAM,OAAO,EAAE;AAAA,EAAA,CAClD;AAEK,QAAA,kBAAkBA,cAAAA,SAAS,MAAM;AACjC,QAAA,CAAC,eAAe,SAAS,qBAAqB,SAAS,eAAe,MAAM,UAAU,QAAQ;AACzF,aAAA;AAAA,IACT;AACA,WAAO,eAAe,MAAM,UAAU,qBAAqB,KAAK;AAAA,EAAA,CACjE;AAEK,QAAA,qBAAqBA,cAAAA,SAAS,MAAM;AACxC,QAAI,CAAC,eAAe;AAAc,aAAA;AAClC,WAAO,qBAAqB,SAAS,eAAe,MAAM,UAAU;AAAA,EAAA,CACrE;AAGK,QAAA,gBAAgB,CAAC,kBAAsC;AAC3D,eAAW,QAAQ;AAAA,EAAA;AAGf,QAAA,uBAAuB,CAAC,YAAoB,cAA0B;AAC1E,UAAM,UAA2B;AAAA,MAC/B,IAAI,KAAK,IAAI,EAAE,SAAS;AAAA,MACxB;AAAA,MACA;AAAA,MACA,SAAS,CAAC;AAAA,MACV,YAAY,UAAU;AAAA,MACtB,YAAW,oBAAI,KAAK,GAAE,YAAY;AAAA,IAAA;AAGpC,mBAAe,QAAQ;AACJ;AACZ,WAAA;AAAA,EAAA;AAGH,QAAA,iBAAiB,CAAC,YAAoB,WAAgB;AAC1D,QAAI,CAAC,eAAe;AAAO;AAEZ,mBAAA,MAAM,QAAQ,UAAU,IAAI;AACxB;EAAA;AAGrB,QAAM,kBAAkB,MAAM;AAC5B,QAAI,CAAC,eAAe,SAAS,CAAC,mBAAmB;AAAO;AAGxD,QAAI,eAAe;AACJ,mBAAA,MAAM,UAAU,QAAQ,CAAY,aAAA;AACjD,YAAM,aAAa,eAAe,MAAO,QAAQ,SAAS,EAAE;AACxD,UAAA,gBAAgB,UAAU,UAAU,GAAG;AACzC;AAAA,MACF;AAAA,IAAA,CACD;AAED,mBAAe,MAAM,eAAe;AACrB,mBAAA,MAAM,QAAQ,KAAK,MAAO,eAAe,eAAe,MAAM,aAAc,GAAG;AAC9F,mBAAe,MAAM,WAAc,oBAAA,KAAA,GAAO;AAG1C,oBAAgB,MAAM,QAAQ,mBAAK,eAAe,MAAO;AAGhC;AAGL;AACD;AAEnB,WAAO,eAAe;AAAA,EAAA;AAGlB,QAAA,kBAAkB,CAAC,UAAoB,eAA6B;AACxE,QAAI,CAAC;AAAmB,aAAA;AAExB,YAAQ,SAAS,MAAM;AAAA,MACrB,KAAK;AAAA,MACL,KAAK;AACH,eAAO,eAAe,SAAS;AAAA,MACjC,KAAK;AACC,YAAA,CAAC,MAAM,QAAQ,UAAU,KAAK,CAAC,MAAM,QAAQ,SAAS,MAAM;AAAU,iBAAA;AAC1E,eAAO,WAAW,WAAW,SAAS,OAAO,UACtC,WAAW,MAAM,CAAA,QAAO,SAAS,OAAO,SAAS,GAAG,CAAC;AAAA,MAC9D,KAAK;AAEI,eAAA;AAAA,MACT;AACS,eAAA;AAAA,IACX;AAAA,EAAA;AAGF,QAAM,2BAA2B,MAAM;AACrC,UAAM,SAAQ,oBAAI,KAAK,GAAE,aAAa;AAElC,QAAA,iBAAiB,UAAU,OAAO;AAEpC,yBAAmB,QAAQ;AAC3B,uBAAiB,QAAQ;AAAA,IAAA,OACpB;AAEc,yBAAA;AAAA,IACrB;AAEsB;EAAA;AAGxB,QAAM,sBAAsB,MAAM;AAChC,mBAAe,QAAQ;AACnBE,kBAAAA,MAAA,kBAAkBC,iCAAa,cAAc;AAAA,EAAA;AAGnD,QAAM,qBAAqB,MAAM;AAC/B,QAAI,eAAe,OAAO;AACpB,UAAA;AACFD,4BAAI,eAAeC,oBAAAA,aAAa,gBAAgB,KAAK,UAAU,eAAe,KAAK,CAAC;AAAA,eAC7E,OAAO;AACdD,sBAAA,MAAc,MAAA,SAAA,8BAAA,mCAAmC,KAAK;AAAA,MACxD;AAAA,IACF;AAAA,EAAA;AAGF,QAAM,qBAAqB,MAAM;AAC3B,QAAA;AACF,YAAM,SAASA,cAAA,MAAI,eAAeC,oBAAA,aAAa,cAAc;AAC7D,UAAI,QAAQ;AACK,uBAAA,QAAQ,KAAK,MAAM,MAAM;AAAA,MAC1C;AAAA,aACO,OAAO;AACdD,oBAAA,MAAc,MAAA,SAAA,8BAAA,mCAAmC,KAAK;AAAA,IACxD;AAAA,EAAA;AAGF,QAAM,sBAAsB,MAAM;AAC5B,QAAA;AAEF,YAAM,gBAAgB,gBAAgB,MAAM,MAAM,GAAG,EAAE;AACvDA,oBAAA,MAAI,eAAe,oBAAoB,KAAK,UAAU,aAAa,CAAC;AAAA,aAC7D,OAAO;AACdA,oBAAA,MAAc,MAAA,SAAA,8BAAA,oCAAoC,KAAK;AAAA,IACzD;AAAA,EAAA;AAGF,QAAM,sBAAsB,MAAM;AAC5B,QAAA;AACI,YAAA,UAAUA,cAAAA,MAAI,eAAe,kBAAkB;AACrD,UAAI,SAAS;AACK,wBAAA,QAAQ,KAAK,MAAM,OAAO;AAAA,MAC5C;AAAA,aACO,OAAO;AACdA,oBAAA,MAAc,MAAA,SAAA,8BAAA,oCAAoC,KAAK;AAAA,IACzD;AAAA,EAAA;AAGF,QAAM,wBAAwB,MAAM;AAC9B,QAAA;AACF,YAAM,OAAO;AAAA,QACX,OAAO,mBAAmB;AAAA,QAC1B,MAAM,iBAAiB;AAAA,MAAA;AAEzBA,oBAAA,MAAI,eAAe,kBAAkB,KAAK,UAAU,IAAI,CAAC;AAAA,aAClD,OAAO;AACdA,oBAAA,MAAc,MAAA,SAAA,8BAAA,uCAAuC,KAAK;AAAA,IAC5D;AAAA,EAAA;AAGF,QAAM,wBAAwB,MAAM;AAC9B,QAAA;AACI,YAAA,OAAOA,cAAAA,MAAI,eAAe,gBAAgB;AAChD,UAAI,MAAM;AACF,cAAA,SAAS,KAAK,MAAM,IAAI;AACX,2BAAA,QAAQ,OAAO,SAAS;AAC1B,yBAAA,QAAQ,OAAO,QAAQ;AAAA,MAC1C;AAAA,aACO,OAAO;AACdA,oBAAA,MAAc,MAAA,SAAA,8BAAA,uCAAuC,KAAK;AAAA,IAC5D;AAAA,EAAA;AAGF,QAAM,iBAAiB,MAAM;AACR;AACC;AACE;EAAA;AAGxB,QAAM,kBAAkB,MAAM;AACtB,UAAA,gBAAgB,gBAAgB,MAAM;AACtC,UAAA,iBAAiB,gBAAgB,MAAM,OAAO,CAAC,KAAK,YAAY,MAAM,QAAQ,YAAY,CAAC;AAC3F,UAAA,eAAe,gBAAgB,MAAM,OAAO,CAAC,KAAK,YAAY,OAAO,QAAQ,gBAAgB,IAAI,CAAC;AACxG,UAAM,eAAe,gBAAgB,IACnC,gBAAgB,MAAM,OAAO,CAAC,KAAK,YAAY,OAAO,QAAQ,SAAS,IAAI,CAAC,IAAI,gBAAgB;AAE3F,WAAA;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA,cAAc,KAAK,MAAM,YAAY;AAAA,MACrC,UAAU,iBAAiB,IAAI,KAAK,MAAO,eAAe,iBAAkB,GAAG,IAAI;AAAA,IAAA;AAAA,EACrF;AAGK,SAAA;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ,CAAC;;"}