{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-transition/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 是否展示组件\n        show: {\n            type: Boolean,\n            default: () => defProps.transition.show\n        },\n        // 使用的动画模式\n        mode: {\n            type: String,\n            default: () => defProps.transition.mode\n        },\n        // 动画的执行时间，单位ms\n        duration: {\n            type: [String, Number],\n            default: () => defProps.transition.duration\n        },\n        // 使用的动画过渡函数\n        timingFunction: {\n            type: String,\n            default: () => defProps.transition.timingFunction\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,WAAW;AAAA,IACtC;AAAA,EACJ;AACL,CAAC;;"}