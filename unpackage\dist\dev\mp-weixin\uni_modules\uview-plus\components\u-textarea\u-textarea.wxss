/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-b6c174a6,
.u-empty__wrap.data-v-b6c174a6,
.u-tabs.data-v-b6c174a6,
.u-tabs__wrapper.data-v-b6c174a6,
.u-tabs__wrapper__scroll-view-wrapper.data-v-b6c174a6,
.u-tabs__wrapper__scroll-view.data-v-b6c174a6,
.u-tabs__wrapper__nav.data-v-b6c174a6,
.u-tabs__wrapper__nav__line.data-v-b6c174a6,
.up-empty.data-v-b6c174a6,
.up-empty__wrap.data-v-b6c174a6,
.up-tabs.data-v-b6c174a6,
.up-tabs__wrapper.data-v-b6c174a6,
.up-tabs__wrapper__scroll-view-wrapper.data-v-b6c174a6,
.up-tabs__wrapper__scroll-view.data-v-b6c174a6,
.up-tabs__wrapper__nav.data-v-b6c174a6,
.up-tabs__wrapper__nav__line.data-v-b6c174a6 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-textarea.data-v-b6c174a6 {
  border-radius: 4px;
  background-color: #fff;
  position: relative;
  display: flex;
  flex-direction: row;
  flex: 1;
  padding: 9px;
}
.u-textarea--radius.data-v-b6c174a6 {
  border-radius: 4px;
}
.u-textarea--no-radius.data-v-b6c174a6 {
  border-radius: 0;
}
.u-textarea--disabled.data-v-b6c174a6 {
  background-color: #f5f7fa;
}
.u-textarea__field.data-v-b6c174a6 {
  flex: 1;
  font-size: 15px;
  color: #606266;
  width: 100%;
}
.u-textarea__count.data-v-b6c174a6 {
  position: absolute;
  right: 5px;
  bottom: 2px;
  font-size: 12px;
  color: #909193;
  background-color: #ffffff;
  padding: 1px 4px;
}