<view class="practice-container data-v-17cd85ad"><u-navbar wx:if="{{b}}" class="data-v-17cd85ad" bindleftClick="{{a}}" u-i="17cd85ad-0" bind:__l="__l" u-p="{{b}}"/><view class="practice-progress data-v-17cd85ad"><view class="progress-info data-v-17cd85ad"><text class="current-question data-v-17cd85ad">第 {{c}} 题</text><text class="total-questions data-v-17cd85ad">共 {{d}} 题</text></view><view class="progress-bar data-v-17cd85ad"><view class="progress-fill data-v-17cd85ad" style="{{'width:' + e}}"></view></view><view class="time-info data-v-17cd85ad"><u-icon wx:if="{{f}}" class="data-v-17cd85ad" u-i="17cd85ad-1" bind:__l="__l" u-p="{{f}}"/><text class="elapsed-time data-v-17cd85ad">{{g}}</text></view></view><view wx:if="{{h}}" class="question-container data-v-17cd85ad"><view class="question-header data-v-17cd85ad"><view class="question-type data-v-17cd85ad"><status-tag wx:if="{{i}}" class="data-v-17cd85ad" u-i="17cd85ad-2" bind:__l="__l" u-p="{{i}}"/></view><view wx:if="{{j}}" class="question-difficulty data-v-17cd85ad"><text class="{{['data-v-17cd85ad', l]}}">{{k}}</text></view></view><view class="question-content data-v-17cd85ad"><text class="question-title data-v-17cd85ad">{{m}}</text></view><view wx:if="{{n}}" class="question-options data-v-17cd85ad"><view wx:for="{{o}}" wx:for-item="option" wx:key="f" class="{{['option-item', 'data-v-17cd85ad', option.g]}}" bindtap="{{option.h}}"><view class="option-indicator data-v-17cd85ad"><text class="option-label data-v-17cd85ad">{{option.a}}</text><u-icon wx:if="{{option.b}}" class="data-v-17cd85ad" u-i="{{option.c}}" bind:__l="__l" u-p="{{option.d}}"/></view><text class="option-text data-v-17cd85ad">{{option.e}}</text></view></view><view wx:elif="{{p}}" class="judge-options data-v-17cd85ad"><view class="{{['judge-option', 'data-v-17cd85ad', r && 'active']}}" bindtap="{{s}}"><u-icon wx:if="{{q}}" class="data-v-17cd85ad" u-i="17cd85ad-4" bind:__l="__l" u-p="{{q}}"/><text class="data-v-17cd85ad">正确</text></view><view class="{{['judge-option', 'data-v-17cd85ad', v && 'active']}}" bindtap="{{w}}"><u-icon wx:if="{{t}}" class="data-v-17cd85ad" u-i="17cd85ad-5" bind:__l="__l" u-p="{{t}}"/><text class="data-v-17cd85ad">错误</text></view></view><view wx:elif="{{x}}" class="essay-input data-v-17cd85ad"><u-textarea wx:if="{{z}}" class="data-v-17cd85ad" u-i="17cd85ad-6" bind:__l="__l" bindupdateModelValue="{{y}}" u-p="{{z}}"/></view></view><view class="action-buttons data-v-17cd85ad"><u-button wx:if="{{A}}" u-s="{{['d']}}" class="prev-btn data-v-17cd85ad" bindclick="{{B}}" u-i="17cd85ad-7" bind:__l="__l" u-p="{{C}}"> 上一题 </u-button><u-button wx:if="{{F}}" u-s="{{['d']}}" class="next-btn data-v-17cd85ad" bindclick="{{E}}" u-i="17cd85ad-8" bind:__l="__l" u-p="{{F}}">{{D}}</u-button></view><view class="answer-sheet-btn data-v-17cd85ad" bindtap="{{H}}"><u-icon wx:if="{{G}}" class="data-v-17cd85ad" u-i="17cd85ad-9" bind:__l="__l" u-p="{{G}}"/><text class="data-v-17cd85ad">答题卡</text></view><u-popup wx:if="{{M}}" class="data-v-17cd85ad" u-s="{{['d']}}" u-i="17cd85ad-10" bind:__l="__l" bindupdateModelValue="{{L}}" u-p="{{M}}"><view class="answer-sheet-modal data-v-17cd85ad"><view class="modal-header data-v-17cd85ad"><text class="modal-title data-v-17cd85ad">答题卡</text><u-icon wx:if="{{J}}" class="data-v-17cd85ad" bindclick="{{I}}" u-i="17cd85ad-11,17cd85ad-10" bind:__l="__l" u-p="{{J}}"/></view><view class="answer-grid data-v-17cd85ad"><view wx:for="{{K}}" wx:for-item="question" wx:key="b" class="{{['answer-item', 'data-v-17cd85ad', question.c]}}" bindtap="{{question.d}}"><text class="data-v-17cd85ad">{{question.a}}</text></view></view><view class="answer-legend data-v-17cd85ad"><view class="legend-item data-v-17cd85ad"><view class="legend-color answered data-v-17cd85ad"></view><text class="data-v-17cd85ad">已答</text></view><view class="legend-item data-v-17cd85ad"><view class="legend-color current data-v-17cd85ad"></view><text class="data-v-17cd85ad">当前</text></view><view class="legend-item data-v-17cd85ad"><view class="legend-color unanswered data-v-17cd85ad"></view><text class="data-v-17cd85ad">未答</text></view></view></view></u-popup><u-modal wx:if="{{Q}}" class="data-v-17cd85ad" bindconfirm="{{N}}" bindcancel="{{O}}" u-i="17cd85ad-12" bind:__l="__l" bindupdateModelValue="{{P}}" u-p="{{Q}}"/></view>