"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const src_stores_app = require("../../src/stores/app.js");
const src_api_index = require("../../src/api/index.js");
const src_utils_index = require("../../src/utils/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  (_easycom_u_navbar2 + _easycom_u_icon2)();
}
const _easycom_u_navbar = () => "../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
if (!Math) {
  (_easycom_u_navbar + LoadingSpinner + EmptyState + StatusTag + _easycom_u_icon)();
}
const LoadingSpinner = () => "../../src/components/common/LoadingSpinner.js";
const EmptyState = () => "../../src/components/common/EmptyState.js";
const StatusTag = () => "../../src/components/common/StatusTag.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "detail",
  props: {
    id: {}
  },
  setup(__props) {
    const appStore = src_stores_app.useAppStore();
    const props = __props;
    const isLoading = common_vendor.ref(true);
    const error = common_vendor.ref("");
    const detail = common_vendor.ref(null);
    const relatedList = common_vendor.ref([]);
    const isCollected = common_vendor.ref(false);
    const formattedContent = common_vendor.computed(() => {
      var _a;
      if (!((_a = detail.value) == null ? void 0 : _a.content))
        return "";
      let content = detail.value.content;
      content = content.replace(/\n/g, "<br/>");
      content = content.replace(/(<br\/>){2,}/g, "</p><p>");
      content = `<p>${content}</p>`;
      return content;
    });
    common_vendor.onMounted(() => {
      loadDetail();
      loadRelated();
    });
    const loadDetail = () => __async(this, null, function* () {
      if (!props.id) {
        error.value = "参数错误";
        isLoading.value = false;
        return;
      }
      isLoading.value = true;
      error.value = "";
      try {
        const response = yield src_api_index.api.info.getAnnouncementDetail(props.id);
        detail.value = response.data;
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/info/detail.vue:169", "加载详情失败:", err);
        error.value = err.message || "加载失败";
      } finally {
        isLoading.value = false;
      }
    });
    const loadRelated = () => __async(this, null, function* () {
      var _a;
      try {
        const response = yield src_api_index.api.info.getAnnouncements({
          page: 1,
          pageSize: 5,
          type: (_a = detail.value) == null ? void 0 : _a.type
        });
        relatedList.value = response.data.list.filter((item) => item.id !== props.id);
      } catch (error2) {
        common_vendor.index.__f__("error", "at pages/info/detail.vue:188", "加载相关推荐失败:", error2);
      }
    });
    const viewRelated = (item) => {
      common_vendor.index.redirectTo({
        url: `/pages/info/detail?id=${item.id}`
      });
    };
    const handleShare = () => {
      if (!detail.value)
        return;
      common_vendor.index.share({
        provider: "weixin",
        scene: "WXSceneSession",
        type: 0,
        href: "",
        // 这里应该是文章的分享链接
        title: detail.value.title,
        summary: detail.value.content.substring(0, 100),
        imageUrl: "",
        // 分享图片
        success: () => {
          appStore.showToast("分享成功", "success");
        },
        fail: (err) => {
          common_vendor.index.__f__("error", "at pages/info/detail.vue:216", "分享失败:", err);
          appStore.showToast("分享失败");
        }
      });
    };
    const handleCollect = () => __async(this, null, function* () {
      if (!detail.value)
        return;
      try {
        if (isCollected.value) {
          isCollected.value = false;
          appStore.showToast("已取消收藏", "success");
        } else {
          isCollected.value = true;
          appStore.showToast("收藏成功", "success");
        }
      } catch (error2) {
        appStore.showToast(error2.message || "操作失败");
      }
    });
    const handleFeedback = () => {
      appStore.showModal({
        title: "问题反馈",
        content: "如果您发现内容有误或有其他问题，请联系我们进行反馈。",
        confirmText: "联系客服",
        cancelText: "取消"
      }).then((confirmed) => {
        if (confirmed) {
          appStore.showToast("功能开发中");
        }
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "详情",
          autoBack: true,
          background: {
            background: "linear-gradient(135deg, #2E8B57 0%, #228B22 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: isLoading.value
      }, isLoading.value ? {
        c: common_vendor.p({
          overlay: true,
          text: "加载中..."
        })
      } : error.value ? {
        e: common_vendor.o(loadDetail),
        f: common_vendor.p({
          type: "no-network",
          title: error.value,
          description: "请检查网络连接后重试",
          showButton: true,
          buttonText: "重新加载"
        })
      } : detail.value ? common_vendor.e({
        h: common_vendor.p({
          type: "announcement",
          status: detail.value.type
        }),
        i: detail.value.isImportant
      }, detail.value.isImportant ? {
        j: common_vendor.p({
          name: "warning",
          color: "#FF9500",
          size: "24"
        })
      } : {}, {
        k: detail.value.isTop
      }, detail.value.isTop ? {
        l: common_vendor.p({
          name: "arrow-up",
          color: "#52C41A",
          size: "24"
        })
      } : {}, {
        m: common_vendor.t(detail.value.title),
        n: common_vendor.p({
          name: "calendar",
          color: "#999",
          size: "28"
        }),
        o: common_vendor.t(common_vendor.unref(src_utils_index.formatDate)(detail.value.publishTime, "YYYY-MM-DD HH:mm")),
        p: detail.value.source
      }, detail.value.source ? {
        q: common_vendor.p({
          name: "home",
          color: "#999",
          size: "28"
        }),
        r: common_vendor.t(detail.value.source)
      } : {}, {
        s: formattedContent.value,
        t: common_vendor.p({
          name: "share",
          color: "#666",
          size: "32"
        }),
        v: common_vendor.o(handleShare),
        w: common_vendor.p({
          name: isCollected.value ? "heart-fill" : "heart",
          color: isCollected.value ? "#F5222D" : "#666",
          size: "32"
        }),
        x: common_vendor.t(isCollected.value ? "已收藏" : "收藏"),
        y: common_vendor.o(handleCollect),
        z: common_vendor.p({
          name: "chat",
          color: "#666",
          size: "32"
        }),
        A: common_vendor.o(handleFeedback)
      }) : {}, {
        d: error.value,
        g: detail.value,
        B: relatedList.value.length > 0
      }, relatedList.value.length > 0 ? {
        C: common_vendor.f(relatedList.value, (item, k0, i0) => {
          return {
            a: common_vendor.t(item.title),
            b: common_vendor.t(common_vendor.unref(src_utils_index.formatRelativeTime)(item.publishTime)),
            c: item.id,
            d: common_vendor.o(($event) => viewRelated(item), item.id)
          };
        })
      } : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ab57d952"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/info/detail.js.map
