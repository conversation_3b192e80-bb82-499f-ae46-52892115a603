{"version": 3, "file": "router-guard.js", "sources": ["src/utils/router-guard.ts"], "sourcesContent": ["import { getPermissionManager } from './permission'\nimport { PAGE_PATHS } from '../constants'\n\n/**\n * 路由守卫配置\n */\ninterface RouteGuardConfig {\n  // 需要登录的页面\n  authRequired: string[]\n  // 需要认证的页面\n  authenticationRequired: string[]\n  // 公开页面（无需任何权限）\n  publicPages: string[]\n  // 默认重定向页面\n  defaultRedirect: string\n}\n\nconst routeGuardConfig: RouteGuardConfig = {\n  authRequired: [\n    PAGE_PATHS.INFO,\n    PAGE_PATHS.STUDY,\n    PAGE_PATHS.EXAM,\n    PAGE_PATHS.PERSONAL,\n    // 分包页面\n    PAGE_PATHS.EXAM_ONLINE_READING,\n    PAGE_PATHS.EXAM_ONLINE_FACE_VERIFY,\n    PAGE_PATHS.EXAM_ONLINE_ANSWER,\n    PAGE_PATHS.EXAM_OFFLINE_DETAIL,\n    PAGE_PATHS.EXAM_HISTORY,\n    PAGE_PATHS.PERSONAL_INFO,\n    PAGE_PATHS.PERSONAL_CERTIFICATE,\n    PAGE_PATHS.PERSONAL_FEEDBACK,\n    PAGE_PATHS.PERSONAL_ABOUT,\n  ],\n  authenticationRequired: [\n    PAGE_PATHS.EXAM,\n    PAGE_PATHS.EXAM_ONLINE_READING,\n    PAGE_PATHS.EXAM_ONLINE_FACE_VERIFY,\n    PAGE_PATHS.EXAM_ONLINE_ANSWER,\n    PAGE_PATHS.EXAM_OFFLINE_DETAIL,\n    PAGE_PATHS.EXAM_HISTORY,\n    PAGE_PATHS.PERSONAL_CERTIFICATE,\n  ],\n  publicPages: [\n    PAGE_PATHS.LOGIN,\n    PAGE_PATHS.PROFILE,\n  ],\n  defaultRedirect: PAGE_PATHS.INFO,\n}\n\n/**\n * 路由守卫类\n */\nexport class RouterGuard {\n  private config: RouteGuardConfig\n\n  constructor(config: RouteGuardConfig) {\n    this.config = config\n  }\n\n  /**\n   * 检查页面访问权限\n   */\n  async checkAccess(url: string): Promise<{\n    allowed: boolean\n    redirectTo?: string\n    message?: string\n  }> {\n    // 提取页面路径（去除参数）\n    const pagePath = url.split('?')[0]\n\n    // 公开页面直接允许访问\n    if (this.config.publicPages.includes(pagePath)) {\n      return { allowed: true }\n    }\n\n    // 检查是否需要登录\n    if (this.config.authRequired.includes(pagePath)) {\n      const permissionManager = getPermissionManager()\n      if (!permissionManager.isLoggedIn()) {\n        return {\n          allowed: false,\n          redirectTo: PAGE_PATHS.LOGIN,\n          message: '请先登录'\n        }\n      }\n    }\n\n    // 检查是否需要认证\n    if (this.config.authenticationRequired.includes(pagePath)) {\n      const permissionManager = getPermissionManager()\n      if (!permissionManager.isAuthenticated()) {\n        const userStatus = permissionManager.getUserStatus()\n        \n        if (userStatus === 'not_submitted') {\n          return {\n            allowed: false,\n            redirectTo: PAGE_PATHS.PROFILE,\n            message: '请先完善个人资料'\n          }\n        } else if (userStatus === 'pending') {\n          return {\n            allowed: false,\n            redirectTo: PAGE_PATHS.PERSONAL,\n            message: '个人资料审核中，暂时无法使用此功能'\n          }\n        } else if (userStatus === 'rejected') {\n          return {\n            allowed: false,\n            redirectTo: PAGE_PATHS.PROFILE,\n            message: '资料审核未通过，请重新提交'\n          }\n        } else {\n          return {\n            allowed: false,\n            redirectTo: PAGE_PATHS.PERSONAL,\n            message: '请先完善个人资料并通过审核'\n          }\n        }\n      }\n    }\n\n    return { allowed: true }\n  }\n\n  /**\n   * 执行路由守卫\n   */\n  async guard(url: string): Promise<boolean> {\n    const result = await this.checkAccess(url)\n\n    if (!result.allowed) {\n      if (result.message) {\n        uni.showToast({\n          title: result.message,\n          icon: 'none',\n          duration: 2000\n        })\n      }\n\n      if (result.redirectTo) {\n        // 延迟跳转，确保toast显示\n        setTimeout(() => {\n          if (result.redirectTo === PAGE_PATHS.LOGIN) {\n            uni.reLaunch({\n              url: result.redirectTo\n            })\n          } else if (this.config.authRequired.includes(result.redirectTo)) {\n            uni.switchTab({\n              url: result.redirectTo\n            })\n          } else {\n            uni.redirectTo({\n              url: result.redirectTo\n            })\n          }\n        }, 1500)\n      }\n\n      return false\n    }\n\n    return true\n  }\n\n  /**\n   * 获取默认首页\n   */\n  getDefaultPage(): string {\n    const permissionManager = getPermissionManager()\n    if (!permissionManager.isLoggedIn()) {\n      return PAGE_PATHS.LOGIN\n    }\n\n    const userStatus = permissionManager.getUserStatus()\n    if (userStatus === 'not_submitted') {\n      return PAGE_PATHS.PROFILE\n    }\n\n    return this.config.defaultRedirect\n  }\n}\n\n// 创建全局路由守卫实例\nexport const routerGuard = new RouterGuard(routeGuardConfig)\n\n/**\n * 页面跳转拦截器\n */\nexport function interceptNavigation() {\n  // 拦截 uni.navigateTo\n  const originalNavigateTo = uni.navigateTo\n  uni.navigateTo = function(options: any) {\n    routerGuard.guard(options.url).then(allowed => {\n      if (allowed) {\n        originalNavigateTo.call(uni, options)\n      }\n    })\n  }\n\n  // 拦截 uni.redirectTo\n  const originalRedirectTo = uni.redirectTo\n  uni.redirectTo = function(options: any) {\n    routerGuard.guard(options.url).then(allowed => {\n      if (allowed) {\n        originalRedirectTo.call(uni, options)\n      }\n    })\n  }\n\n  // 拦截 uni.switchTab\n  const originalSwitchTab = uni.switchTab\n  uni.switchTab = function(options: any) {\n    routerGuard.guard(options.url).then(allowed => {\n      if (allowed) {\n        originalSwitchTab.call(uni, options)\n      }\n    })\n  }\n}\n\n/**\n * 页面权限检查 mixin\n */\nexport const pagePermissionMixin = {\n  onLoad() {\n    // 获取当前页面路径\n    const pages = getCurrentPages()\n    const currentPage = pages[pages.length - 1]\n    const route = currentPage.route\n\n    // 检查页面访问权限\n    routerGuard.guard(`/${route}`).then(allowed => {\n      if (!allowed) {\n        // 权限检查失败，页面会被重定向\n        console.log('Page access denied:', route)\n      }\n    })\n  }\n}\n\n/**\n * 初始化路由守卫\n */\nexport function initRouterGuard() {\n  // 启用导航拦截\n  interceptNavigation()\n  \n  console.log('Router guard initialized')\n}\n"], "names": ["PAGE_PATHS", "getPermissionManager", "uni"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAiBA,MAAM,mBAAqC;AAAA,EACzC,cAAc;AAAA,IACZA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA;AAAA,IAEXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,EACb;AAAA,EACA,wBAAwB;AAAA,IACtBA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,EACb;AAAA,EACA,aAAa;AAAA,IACXA,oBAAAA,WAAW;AAAA,IACXA,oBAAAA,WAAW;AAAA,EACb;AAAA,EACA,iBAAiBA,oBAAW,WAAA;AAC9B;AAKO,MAAM,YAAY;AAAA,EAGvB,YAAY,QAA0B;AACpC,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAKM,YAAY,KAIf;AAAA;AAED,YAAM,WAAW,IAAI,MAAM,GAAG,EAAE,CAAC;AAGjC,UAAI,KAAK,OAAO,YAAY,SAAS,QAAQ,GAAG;AACvC,eAAA,EAAE,SAAS;MACpB;AAGA,UAAI,KAAK,OAAO,aAAa,SAAS,QAAQ,GAAG;AAC/C,cAAM,oBAAoBC,qBAAAA;AACtB,YAAA,CAAC,kBAAkB,cAAc;AAC5B,iBAAA;AAAA,YACL,SAAS;AAAA,YACT,YAAYD,oBAAW,WAAA;AAAA,YACvB,SAAS;AAAA,UAAA;AAAA,QAEb;AAAA,MACF;AAGA,UAAI,KAAK,OAAO,uBAAuB,SAAS,QAAQ,GAAG;AACzD,cAAM,oBAAoBC,qBAAAA;AACtB,YAAA,CAAC,kBAAkB,mBAAmB;AAClC,gBAAA,aAAa,kBAAkB;AAErC,cAAI,eAAe,iBAAiB;AAC3B,mBAAA;AAAA,cACL,SAAS;AAAA,cACT,YAAYD,oBAAW,WAAA;AAAA,cACvB,SAAS;AAAA,YAAA;AAAA,UACX,WACS,eAAe,WAAW;AAC5B,mBAAA;AAAA,cACL,SAAS;AAAA,cACT,YAAYA,oBAAW,WAAA;AAAA,cACvB,SAAS;AAAA,YAAA;AAAA,UACX,WACS,eAAe,YAAY;AAC7B,mBAAA;AAAA,cACL,SAAS;AAAA,cACT,YAAYA,oBAAW,WAAA;AAAA,cACvB,SAAS;AAAA,YAAA;AAAA,UACX,OACK;AACE,mBAAA;AAAA,cACL,SAAS;AAAA,cACT,YAAYA,oBAAW,WAAA;AAAA,cACvB,SAAS;AAAA,YAAA;AAAA,UAEb;AAAA,QACF;AAAA,MACF;AAEO,aAAA,EAAE,SAAS;IACpB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKM,MAAM,KAA+B;AAAA;AACzC,YAAM,SAAS,MAAM,KAAK,YAAY,GAAG;AAErC,UAAA,CAAC,OAAO,SAAS;AACnB,YAAI,OAAO,SAAS;AAClBE,wBAAAA,MAAI,UAAU;AAAA,YACZ,OAAO,OAAO;AAAA,YACd,MAAM;AAAA,YACN,UAAU;AAAA,UAAA,CACX;AAAA,QACH;AAEA,YAAI,OAAO,YAAY;AAErB,qBAAW,MAAM;AACX,gBAAA,OAAO,eAAeF,oBAAA,WAAW,OAAO;AAC1CE,4BAAAA,MAAI,SAAS;AAAA,gBACX,KAAK,OAAO;AAAA,cAAA,CACb;AAAA,YAAA,WACQ,KAAK,OAAO,aAAa,SAAS,OAAO,UAAU,GAAG;AAC/DA,4BAAAA,MAAI,UAAU;AAAA,gBACZ,KAAK,OAAO;AAAA,cAAA,CACb;AAAA,YAAA,OACI;AACLA,4BAAAA,MAAI,WAAW;AAAA,gBACb,KAAK,OAAO;AAAA,cAAA,CACb;AAAA,YACH;AAAA,aACC,IAAI;AAAA,QACT;AAEO,eAAA;AAAA,MACT;AAEO,aAAA;AAAA,IACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAyB;AACvB,UAAM,oBAAoBD,qBAAAA;AACtB,QAAA,CAAC,kBAAkB,cAAc;AACnC,aAAOD,oBAAAA,WAAW;AAAA,IACpB;AAEM,UAAA,aAAa,kBAAkB;AACrC,QAAI,eAAe,iBAAiB;AAClC,aAAOA,oBAAAA,WAAW;AAAA,IACpB;AAEA,WAAO,KAAK,OAAO;AAAA,EACrB;AACF;AAGa,MAAA,cAAc,IAAI,YAAY,gBAAgB;AAKpD,SAAS,sBAAsB;AAEpC,QAAM,qBAAqBE,cAAI,MAAA;AAC3BA,sBAAA,aAAa,SAAS,SAAc;AACtC,gBAAY,MAAM,QAAQ,GAAG,EAAE,KAAK,CAAW,YAAA;AAC7C,UAAI,SAAS;AACQ,2BAAA,KAAKA,qBAAK,OAAO;AAAA,MACtC;AAAA,IAAA,CACD;AAAA,EAAA;AAIH,QAAM,qBAAqBA,cAAI,MAAA;AAC3BA,sBAAA,aAAa,SAAS,SAAc;AACtC,gBAAY,MAAM,QAAQ,GAAG,EAAE,KAAK,CAAW,YAAA;AAC7C,UAAI,SAAS;AACQ,2BAAA,KAAKA,qBAAK,OAAO;AAAA,MACtC;AAAA,IAAA,CACD;AAAA,EAAA;AAIH,QAAM,oBAAoBA,cAAI,MAAA;AAC1BA,sBAAA,YAAY,SAAS,SAAc;AACrC,gBAAY,MAAM,QAAQ,GAAG,EAAE,KAAK,CAAW,YAAA;AAC7C,UAAI,SAAS;AACO,0BAAA,KAAKA,qBAAK,OAAO;AAAA,MACrC;AAAA,IAAA,CACD;AAAA,EAAA;AAEL;AAyBO,SAAS,kBAAkB;AAEZ;AAEpBA,gBAAAA,MAAA,MAAA,OAAA,oCAAY,0BAA0B;AACxC;;"}