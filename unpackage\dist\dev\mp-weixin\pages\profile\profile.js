"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const src_stores_user = require("../../src/stores/user.js");
const src_stores_app = require("../../src/stores/app.js");
const src_api_index = require("../../src/api/index.js");
const src_constants_index = require("../../src/constants/index.js");
const src_utils_index = require("../../src/utils/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_input2 = common_vendor.resolveComponent("u-input");
  const _easycom_u_form_item2 = common_vendor.resolveComponent("u-form-item");
  const _easycom_u_form2 = common_vendor.resolveComponent("u-form");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  const _easycom_u_picker2 = common_vendor.resolveComponent("u-picker");
  (_easycom_u_navbar2 + _easycom_u_icon2 + _easycom_u_input2 + _easycom_u_form_item2 + _easycom_u_form2 + _easycom_u_button2 + _easycom_u_picker2)();
}
const _easycom_u_navbar = () => "../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_input = () => "../../uni_modules/uview-plus/components/u-input/u-input.js";
const _easycom_u_form_item = () => "../../uni_modules/uview-plus/components/u-form-item/u-form-item.js";
const _easycom_u_form = () => "../../uni_modules/uview-plus/components/u-form/u-form.js";
const _easycom_u_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_u_picker = () => "../../uni_modules/uview-plus/components/u-picker/u-picker.js";
if (!Math) {
  (_easycom_u_navbar + _easycom_u_icon + _easycom_u_input + _easycom_u_form_item + _easycom_u_form + _easycom_u_button + _easycom_u_picker)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "profile",
  setup(__props) {
    const userStore = src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const isSubmitting = common_vendor.ref(false);
    const showOrganizationPicker = common_vendor.ref(false);
    const showPositionPicker = common_vendor.ref(false);
    const formData = common_vendor.reactive({
      realName: "",
      phone: "",
      idCard: "",
      organization: "",
      position: "",
      photo: ""
    });
    const rules = {
      realName: [
        { required: true, message: "请输入姓名", trigger: "blur" },
        { min: 2, max: 10, message: "姓名长度应为2-10个字符", trigger: "blur" }
      ],
      phone: [
        { required: true, message: "请输入手机号码", trigger: "blur" },
        {
          validator: (rule, value) => src_utils_index.validatePhone(value),
          message: "请输入正确的手机号码",
          trigger: "blur"
        }
      ],
      idCard: [
        { required: true, message: "请输入身份证号码", trigger: "blur" },
        {
          validator: (rule, value) => src_utils_index.validateIdCard(value),
          message: "请输入正确的身份证号码",
          trigger: "blur"
        }
      ],
      organization: [
        { required: true, message: "请选择隶属机构", trigger: "change" }
      ],
      position: [
        { required: true, message: "请选择职位", trigger: "change" }
      ]
    };
    const organizationList = [
      "市疾病预防控制中心",
      "区疾病预防控制中心",
      "县疾病预防控制中心",
      "社区卫生服务中心",
      "乡镇卫生院",
      "其他医疗机构"
    ];
    const positionList = [
      "疾控科医师",
      "预防保健科医师",
      "接种门诊医师",
      "产科医师",
      "犬伤门诊医师",
      "护士",
      "公共卫生医师",
      "其他"
    ];
    const formRef = common_vendor.ref();
    const onOrganizationConfirm = (e) => {
      formData.organization = organizationList[e.value[0]];
      showOrganizationPicker.value = false;
    };
    const onPositionConfirm = (e) => {
      formData.position = positionList[e.value[0]];
      showPositionPicker.value = false;
    };
    const chooseImage = () => {
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          common_vendor.index.getFileInfo({
            filePath: tempFilePath,
            success: (fileInfo) => {
              if (fileInfo.size > src_constants_index.UPLOAD_CONFIG.MAX_SIZE) {
                appStore.showToast("图片大小不能超过200KB");
                return;
              }
              formData.photo = tempFilePath;
              uploadImage(tempFilePath);
            }
          });
        }
      });
    };
    const deleteImage = () => {
      appStore.showModal({
        title: "确认删除",
        content: "确定要删除这张照片吗？"
      }).then((confirmed) => {
        if (confirmed) {
          formData.photo = "";
        }
      });
    };
    const uploadImage = (filePath) => __async(this, null, function* () {
      try {
        appStore.showLoading("上传中...");
        const response = yield src_api_index.api.user.uploadPhoto(filePath);
        formData.photo = response.data.url;
        appStore.hideLoading();
        appStore.showToast("照片上传成功", "success");
      } catch (error) {
        appStore.hideLoading();
        appStore.showToast(error.message || "照片上传失败，请重试");
        formData.photo = "";
      }
    });
    const skipSubmit = () => {
      appStore.showModal({
        title: "确认跳过",
        content: "跳过资料提交后，您只能使用部分功能。建议完善资料后使用完整功能。",
        confirmText: "确认跳过",
        cancelText: "继续完善"
      }).then((confirmed) => {
        if (confirmed) {
          appStore.switchTab(src_constants_index.PAGE_PATHS.STUDY);
        }
      });
    };
    const handleSubmit = () => __async(this, null, function* () {
      try {
        const valid = yield formRef.value.validate();
        if (!valid)
          return;
        if (!formData.photo) {
          appStore.showToast("请上传本人照片");
          return;
        }
        isSubmitting.value = true;
        const response = yield src_api_index.api.user.submitProfile(formData);
        userStore.updateProfile(response.data);
        appStore.showToast("资料提交成功", "success");
        setTimeout(() => {
          appStore.switchTab(src_constants_index.PAGE_PATHS.PERSONAL);
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/profile/profile.vue:376", "提交失败:", error);
        appStore.showToast(error.message || "提交失败，请重试");
      } finally {
        isSubmitting.value = false;
      }
    });
    common_vendor.onMounted(() => {
      if (userStore.userInfo) {
        const user = userStore.userInfo;
        formData.realName = user.realName || "";
        formData.phone = user.phone || "";
        formData.idCard = user.idCard || "";
        formData.organization = user.organization || "";
        formData.position = user.position || "";
        formData.photo = user.photo || "";
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "完善个人资料",
          autoBack: true,
          background: {
            background: "linear-gradient(135deg, #2E8B57 0%, #228B22 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: common_vendor.p({
          name: "checkmark",
          color: "#fff",
          size: "24"
        }),
        c: common_vendor.p({
          name: "account",
          color: "#2E8B57",
          size: "32"
        }),
        d: common_vendor.o(($event) => formData.realName = $event),
        e: common_vendor.p({
          placeholder: "请输入真实姓名",
          clearable: true,
          modelValue: formData.realName
        }),
        f: common_vendor.p({
          label: "姓名",
          prop: "realName",
          required: true
        }),
        g: common_vendor.o(($event) => formData.phone = $event),
        h: common_vendor.p({
          placeholder: "请输入手机号码",
          type: "number",
          clearable: true,
          modelValue: formData.phone
        }),
        i: common_vendor.p({
          label: "手机号码",
          prop: "phone",
          required: true
        }),
        j: common_vendor.o(($event) => formData.idCard = $event),
        k: common_vendor.p({
          placeholder: "请输入身份证号码",
          clearable: true,
          modelValue: formData.idCard
        }),
        l: common_vendor.p({
          label: "身份证号",
          prop: "idCard",
          required: true
        }),
        m: common_vendor.p({
          name: "home",
          color: "#2E8B57",
          size: "32"
        }),
        n: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc"
        }),
        o: common_vendor.o(($event) => showOrganizationPicker.value = true),
        p: common_vendor.o(($event) => formData.organization = $event),
        q: common_vendor.p({
          placeholder: "请选择隶属机构",
          disabled: true,
          modelValue: formData.organization
        }),
        r: common_vendor.p({
          label: "隶属机构",
          prop: "organization",
          required: true
        }),
        s: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc"
        }),
        t: common_vendor.o(($event) => showPositionPicker.value = true),
        v: common_vendor.o(($event) => formData.position = $event),
        w: common_vendor.p({
          placeholder: "请选择职位",
          disabled: true,
          modelValue: formData.position
        }),
        x: common_vendor.p({
          label: "职位",
          prop: "position",
          required: true
        }),
        y: common_vendor.p({
          name: "camera",
          color: "#2E8B57",
          size: "32"
        }),
        z: common_vendor.p({
          name: "info-circle",
          color: "#FF9500",
          size: "28"
        }),
        A: !formData.photo
      }, !formData.photo ? {
        B: common_vendor.p({
          name: "camera-fill",
          color: "#ccc",
          size: "80"
        })
      } : {
        C: formData.photo
      }, {
        D: formData.photo
      }, formData.photo ? {
        E: common_vendor.p({
          name: "camera",
          color: "#fff",
          size: "24"
        }),
        F: common_vendor.o(chooseImage),
        G: common_vendor.p({
          name: "trash",
          color: "#fff",
          size: "24"
        }),
        H: common_vendor.o(deleteImage)
      } : {}, {
        I: common_vendor.o(chooseImage),
        J: common_vendor.sr(formRef, "dd383ca2-2", {
          "k": "formRef"
        }),
        K: common_vendor.p({
          model: formData,
          rules,
          labelWidth: "140"
        }),
        L: common_vendor.o(skipSubmit),
        M: common_vendor.p({
          type: "info",
          plain: true
        }),
        N: common_vendor.o(handleSubmit),
        O: common_vendor.p({
          type: "primary",
          loading: isSubmitting.value,
          loadingText: "提交中..."
        }),
        P: common_vendor.o(onOrganizationConfirm),
        Q: common_vendor.o(($event) => showOrganizationPicker.value = false),
        R: common_vendor.o(($event) => showOrganizationPicker.value = $event),
        S: common_vendor.p({
          mode: "selector",
          range: organizationList,
          modelValue: showOrganizationPicker.value
        }),
        T: common_vendor.o(onPositionConfirm),
        U: common_vendor.o(($event) => showPositionPicker.value = false),
        V: common_vendor.o(($event) => showPositionPicker.value = $event),
        W: common_vendor.p({
          mode: "selector",
          range: positionList,
          modelValue: showPositionPicker.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-dd383ca2"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/profile/profile.js.map
