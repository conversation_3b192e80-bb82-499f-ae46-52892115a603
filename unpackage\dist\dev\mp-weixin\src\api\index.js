"use strict";
const src_utils_request = require("../utils/request.js");
const userApi = {
  // 微信登录
  wxLogin: (data) => src_utils_request.request.post("/auth/wx-login", data),
  // 提交个人资料
  submitProfile: (data) => src_utils_request.request.post("/user/profile", data),
  // 获取用户信息
  getUserInfo: () => src_utils_request.request.get("/user/info"),
  // 更新用户信息
  updateUserInfo: (data) => src_utils_request.request.put("/user/info", data),
  // 上传头像
  uploadAvatar: (filePath) => src_utils_request.request.upload({
    url: "/user/avatar",
    filePath,
    name: "avatar"
  }),
  // 上传本人照片
  uploadPhoto: (filePath) => src_utils_request.request.upload({
    url: "/user/photo",
    filePath,
    name: "photo"
  })
};
const infoApi = {
  // 获取公告列表
  getAnnouncements: (params) => src_utils_request.request.get("/announcements", params),
  // 获取公告详情
  getAnnouncementDetail: (id) => src_utils_request.request.get(`/announcements/${id}`),
  // 获取政策法规列表
  getPolicies: (params) => src_utils_request.request.get("/policies", params),
  // 获取重要通知列表
  getNotices: (params) => src_utils_request.request.get("/notices", params)
};
const studyApi = {
  // 获取题库分类
  getCategories: () => src_utils_request.request.get("/study/categories"),
  // 获取题目列表
  getQuestions: (categoryId, count = 10) => src_utils_request.request.get("/study/questions", { categoryId, count }),
  // 提交练习结果
  submitPractice: (data) => src_utils_request.request.post("/study/practice", data),
  // 获取练习历史
  getPracticeHistory: (params) => src_utils_request.request.get("/study/practice/history", params),
  // 获取学习统计
  getStudyStats: () => src_utils_request.request.get("/study/stats")
};
const examApi = {
  // 获取当前考试列表
  getCurrentExams: () => src_utils_request.request.get("/exams/current"),
  // 获取考试详情
  getExamDetail: (id) => src_utils_request.request.get(`/exams/${id}`),
  // 获取考试题目
  getExamQuestions: (examId) => src_utils_request.request.get(`/exams/${examId}/questions`),
  // 开始考试
  startExam: (examId) => src_utils_request.request.post(`/exams/${examId}/start`),
  // 人脸识别验证
  verifyFace: (data) => src_utils_request.request.post("/exams/verify-face", data),
  // 提交答案
  submitAnswers: (data) => src_utils_request.request.post("/exams/submit", data),
  // 获取考试记录
  getExamRecords: (params) => src_utils_request.request.get("/exams/records", params),
  // 获取线下考试场地
  getExamVenues: (examId) => src_utils_request.request.get(`/exams/${examId}/venues`),
  // 预约线下考试
  bookOfflineExam: (data) => src_utils_request.request.post("/exams/book", data),
  // 取消预约
  cancelBooking: (registrationId) => src_utils_request.request.delete(`/exams/booking/${registrationId}`),
  // 上传防作弊日志
  uploadAntiCheatLog: (data) => src_utils_request.request.post("/exams/anti-cheat-log", data)
};
const personalApi = {
  // 获取证书列表
  getCertificates: () => src_utils_request.request.get("/certificates"),
  // 获取证书详情
  getCertificateDetail: (id) => src_utils_request.request.get(`/certificates/${id}`),
  // 下载证书
  downloadCertificate: (id) => src_utils_request.request.get(`/certificates/${id}/download`),
  // 提交反馈
  submitFeedback: (data) => src_utils_request.request.post("/feedback", data),
  // 获取关于我们信息
  getAboutInfo: () => src_utils_request.request.get("/about")
};
const commonApi = {
  // 获取机构列表
  getOrganizations: (keyword) => src_utils_request.request.get("/common/organizations", { keyword }),
  // 获取职位列表
  getPositions: (keyword) => src_utils_request.request.get("/common/positions", { keyword }),
  // 上传文件
  uploadFile: (filePath, type = "image") => src_utils_request.request.upload({
    url: "/common/upload",
    filePath,
    name: "file",
    formData: { type }
  }),
  // 获取系统配置
  getSystemConfig: () => src_utils_request.request.get("/common/config")
};
const api = {
  user: userApi,
  info: infoApi,
  study: studyApi,
  exam: examApi,
  personal: personalApi,
  common: commonApi
};
exports.api = api;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/src/api/index.js.map
