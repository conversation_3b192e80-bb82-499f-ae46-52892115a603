{"version": 3, "file": "app.js", "sources": ["App.vue", "main.js"], "sourcesContent": ["<script setup lang=\"ts\">\r\nimport { onLaunch, onShow, onHide } from '@dcloudio/uni-app'\r\nimport { useUserStore } from './src/stores/user'\r\nimport { useAppStore } from './src/stores/app'\r\nimport { useStudyStore } from './src/stores/study'\r\nimport { initRouterGuard } from './src/utils/router-guard'\r\n\r\n// Store\r\nconst userStore = useUserStore()\r\nconst appStore = useAppStore()\r\nconst studyStore = useStudyStore()\r\n\r\nonLaunch(() => {\r\n  uni.__f__('log','at App.vue:14','疾控医护考试系统启动')\r\n\r\n  // 初始化应用\r\n  initApp()\r\n})\r\n\r\nonShow(() => {\r\n  uni.__f__('log','at App.vue:21','App Show')\r\n\r\n  // 检查网络状态\r\n  appStore.checkNetworkStatus()\r\n})\r\n\r\nonHide(() => {\r\n  uni.__f__('log','at App.vue:28','App Hide')\r\n})\r\n\r\n// 初始化应用\r\nconst initApp = async () => {\r\n  try {\r\n    // 初始化应用状态\r\n    await appStore.initApp()\r\n\r\n    // 从本地存储恢复用户状态\r\n    userStore.initFromStorage()\r\n\r\n    // 初始化学习状态\r\n    studyStore.initStudyStore()\r\n\r\n    // 初始化路由守卫\r\n    initRouterGuard()\r\n\r\n    uni.__f__('log','at App.vue:46','应用初始化完成')\r\n  } catch (error) {\r\n    uni.__f__('error','at App.vue:48','应用初始化失败:', error)\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n// 引入uni.scss变量（包含uview-plus主题变量）\r\n@import 'uni.scss';\r\n\r\n// 引入uview-plus样式\r\n@import 'uni_modules/uview-plus/index.scss';\r\n\r\n// 引入全局样式\r\n@import 'src/styles/global.scss';\r\n\r\n/* 全局页面样式 */\r\npage {\r\n  background-color: #F8F9FA;\r\n  height: 100%;\r\n  font-size: 28rpx;\r\n  line-height: 1.6;\r\n  color: #262626;\r\n}\r\n\r\n/* H5 兼容样式 */\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n/* 通用样式重置已在global.scss中定义，此处不再重复 */\r\n\r\n/* 安全区域适配 */\r\n.safe-area-inset-top {\r\n  padding-top: constant(safe-area-inset-top);\r\n  padding-top: env(safe-area-inset-top);\r\n}\r\n\r\n.safe-area-inset-bottom {\r\n  padding-bottom: constant(safe-area-inset-bottom);\r\n  padding-bottom: env(safe-area-inset-bottom);\r\n}\r\n\r\n/* App.vue特有样式 - 避免与global.scss重复 */\r\n</style>\r\n", "import App from './App'\r\nimport { createSSRApp } from 'vue'\r\nimport { createPinia } from 'pinia'\r\n// 引入uview-plus\r\nimport uviewPlus from './uni_modules/uview-plus/index.js'\r\n\r\nexport function createApp() {\r\n\tconst app = createSSRApp(App)\r\n\r\n\t// 使用 Pinia 状态管理\r\n\tconst pinia = createPinia()\r\n\tapp.use(pinia)\r\n\r\n\t// 使用 uview-plus UI库\r\n\tapp.use(uviewPlus)\r\n\r\n\t// 全局配置\r\n\tapp.config.globalProperties.$appName = '疾控医护考试系统'\r\n\tapp.config.globalProperties.$version = '1.0.0'\r\n\r\n\treturn {\r\n\t\tapp,\r\n\t\tPinia: createPinia\r\n\t}\r\n}\r\n"], "names": ["useUserStore", "useAppStore", "useStudyStore", "onLaunch", "uni", "onShow", "onHide", "initRouterGuard", "createSSRApp", "App", "createPinia", "uviewPlus"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,UAAM,YAAYA,gBAAAA;AAClB,UAAM,WAAWC,eAAAA;AACjB,UAAM,aAAaC,iBAAAA;AAEnBC,kBAAAA,SAAS,MAAM;AACTC,oBAAAA,MAAA,MAAM,OAAM,iBAAgB,YAAY;AAGpC;IAAA,CACT;AAEDC,kBAAAA,OAAO,MAAM;AACPD,oBAAAA,MAAA,MAAM,OAAM,iBAAgB,UAAU;AAG1C,eAAS,mBAAmB;AAAA,IAAA,CAC7B;AAEDE,kBAAAA,OAAO,MAAM;AACPF,oBAAAA,MAAA,MAAM,OAAM,iBAAgB,UAAU;AAAA,IAAA,CAC3C;AAGD,UAAM,UAAU,MAAY;AACtB,UAAA;AAEF,cAAM,SAAS;AAGf,kBAAU,gBAAgB;AAG1B,mBAAW,eAAe;AAGVG,8BAAAA;AAEZH,sBAAAA,MAAA,MAAM,OAAM,iBAAgB,SAAS;AAAA,eAClC,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,iBAAgB,YAAY,KAAK;AAAA,MACrD;AAAA,IAAA;;;;;AC1CK,SAAS,YAAY;AAC3B,QAAM,MAAMI,cAAY,aAACC,SAAG;AAG5B,QAAM,QAAQC,cAAAA,YAAa;AAC3B,MAAI,IAAI,KAAK;AAGb,MAAI,IAAIC,qCAAS;AAGjB,MAAI,OAAO,iBAAiB,WAAW;AACvC,MAAI,OAAO,iBAAiB,WAAW;AAEvC,SAAO;AAAA,IACN;AAAA,IACA,OAAOD,cAAW;AAAA,EAClB;AACF;AAEA,YAAY,IAAI,MAAM,MAAM;;"}