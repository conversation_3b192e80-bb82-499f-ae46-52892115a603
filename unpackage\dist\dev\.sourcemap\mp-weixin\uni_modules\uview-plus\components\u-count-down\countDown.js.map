{"version": 3, "file": "countDown.js", "sources": ["uni_modules/uview-plus/components/u-count-down/countDown.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:11:29\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/countDown.js\n */\nexport default {\n    // u-count-down 计时器组件\n    countDown: {\n        time: 0,\n        format: 'HH:mm:ss',\n        autoStart: true,\n        millisecond: false\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,YAAA;AAAA;AAAA,EAEX,WAAW;AAAA,IACP,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,aAAa;AAAA,EAChB;AACL;;"}