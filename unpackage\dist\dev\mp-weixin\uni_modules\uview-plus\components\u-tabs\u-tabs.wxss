/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-02b0c54f,
.u-empty__wrap.data-v-02b0c54f,
.u-tabs.data-v-02b0c54f,
.u-tabs__wrapper.data-v-02b0c54f,
.u-tabs__wrapper__scroll-view-wrapper.data-v-02b0c54f,
.u-tabs__wrapper__scroll-view.data-v-02b0c54f,
.u-tabs__wrapper__nav.data-v-02b0c54f,
.u-tabs__wrapper__nav__line.data-v-02b0c54f,
.up-empty.data-v-02b0c54f,
.up-empty__wrap.data-v-02b0c54f,
.up-tabs.data-v-02b0c54f,
.up-tabs__wrapper.data-v-02b0c54f,
.up-tabs__wrapper__scroll-view-wrapper.data-v-02b0c54f,
.up-tabs__wrapper__scroll-view.data-v-02b0c54f,
.up-tabs__wrapper__nav.data-v-02b0c54f,
.up-tabs__wrapper__nav__line.data-v-02b0c54f {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-tabs__wrapper.data-v-02b0c54f {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-tabs__wrapper__scroll-view-wrapper.data-v-02b0c54f {
  flex: 1;
  overflow: auto hidden;
}
.u-tabs__wrapper__scroll-view.data-v-02b0c54f {
  display: flex;
  flex-direction: row;
  flex: 1;
}
.u-tabs__wrapper__nav.data-v-02b0c54f {
  display: flex;
  flex-direction: row;
  position: relative;
}
.u-tabs__wrapper__nav__item.data-v-02b0c54f {
  padding: 0 11px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.u-tabs__wrapper__nav__item__text.data-v-02b0c54f {
  font-size: 15px;
  color: #606266;
  white-space: nowrap !important;
}
.u-tabs__wrapper__nav__item__text--disabled.data-v-02b0c54f {
  color: #c8c9cc !important;
}
.u-tabs__wrapper__nav__line.data-v-02b0c54f {
  height: 3px;
  background: #3c9cff;
  width: 30px;
  position: absolute;
  bottom: 2px;
  border-radius: 100px;
  transition-property: transform;
  transition-duration: 300ms;
}