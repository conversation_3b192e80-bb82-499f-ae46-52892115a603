{"version": 3, "file": "datetimePicker.js", "sources": ["uni_modules/uview-plus/components/u-datetime-picker/datetimePicker.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:57:48\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/datetimePicker.js\n */\nexport default {\n    // datetimePicker 组件\n    datetimePicker: {\n        show: false,\n\t\tpopupMode: 'bottom',\n        showToolbar: true,\n        value: '',\n        title: '',\n        mode: 'datetime',\n        maxDate: new Date(new Date().getFullYear() + 10, 0, 1).getTime(),\n        minDate: new Date(new Date().getFullYear() - 10, 0, 1).getTime(),\n        minHour: 0,\n        maxHour: 23,\n        minMinute: 0,\n        maxMinute: 59,\n        filter: null,\n        formatter: null,\n        loading: false,\n        itemHeight: 44,\n        cancelText: '取消',\n        confirmText: '确认',\n        cancelColor: '#909193',\n        confirmColor: '#3c9cff',\n        visibleItemCount: 5,\n        closeOnClickOverlay: false,\n        defaultIndex: [],\n        inputBorder: 'surround',\n        disabled: false,\n        disabledColor: '',\n        placeholder: '请选择',\n        inputProps: {},\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,iBAAA;AAAA;AAAA,EAEX,gBAAgB;AAAA,IACZ,MAAM;AAAA,IACZ,WAAW;AAAA,IACL,aAAa;AAAA,IACb,OAAO;AAAA,IACP,OAAO;AAAA,IACP,MAAM;AAAA,IACN,SAAS,IAAI,MAAK,oBAAI,QAAO,gBAAgB,IAAI,GAAG,CAAC,EAAE,QAAS;AAAA,IAChE,SAAS,IAAI,MAAK,oBAAI,QAAO,gBAAgB,IAAI,GAAG,CAAC,EAAE,QAAS;AAAA,IAChE,SAAS;AAAA,IACT,SAAS;AAAA,IACT,WAAW;AAAA,IACX,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,SAAS;AAAA,IACT,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,aAAa;AAAA,IACb,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,qBAAqB;AAAA,IACrB,cAAc,CAAE;AAAA,IAChB,aAAa;AAAA,IACb,UAAU;AAAA,IACV,eAAe;AAAA,IACf,aAAa;AAAA,IACb,YAAY,CAAE;AAAA,EACjB;AACL;;"}