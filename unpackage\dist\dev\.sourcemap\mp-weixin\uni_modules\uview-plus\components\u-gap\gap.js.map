{"version": 3, "file": "gap.js", "sources": ["uni_modules/uview-plus/components/u-gap/gap.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:05:25\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/gap.js\n */\nexport default {\n    // gap组件\n    gap: {\n        bgColor: 'transparent',\n        height: 20,\n        marginTop: 0,\n        marginBottom: 0,\n        customStyle: {}\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,MAAA;AAAA;AAAA,EAEX,KAAK;AAAA,IACD,SAAS;AAAA,IACT,QAAQ;AAAA,IACR,WAAW;AAAA,IACX,cAAc;AAAA,IACd,aAAa,CAAE;AAAA,EAClB;AACL;;"}