{"version": 3, "file": "icon.js", "sources": ["uni_modules/uview-plus/components/u-icon/icon.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 18:00:14\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/icon.js\n */\nimport config from '../../libs/config/config'\n\nconst {\n    color\n} = config\nexport default {\n    // icon组件\n    icon: {\n        name: '',\n        color: color['u-content-color'],\n        size: '16px',\n        bold: false,\n        index: '',\n        hoverClass: '',\n        customPrefix: 'uicon',\n        label: '',\n        labelPos: 'right',\n        labelSize: '15px',\n        labelColor: color['u-content-color'],\n        space: '3px',\n        imgMode: '',\n        width: '',\n        height: '',\n        top: 0,\n        stop: false\n    }\n}\n"], "names": ["config"], "mappings": ";;AAWA,MAAM;AAAA,EACF;AACJ,IAAIA,yCAAM;AACV,MAAe,OAAA;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,MAAM;AAAA,IACN,OAAO,MAAM,iBAAiB;AAAA,IAC9B,MAAM;AAAA,IACN,MAAM;AAAA,IACN,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,OAAO;AAAA,IACP,UAAU;AAAA,IACV,WAAW;AAAA,IACX,YAAY,MAAM,iBAAiB;AAAA,IACnC,OAAO;AAAA,IACP,SAAS;AAAA,IACT,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,EACT;AACL;;"}