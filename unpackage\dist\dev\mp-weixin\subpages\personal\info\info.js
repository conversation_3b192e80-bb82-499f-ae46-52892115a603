"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
const src_stores_user = require("../../../src/stores/user.js");
const src_stores_app = require("../../../src/stores/app.js");
const src_utils_index = require("../../../src/utils/index.js");
const src_api_index = require("../../../src/api/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_input2 = common_vendor.resolveComponent("u-input");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_navbar2 + _easycom_u_icon2 + _easycom_u_input2 + _easycom_u_button2)();
}
const _easycom_u_navbar = () => "../../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_input = () => "../../../uni_modules/uview-plus/components/u-input/u-input.js";
const _easycom_u_button = () => "../../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_navbar + UserStatusBanner + _easycom_u_icon + _easycom_u_input + StatusTag + _easycom_u_button)();
}
const UserStatusBanner = () => "../../../src/components/common/UserStatusBanner.js";
const StatusTag = () => "../../../src/components/common/StatusTag.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "info",
  setup(__props) {
    const userStore = src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const formData = common_vendor.ref({
      realName: "",
      idCard: "",
      phone: "",
      organization: "",
      department: "",
      position: "",
      photo: ""
    });
    const isSaving = common_vendor.ref(false);
    const canEdit = common_vendor.ref(false);
    const userInfo = common_vendor.computed(() => userStore.userInfo);
    const inputStyle = common_vendor.computed(() => ({
      backgroundColor: canEdit.value ? "#fff" : "#f8f9fa",
      color: canEdit.value ? "#333" : "#999"
    }));
    common_vendor.onMounted(() => {
      initFormData();
      checkEditPermission();
    });
    const initFormData = () => {
      if (userInfo.value) {
        formData.value = {
          realName: userInfo.value.realName || "",
          idCard: userInfo.value.idCard || "",
          phone: userInfo.value.phone || "",
          organization: userInfo.value.organization || "",
          department: userInfo.value.department || "",
          position: userInfo.value.position || "",
          photo: userInfo.value.photo || ""
        };
      }
    };
    const checkEditPermission = () => {
      var _a;
      const status = (_a = userInfo.value) == null ? void 0 : _a.status;
      canEdit.value = status === "not_submitted" || status === "rejected";
    };
    const getReadonlyTip = () => {
      var _a;
      const status = (_a = userInfo.value) == null ? void 0 : _a.status;
      switch (status) {
        case "pending":
          return "资料审核中，暂时无法修改";
        case "approved":
          return "资料已通过审核，如需修改请联系管理员";
        default:
          return "暂时无法修改个人信息";
      }
    };
    const enableEdit = () => {
      canEdit.value = true;
    };
    const chooseAvatar = () => {
      if (!canEdit.value)
        return;
      common_vendor.index.chooseImage({
        count: 1,
        sizeType: ["compressed"],
        sourceType: ["album", "camera"],
        success: (res) => {
          const tempFilePath = res.tempFilePaths[0];
          uploadAvatar(tempFilePath);
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at subpages/personal/info/info.vue:272", "选择图片失败:", error);
          appStore.showToast("选择图片失败");
        }
      });
    };
    const uploadAvatar = (filePath) => __async(this, null, function* () {
      appStore.showLoading("上传中...");
      try {
        formData.value.photo = filePath;
        appStore.hideLoading();
        appStore.showToast("头像上传成功", "success");
      } catch (error) {
        appStore.hideLoading();
        common_vendor.index.__f__("error", "at subpages/personal/info/info.vue:293", "上传头像失败:", error);
        appStore.showToast(error.message || "上传失败");
      }
    });
    const saveUserInfo = () => __async(this, null, function* () {
      var _a;
      if (!formData.value.realName.trim()) {
        appStore.showToast("请输入真实姓名");
        return;
      }
      if (!formData.value.idCard.trim()) {
        appStore.showToast("请输入身份证号码");
        return;
      }
      if (!formData.value.phone.trim()) {
        appStore.showToast("请输入手机号码");
        return;
      }
      if (!formData.value.organization.trim()) {
        appStore.showToast("请输入所属机构");
        return;
      }
      const idCardRegex = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      if (!idCardRegex.test(formData.value.idCard)) {
        appStore.showToast("身份证号码格式不正确");
        return;
      }
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(formData.value.phone)) {
        appStore.showToast("手机号码格式不正确");
        return;
      }
      isSaving.value = true;
      try {
        yield src_api_index.api.user.updateUserInfo(formData.value);
        yield userStore.refreshUserInfo();
        appStore.showToast("保存成功", "success");
        canEdit.value = false;
        if (((_a = userInfo.value) == null ? void 0 : _a.status) === "not_submitted") {
          setTimeout(() => {
            appStore.navigateBack();
          }, 1500);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/personal/info/info.vue:353", "保存用户信息失败:", error);
        appStore.showToast(error.message || "保存失败，请重试");
      } finally {
        isSaving.value = false;
      }
    });
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k;
      return common_vendor.e({
        a: common_vendor.p({
          title: "个人信息",
          autoBack: true,
          background: {
            background: "linear-gradient(135deg, #2E8B57 0%, #228B22 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: common_vendor.p({
          showAction: false
        }),
        c: formData.value.photo || "/static/images/default-avatar.png",
        d: common_vendor.p({
          name: "camera",
          color: "#fff",
          size: "40"
        }),
        e: common_vendor.o(chooseAvatar),
        f: common_vendor.o(($event) => formData.value.realName = $event),
        g: common_vendor.p({
          placeholder: "请输入真实姓名",
          disabled: !canEdit.value,
          border: false,
          customStyle: inputStyle.value,
          modelValue: formData.value.realName
        }),
        h: common_vendor.o(($event) => formData.value.idCard = $event),
        i: common_vendor.p({
          placeholder: "请输入身份证号码",
          disabled: !canEdit.value,
          border: false,
          customStyle: inputStyle.value,
          modelValue: formData.value.idCard
        }),
        j: common_vendor.o(($event) => formData.value.phone = $event),
        k: common_vendor.p({
          placeholder: "请输入手机号码",
          type: "number",
          disabled: !canEdit.value,
          border: false,
          customStyle: inputStyle.value,
          modelValue: formData.value.phone
        }),
        l: common_vendor.o(($event) => formData.value.organization = $event),
        m: common_vendor.p({
          placeholder: "请输入所属机构",
          disabled: !canEdit.value,
          border: false,
          customStyle: inputStyle.value,
          modelValue: formData.value.organization
        }),
        n: common_vendor.o(($event) => formData.value.department = $event),
        o: common_vendor.p({
          placeholder: "请输入科室部门",
          disabled: !canEdit.value,
          border: false,
          customStyle: inputStyle.value,
          modelValue: formData.value.department
        }),
        p: common_vendor.o(($event) => formData.value.position = $event),
        q: common_vendor.p({
          placeholder: "请输入职务职称",
          disabled: !canEdit.value,
          border: false,
          customStyle: inputStyle.value,
          modelValue: formData.value.position
        }),
        r: ((_a = userInfo.value) == null ? void 0 : _a.status) !== "not_submitted"
      }, ((_b = userInfo.value) == null ? void 0 : _b.status) !== "not_submitted" ? common_vendor.e({
        s: common_vendor.p({
          type: "user",
          status: ((_c = userInfo.value) == null ? void 0 : _c.status) || ""
        }),
        t: common_vendor.t(common_vendor.unref(src_utils_index.formatDate)((_d = userInfo.value) == null ? void 0 : _d.submittedAt, "YYYY-MM-DD HH:mm")),
        v: (_e = userInfo.value) == null ? void 0 : _e.reviewedAt
      }, ((_f = userInfo.value) == null ? void 0 : _f.reviewedAt) ? {
        w: common_vendor.t(common_vendor.unref(src_utils_index.formatDate)((_g = userInfo.value) == null ? void 0 : _g.reviewedAt, "YYYY-MM-DD HH:mm"))
      } : {}, {
        x: (_h = userInfo.value) == null ? void 0 : _h.rejectReason
      }, ((_i = userInfo.value) == null ? void 0 : _i.rejectReason) ? {
        y: common_vendor.t(userInfo.value.rejectReason)
      } : {}) : {}, {
        z: canEdit.value
      }, canEdit.value ? {
        A: common_vendor.o(saveUserInfo),
        B: common_vendor.p({
          type: "primary",
          loading: isSaving.value,
          loadingText: "保存中..."
        })
      } : ((_j = userInfo.value) == null ? void 0 : _j.status) === "rejected" ? {
        D: common_vendor.o(enableEdit),
        E: common_vendor.p({
          type: "primary"
        })
      } : {
        F: common_vendor.t(getReadonlyTip())
      }, {
        C: ((_k = userInfo.value) == null ? void 0 : _k.status) === "rejected"
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4d59f12d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpages/personal/info/info.js.map
