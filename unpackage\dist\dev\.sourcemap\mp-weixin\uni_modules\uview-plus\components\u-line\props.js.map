{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-line/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        color: {\n            type: String,\n            default: () => defProps.line.color\n        },\n        // 长度，竖向时表现为高度，横向时表现为长度，可以为百分比，带px单位的值等\n        length: {\n            type: [String, Number],\n            default: () => defProps.line.length\n        },\n        // 线条方向，col-竖向，row-横向\n        direction: {\n            type: String,\n            default: () => defProps.line.direction\n        },\n        // 是否显示细边框\n        hairline: {\n            type: <PERSON><PERSON><PERSON>,\n            default: () => defProps.line.hairline\n        },\n        // 线条与上下左右元素的间距，字符串形式，如\"30px\"、\"20px 30px\"\n        margin: {\n            type: [String, Number],\n            default: () => defProps.line.margin\n        },\n        // 是否虚线，true-虚线，false-实线\n        dashed: {\n            type: Boolean,\n            default: () => defProps.line.dashed\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA,IACH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,QAAQ;AAAA,MACJ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA,EACJ;AACL,CAAC;;"}