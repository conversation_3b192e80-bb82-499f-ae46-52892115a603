{"version": 3, "file": "isAbsoluteURL.js", "sources": ["uni_modules/uview-plus/libs/luch-request/helpers/isAbsoluteURL.js"], "sourcesContent": ["'use strict'\r\n\r\n/**\r\n * Determines whether the specified URL is absolute\r\n *\r\n * @param {string} url The URL to test\r\n * @returns {boolean} True if the specified URL is absolute, otherwise false\r\n */\r\nexport default function isAbsoluteURL(url) {\r\n    // A URL is considered absolute if it begins with \"<scheme>://\" or \"//\" (protocol-relative URL).\r\n    // RFC 3986 defines scheme name as a sequence of characters beginning with a letter and followed\r\n    // by any combination of letters, digits, plus, period, or hyphen.\r\n    return /^([a-z][a-z\\d+\\-.]*:)?\\/\\//i.test(url)\r\n}\r\n"], "names": [], "mappings": ";AAQe,SAAS,cAAc,KAAK;AAIvC,SAAO,8BAA8B,KAAK,GAAG;AACjD;;"}