/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-df79975b,
.u-empty__wrap.data-v-df79975b,
.u-tabs.data-v-df79975b,
.u-tabs__wrapper.data-v-df79975b,
.u-tabs__wrapper__scroll-view-wrapper.data-v-df79975b,
.u-tabs__wrapper__scroll-view.data-v-df79975b,
.u-tabs__wrapper__nav.data-v-df79975b,
.u-tabs__wrapper__nav__line.data-v-df79975b,
.up-empty.data-v-df79975b,
.up-empty__wrap.data-v-df79975b,
.up-tabs.data-v-df79975b,
.up-tabs__wrapper.data-v-df79975b,
.up-tabs__wrapper__scroll-view-wrapper.data-v-df79975b,
.up-tabs__wrapper__scroll-view.data-v-df79975b,
.up-tabs__wrapper__nav.data-v-df79975b,
.up-tabs__wrapper__nav__line.data-v-df79975b {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-input.data-v-df79975b {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}
.u-input--radius.data-v-df79975b, .u-input--square.data-v-df79975b {
  border-radius: 4px;
}
.u-input--no-radius.data-v-df79975b {
  border-radius: 0;
}
.u-input--circle.data-v-df79975b {
  border-radius: 100px;
}
.u-input__content.data-v-df79975b {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.u-input__content__field-wrapper.data-v-df79975b {
  position: relative;
  display: flex;
  flex-direction: row;
  margin: 0;
  flex: 1;
}
.u-input__content__field-wrapper__field.data-v-df79975b {
  line-height: 26px;
  text-align: left;
  color: #303133;
  height: 24px;
  font-size: 15px;
  flex: 1;
}
.u-input__content__clear.data-v-df79975b {
  width: 20px;
  height: 20px;
  border-radius: 100px;
  background-color: #c6c7cb;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  transform: scale(0.82);
  margin-left: 4px;
}
.u-input__content__subfix-icon.data-v-df79975b {
  margin-left: 4px;
}
.u-input__content__prefix-icon.data-v-df79975b {
  margin-right: 4px;
}