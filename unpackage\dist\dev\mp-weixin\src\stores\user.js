"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const common_vendor = require("../../common/vendor.js");
const src_constants_index = require("../constants/index.js");
const useUserStore = common_vendor.defineStore("user", () => {
  const token = common_vendor.ref("");
  const userInfo = common_vendor.ref(null);
  const isLoggedIn = common_vendor.ref(false);
  const isAuthenticated = common_vendor.computed(() => {
    var _a;
    return isLoggedIn.value && ((_a = userInfo.value) == null ? void 0 : _a.status) === "approved";
  });
  const userStatusText = common_vendor.computed(() => {
    if (!userInfo.value)
      return "未登录";
    const statusMap = {
      not_submitted: "未提交资料",
      pending: "审核中",
      approved: "已认证",
      rejected: "审核未通过"
    };
    return statusMap[userInfo.value.status] || "未知状态";
  });
  const canAccessExam = common_vendor.computed(() => {
    var _a;
    return ((_a = userInfo.value) == null ? void 0 : _a.status) === "approved";
  });
  const canAccessFullFeatures = common_vendor.computed(() => {
    var _a;
    return ((_a = userInfo.value) == null ? void 0 : _a.status) === "approved";
  });
  const setToken = (newToken) => {
    token.value = newToken;
    isLoggedIn.value = !!newToken;
    if (newToken) {
      common_vendor.index.setStorageSync(src_constants_index.STORAGE_KEYS.TOKEN, newToken);
    } else {
      common_vendor.index.removeStorageSync(src_constants_index.STORAGE_KEYS.TOKEN);
    }
  };
  const setUserInfo = (user) => {
    userInfo.value = user;
    if (user) {
      common_vendor.index.setStorageSync(src_constants_index.STORAGE_KEYS.USER_INFO, JSON.stringify(user));
    } else {
      common_vendor.index.removeStorageSync(src_constants_index.STORAGE_KEYS.USER_INFO);
    }
  };
  const updateUserStatus = (status) => {
    if (userInfo.value) {
      userInfo.value.status = status;
      setUserInfo(userInfo.value);
    }
  };
  const login = (tokenValue, user) => {
    setToken(tokenValue);
    setUserInfo(user);
  };
  const logout = () => {
    setToken("");
    setUserInfo(null);
    common_vendor.index.removeStorageSync(src_constants_index.STORAGE_KEYS.PRACTICE_CACHE);
    common_vendor.index.removeStorageSync(src_constants_index.STORAGE_KEYS.EXAM_CACHE);
  };
  const initFromStorage = () => {
    try {
      const storedToken = common_vendor.index.getStorageSync(src_constants_index.STORAGE_KEYS.TOKEN);
      if (storedToken) {
        token.value = storedToken;
        isLoggedIn.value = true;
      }
      const storedUserInfo = common_vendor.index.getStorageSync(src_constants_index.STORAGE_KEYS.USER_INFO);
      if (storedUserInfo) {
        userInfo.value = JSON.parse(storedUserInfo);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at src/stores/user.ts:98", "Failed to init user store from storage:", error);
      logout();
    }
  };
  const updateProfile = (updates) => {
    if (userInfo.value) {
      const updatedUser = __spreadValues(__spreadValues({}, userInfo.value), updates);
      setUserInfo(updatedUser);
    }
  };
  return {
    // 状态
    token,
    userInfo,
    isLoggedIn,
    // 计算属性
    isAuthenticated,
    userStatusText,
    canAccessExam,
    canAccessFullFeatures,
    // 方法
    setToken,
    setUserInfo,
    updateUserStatus,
    login,
    logout,
    initFromStorage,
    updateProfile
  };
});
exports.useUserStore = useUserStore;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/src/stores/user.js.map
