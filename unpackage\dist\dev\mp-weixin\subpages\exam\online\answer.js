"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
const src_stores_app = require("../../../src/stores/app.js");
const src_utils_index = require("../../../src/utils/index.js");
const src_constants_index = require("../../../src/constants/index.js");
const src_api_index = require("../../../src/api/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_textarea2 = common_vendor.resolveComponent("u-textarea");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  (_easycom_u_navbar2 + _easycom_u_icon2 + _easycom_u_textarea2 + _easycom_u_button2 + _easycom_u_popup2 + _easycom_u_modal2)();
}
const _easycom_u_navbar = () => "../../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_textarea = () => "../../../uni_modules/uview-plus/components/u-textarea/u-textarea.js";
const _easycom_u_button = () => "../../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_u_popup = () => "../../../uni_modules/uview-plus/components/u-popup/u-popup.js";
const _easycom_u_modal = () => "../../../uni_modules/uview-plus/components/u-modal/u-modal.js";
if (!Math) {
  (_easycom_u_navbar + StatusTag + _easycom_u_icon + _easycom_u_textarea + _easycom_u_button + _easycom_u_popup + _easycom_u_modal)();
}
const StatusTag = () => "../../../src/components/common/StatusTag.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "answer",
  props: {
    examId: {},
    recordId: {}
  },
  setup(__props) {
    const appStore = src_stores_app.useAppStore();
    const props = __props;
    const examTitle = common_vendor.ref("正在考试");
    const questions = common_vendor.ref([]);
    const answers = common_vendor.ref({});
    const currentQuestionIndex = common_vendor.ref(0);
    const timeRemaining = common_vendor.ref(0);
    const examDuration = common_vendor.ref(0);
    const showAnswerSheet = common_vendor.ref(false);
    const showSubmitModal = common_vendor.ref(false);
    const showTimeWarning = common_vendor.ref(false);
    const isSubmitting = common_vendor.ref(false);
    let timer = null;
    let antiCheatLogs = [];
    const totalQuestions = common_vendor.computed(() => questions.value.length);
    const currentQuestion = common_vendor.computed(() => questions.value[currentQuestionIndex.value]);
    const isLastQuestion = common_vendor.computed(() => currentQuestionIndex.value === totalQuestions.value - 1);
    const progressPercentage = common_vendor.computed(() => {
      if (totalQuestions.value === 0)
        return 0;
      return (currentQuestionIndex.value + 1) / totalQuestions.value * 100;
    });
    const currentAnswer = common_vendor.computed({
      get: () => {
        if (!currentQuestion.value)
          return null;
        return answers.value[currentQuestion.value.id] || null;
      },
      set: (value) => {
        if (currentQuestion.value) {
          answers.value[currentQuestion.value.id] = value;
        }
      }
    });
    const hasAnswer = common_vendor.computed(() => {
      if (!currentQuestion.value)
        return false;
      const answer = currentAnswer.value;
      if (answer === null || answer === void 0)
        return false;
      if (currentQuestion.value.type === "multiple") {
        return Array.isArray(answer) && answer.length > 0;
      }
      if (currentQuestion.value.type === "essay") {
        return typeof answer === "string" && answer.trim().length > 0;
      }
      return true;
    });
    const isChoiceQuestion = common_vendor.computed(() => {
      var _a, _b;
      return ((_a = currentQuestion.value) == null ? void 0 : _a.type) === "single" || ((_b = currentQuestion.value) == null ? void 0 : _b.type) === "multiple";
    });
    const submitModalContent = common_vendor.computed(() => {
      const answeredCount = Object.keys(answers.value).length;
      const unansweredCount = totalQuestions.value - answeredCount;
      if (unansweredCount > 0) {
        return `您还有${unansweredCount}道题未作答，确定要提交考试吗？未作答的题目将按0分计算。`;
      } else {
        return "您已完成所有题目，确定要提交考试吗？提交后将无法修改答案。";
      }
    });
    common_vendor.onMounted(() => {
      loadExamData();
      startTimer();
      setupAntiCheat();
    });
    common_vendor.onUnmounted(() => {
      if (timer) {
        clearInterval(timer);
      }
      removeAntiCheat();
    });
    const loadExamData = () => __async(this, null, function* () {
      try {
        const response = yield src_api_index.api.exam.getExamQuestions(props.examId);
        questions.value = response.data;
        const examResponse = yield src_api_index.api.exam.getExamDetail(props.examId);
        const exam = examResponse.data;
        examTitle.value = exam.name;
        examDuration.value = exam.duration * 60;
        timeRemaining.value = examDuration.value;
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/exam/online/answer.vue:333", "加载考试数据失败:", error);
        appStore.showToast(error.message || "加载失败");
        appStore.navigateBack();
      }
    });
    const startTimer = () => {
      timer = setInterval(() => {
        timeRemaining.value--;
        if (timeRemaining.value === 300 && !showTimeWarning.value) {
          showTimeWarning.value = true;
        }
        if (timeRemaining.value <= src_constants_index.EXAM_CONFIG.AUTO_SUBMIT_BEFORE_END) {
          autoSubmitExam();
        }
      }, 1e3);
    };
    const formatTime = (seconds) => {
      return src_utils_index.formatDuration(seconds);
    };
    const getOptionLabel = (index) => {
      return String.fromCharCode(65 + index);
    };
    const getOptionClass = (index) => {
      const classes = ["option"];
      if (isOptionSelected(index)) {
        classes.push("selected");
      }
      return classes.join(" ");
    };
    const isOptionSelected = (index) => {
      if (!currentQuestion.value)
        return false;
      const answer = currentAnswer.value;
      if (currentQuestion.value.type === "multiple") {
        return Array.isArray(answer) && answer.includes(index);
      } else {
        return answer === index;
      }
    };
    const selectOption = (index) => {
      if (!currentQuestion.value)
        return;
      if (currentQuestion.value.type === "multiple") {
        let answer = Array.isArray(currentAnswer.value) ? [...currentAnswer.value] : [];
        const selectedIndex = answer.indexOf(index);
        if (selectedIndex > -1) {
          answer.splice(selectedIndex, 1);
        } else {
          answer.push(index);
        }
        currentAnswer.value = answer;
      } else {
        currentAnswer.value = index;
      }
    };
    const selectJudge = (value) => {
      currentAnswer.value = value;
    };
    const prevQuestion = () => {
      if (currentQuestionIndex.value > 0) {
        currentQuestionIndex.value--;
      }
    };
    const nextQuestion = () => {
      if (isLastQuestion.value) {
        confirmSubmitExam();
      } else {
        currentQuestionIndex.value++;
      }
    };
    const jumpToQuestion = (index) => {
      currentQuestionIndex.value = index;
      showAnswerSheet.value = false;
    };
    const getAnswerItemClass = (index) => {
      var _a;
      const classes = ["answer-item"];
      if (index === currentQuestionIndex.value) {
        classes.push("current");
      } else if (answers.value[(_a = questions.value[index]) == null ? void 0 : _a.id]) {
        classes.push("answered");
      } else {
        classes.push("unanswered");
      }
      return classes.join(" ");
    };
    const confirmSubmitExam = () => {
      showSubmitModal.value = true;
    };
    const submitExam = () => __async(this, null, function* () {
      if (isSubmitting.value)
        return;
      isSubmitting.value = true;
      showSubmitModal.value = false;
      try {
        if (timer) {
          clearInterval(timer);
          timer = null;
        }
        const duration = examDuration.value - timeRemaining.value;
        yield src_api_index.api.exam.submitAnswers({
          recordId: props.recordId,
          answers: answers.value,
          duration,
          antiCheatLogs
        });
        appStore.showToast("考试提交成功", "success");
        setTimeout(() => {
          appStore.redirectTo(src_constants_index.PAGE_PATHS.EXAM_HISTORY, { recordId: props.recordId });
        }, 1500);
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/exam/online/answer.vue:486", "提交考试失败:", error);
        appStore.showToast(error.message || "提交失败，请重试");
        startTimer();
      } finally {
        isSubmitting.value = false;
      }
    });
    const autoSubmitExam = () => {
      appStore.showToast("考试时间到，自动提交", "warning");
      submitExam();
    };
    const setupAntiCheat = () => {
      common_vendor.index.onAppHide(() => {
        logAntiCheat("app_hide", { timestamp: Date.now() });
      });
      common_vendor.index.onAppShow(() => {
        logAntiCheat("app_show", { timestamp: Date.now() });
      });
    };
    const removeAntiCheat = () => {
    };
    const logAntiCheat = (type, data) => {
      antiCheatLogs.push({
        type,
        data,
        timestamp: Date.now()
      });
      const switchCount = antiCheatLogs.filter((log) => log.type === "app_hide").length;
      if (switchCount >= src_constants_index.EXAM_CONFIG.MAX_SWITCH_COUNT) {
        appStore.showModal({
          title: "违规警告",
          content: "检测到您多次切换应用，这可能被视为作弊行为。请专心答题。",
          showCancelButton: false,
          confirmText: "我知道了"
        });
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: examTitle.value,
          autoBack: false,
          background: {
            background: "linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: common_vendor.t(currentQuestionIndex.value + 1),
        c: common_vendor.t(totalQuestions.value),
        d: common_vendor.t(formatTime(timeRemaining.value)),
        e: timeRemaining.value <= 300 ? 1 : "",
        f: progressPercentage.value + "%",
        g: currentQuestion.value
      }, currentQuestion.value ? common_vendor.e({
        h: common_vendor.p({
          type: "question",
          status: currentQuestion.value.type
        }),
        i: common_vendor.t(currentQuestion.value.score || 5),
        j: common_vendor.t(currentQuestion.value.title),
        k: isChoiceQuestion.value
      }, isChoiceQuestion.value ? {
        l: common_vendor.f(currentQuestion.value.options, (option, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(getOptionLabel(index)),
            b: isOptionSelected(index)
          }, isOptionSelected(index) ? {
            c: "466c6991-2-" + i0,
            d: common_vendor.p({
              name: currentQuestion.value.type === "multiple" ? "checkbox-mark" : "checkmark-circle-fill",
              color: "#4A90E2",
              size: "32"
            })
          } : {}, {
            e: common_vendor.t(option),
            f: index,
            g: common_vendor.n(getOptionClass(index)),
            h: common_vendor.o(($event) => selectOption(index), index)
          });
        })
      } : currentQuestion.value.type === "judge" ? {
        n: common_vendor.p({
          name: currentAnswer.value === true ? "checkmark-circle-fill" : "checkmark-circle",
          color: currentAnswer.value === true ? "#4CAF50" : "#ccc",
          size: "48"
        }),
        o: currentAnswer.value === true ? 1 : "",
        p: common_vendor.o(($event) => selectJudge(true)),
        q: common_vendor.p({
          name: currentAnswer.value === false ? "close-circle-fill" : "close-circle",
          color: currentAnswer.value === false ? "#f56c6c" : "#ccc",
          size: "48"
        }),
        r: currentAnswer.value === false ? 1 : "",
        s: common_vendor.o(($event) => selectJudge(false))
      } : currentQuestion.value.type === "essay" ? {
        v: common_vendor.o(($event) => currentAnswer.value = $event),
        w: common_vendor.p({
          placeholder: "请输入您的答案...",
          maxlength: 1e3,
          showWordLimit: true,
          height: "400",
          autoHeight: true,
          modelValue: currentAnswer.value
        })
      } : {}, {
        m: currentQuestion.value.type === "judge",
        t: currentQuestion.value.type === "essay"
      }) : {}, {
        x: currentQuestionIndex.value > 0
      }, currentQuestionIndex.value > 0 ? {
        y: common_vendor.o(prevQuestion),
        z: common_vendor.p({
          type: "info",
          plain: true
        })
      } : {}, {
        A: common_vendor.t(isLastQuestion.value ? "提交考试" : "下一题"),
        B: common_vendor.o(nextQuestion),
        C: common_vendor.p({
          type: "primary",
          disabled: !hasAnswer.value
        }),
        D: common_vendor.p({
          name: "grid",
          color: "#fff",
          size: "32"
        }),
        E: common_vendor.o(($event) => showAnswerSheet.value = true),
        F: common_vendor.t(formatTime(timeRemaining.value)),
        G: common_vendor.o(($event) => showAnswerSheet.value = false),
        H: common_vendor.p({
          name: "close",
          size: "32"
        }),
        I: common_vendor.f(questions.value, (question, index, i0) => {
          return {
            a: common_vendor.t(index + 1),
            b: question.id,
            c: common_vendor.n(getAnswerItemClass(index)),
            d: common_vendor.o(($event) => jumpToQuestion(index), question.id)
          };
        }),
        J: common_vendor.o(confirmSubmitExam),
        K: common_vendor.p({
          type: "warning"
        }),
        L: common_vendor.o(($event) => showAnswerSheet.value = $event),
        M: common_vendor.p({
          mode: "bottom",
          height: "70%",
          closeOnClickOverlay: true,
          modelValue: showAnswerSheet.value
        }),
        N: common_vendor.o(submitExam),
        O: common_vendor.o(($event) => showSubmitModal.value = false),
        P: common_vendor.o(($event) => showSubmitModal.value = $event),
        Q: common_vendor.p({
          title: "确认提交",
          content: submitModalContent.value,
          showCancelButton: true,
          confirmText: "确认提交",
          cancelText: "继续答题",
          modelValue: showSubmitModal.value
        }),
        R: common_vendor.o(($event) => showTimeWarning.value = false),
        S: common_vendor.o(($event) => showTimeWarning.value = $event),
        T: common_vendor.p({
          title: "时间提醒",
          content: "考试时间还剩5分钟，请抓紧时间完成答题！",
          showCancelButton: false,
          confirmText: "我知道了",
          modelValue: showTimeWarning.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-466c6991"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpages/exam/online/answer.js.map
