{"version": 3, "file": "profile.js", "sources": ["pages/profile/profile.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvcHJvZmlsZS9wcm9maWxlLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"profile-submit-container\">\n    <!-- 自定义导航栏 -->\n    <u-navbar \n      title=\"完善个人资料\" \n      :autoBack=\"true\"\n      :background=\"{ background: 'linear-gradient(135deg, #2E8B57 0%, #228B22 100%)' }\"\n      titleStyle=\"color: #fff; font-weight: bold;\"\n    />\n    \n    <!-- 主要内容 -->\n    <view class=\"main-content\">\n      <!-- 进度指示 -->\n      <view class=\"progress-section\">\n        <view class=\"progress-item active\">\n          <view class=\"progress-icon\">\n            <u-icon name=\"checkmark\" color=\"#fff\" size=\"24\" />\n          </view>\n          <text class=\"progress-text\">微信登录</text>\n        </view>\n        <view class=\"progress-line\"></view>\n        <view class=\"progress-item current\">\n          <view class=\"progress-icon\">2</view>\n          <text class=\"progress-text\">完善资料</text>\n        </view>\n        <view class=\"progress-line\"></view>\n        <view class=\"progress-item\">\n          <view class=\"progress-icon\">3</view>\n          <text class=\"progress-text\">等待审核</text>\n        </view>\n      </view>\n      \n      <!-- 表单区域 -->\n      <view class=\"form-container\">\n        <u-form :model=\"formData\" ref=\"formRef\" :rules=\"rules\" labelWidth=\"140\">\n          <!-- 基本信息 -->\n          <view class=\"form-section\">\n            <view class=\"section-title\">\n              <u-icon name=\"account\" color=\"#2E8B57\" size=\"32\" />\n              <text>基本信息</text>\n            </view>\n            \n            <u-form-item label=\"姓名\" prop=\"realName\" required>\n              <u-input \n                v-model=\"formData.realName\" \n                placeholder=\"请输入真实姓名\"\n                :clearable=\"true\"\n              />\n            </u-form-item>\n            \n            <u-form-item label=\"手机号码\" prop=\"phone\" required>\n              <u-input \n                v-model=\"formData.phone\" \n                placeholder=\"请输入手机号码\"\n                type=\"number\"\n                :clearable=\"true\"\n              />\n            </u-form-item>\n            \n            <u-form-item label=\"身份证号\" prop=\"idCard\" required>\n              <u-input \n                v-model=\"formData.idCard\" \n                placeholder=\"请输入身份证号码\"\n                :clearable=\"true\"\n              />\n            </u-form-item>\n          </view>\n          \n          <!-- 工作信息 -->\n          <view class=\"form-section\">\n            <view class=\"section-title\">\n              <u-icon name=\"home\" color=\"#2E8B57\" size=\"32\" />\n              <text>工作信息</text>\n            </view>\n            \n            <u-form-item label=\"隶属机构\" prop=\"organization\" required>\n              <u-input \n                v-model=\"formData.organization\" \n                placeholder=\"请选择隶属机构\"\n                :disabled=\"true\"\n                @click=\"showOrganizationPicker = true\"\n              >\n                <template #suffix>\n                  <u-icon name=\"arrow-right\" color=\"#c0c4cc\" />\n                </template>\n              </u-input>\n            </u-form-item>\n            \n            <u-form-item label=\"职位\" prop=\"position\" required>\n              <u-input \n                v-model=\"formData.position\" \n                placeholder=\"请选择职位\"\n                :disabled=\"true\"\n                @click=\"showPositionPicker = true\"\n              >\n                <template #suffix>\n                  <u-icon name=\"arrow-right\" color=\"#c0c4cc\" />\n                </template>\n              </u-input>\n            </u-form-item>\n          </view>\n          \n          <!-- 身份验证 -->\n          <view class=\"form-section\">\n            <view class=\"section-title\">\n              <u-icon name=\"camera\" color=\"#2E8B57\" size=\"32\" />\n              <text>身份验证</text>\n            </view>\n            \n            <view class=\"photo-upload-section\">\n              <view class=\"upload-tips\">\n                <u-icon name=\"info-circle\" color=\"#FF9500\" size=\"28\" />\n                <text class=\"tips-text\">请上传本人近期正面免冠证件照或清晰的生活照，确保五官清晰可见，用于线上考试人脸识别验证</text>\n              </view>\n              \n              <view class=\"upload-area\" @click=\"chooseImage\">\n                <view v-if=\"!formData.photo\" class=\"upload-placeholder\">\n                  <u-icon name=\"camera-fill\" color=\"#ccc\" size=\"80\" />\n                  <text class=\"upload-text\">点击上传本人照片</text>\n                  <text class=\"upload-size\">支持JPG、PNG格式，不超过200KB</text>\n                </view>\n                <image \n                  v-else \n                  :src=\"formData.photo\" \n                  class=\"uploaded-image\" \n                  mode=\"aspectFill\"\n                />\n                <view v-if=\"formData.photo\" class=\"image-actions\">\n                  <view class=\"action-btn\" @click.stop=\"chooseImage\">\n                    <u-icon name=\"camera\" color=\"#fff\" size=\"24\" />\n                  </view>\n                  <view class=\"action-btn\" @click.stop=\"deleteImage\">\n                    <u-icon name=\"trash\" color=\"#fff\" size=\"24\" />\n                  </view>\n                </view>\n              </view>\n            </view>\n          </view>\n        </u-form>\n        \n        <!-- 操作按钮 -->\n        <view class=\"action-buttons\">\n          <u-button \n            class=\"skip-btn\"\n            type=\"info\"\n            plain\n            @click=\"skipSubmit\"\n          >\n            跳过，先去学习\n          </u-button>\n          \n          <u-button \n            class=\"submit-btn\"\n            type=\"primary\"\n            :loading=\"isSubmitting\"\n            loadingText=\"提交中...\"\n            @click=\"handleSubmit\"\n          >\n            提交审核\n          </u-button>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 机构选择器 -->\n    <u-picker\n      v-model=\"showOrganizationPicker\"\n      mode=\"selector\"\n      :range=\"organizationList\"\n      @confirm=\"onOrganizationConfirm\"\n      @cancel=\"showOrganizationPicker = false\"\n    />\n    \n    <!-- 职位选择器 -->\n    <u-picker\n      v-model=\"showPositionPicker\"\n      mode=\"selector\"\n      :range=\"positionList\"\n      @confirm=\"onPositionConfirm\"\n      @cancel=\"showPositionPicker = false\"\n    />\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, reactive, onMounted } from 'vue'\nimport { useUserStore } from '../../src/stores/user'\nimport { useAppStore } from '../../src/stores/app'\nimport api from '../../src/api'\nimport { PAGE_PATHS, UPLOAD_CONFIG, REGEX } from '../../src/constants'\nimport { validatePhone, validateIdCard } from '../../src/utils'\n\n// Store\nconst userStore = useUserStore()\nconst appStore = useAppStore()\n\n// 响应式数据\nconst isSubmitting = ref(false)\nconst showOrganizationPicker = ref(false)\nconst showPositionPicker = ref(false)\n\n// 表单数据\nconst formData = reactive({\n  realName: '',\n  phone: '',\n  idCard: '',\n  organization: '',\n  position: '',\n  photo: ''\n})\n\n// 表单验证规则\nconst rules = {\n  realName: [\n    { required: true, message: '请输入姓名', trigger: 'blur' },\n    { min: 2, max: 10, message: '姓名长度应为2-10个字符', trigger: 'blur' }\n  ],\n  phone: [\n    { required: true, message: '请输入手机号码', trigger: 'blur' },\n    { \n      validator: (rule: any, value: string) => validatePhone(value), \n      message: '请输入正确的手机号码', \n      trigger: 'blur' \n    }\n  ],\n  idCard: [\n    { required: true, message: '请输入身份证号码', trigger: 'blur' },\n    { \n      validator: (rule: any, value: string) => validateIdCard(value), \n      message: '请输入正确的身份证号码', \n      trigger: 'blur' \n    }\n  ],\n  organization: [\n    { required: true, message: '请选择隶属机构', trigger: 'change' }\n  ],\n  position: [\n    { required: true, message: '请选择职位', trigger: 'change' }\n  ]\n}\n\n// 机构列表\nconst organizationList = [\n  '市疾病预防控制中心',\n  '区疾病预防控制中心',\n  '县疾病预防控制中心',\n  '社区卫生服务中心',\n  '乡镇卫生院',\n  '其他医疗机构'\n]\n\n// 职位列表  \nconst positionList = [\n  '疾控科医师',\n  '预防保健科医师',\n  '接种门诊医师',\n  '产科医师',\n  '犬伤门诊医师',\n  '护士',\n  '公共卫生医师',\n  '其他'\n]\n\nconst formRef = ref()\n\n// 机构选择确认\nconst onOrganizationConfirm = (e: any) => {\n  formData.organization = organizationList[e.value[0]]\n  showOrganizationPicker.value = false\n}\n\n// 职位选择确认\nconst onPositionConfirm = (e: any) => {\n  formData.position = positionList[e.value[0]]\n  showPositionPicker.value = false\n}\n\n// 选择图片\nconst chooseImage = () => {\n  uni.chooseImage({\n    count: 1,\n    sizeType: ['compressed'],\n    sourceType: ['album', 'camera'],\n    success: (res) => {\n      const tempFilePath = res.tempFilePaths[0]\n      \n      // 检查文件大小\n      uni.getFileInfo({\n        filePath: tempFilePath,\n        success: (fileInfo) => {\n          if (fileInfo.size > UPLOAD_CONFIG.MAX_SIZE) {\n            appStore.showToast('图片大小不能超过200KB')\n            return\n          }\n          \n          formData.photo = tempFilePath\n          uploadImage(tempFilePath)\n        }\n      })\n    }\n  })\n}\n\n// 删除图片\nconst deleteImage = () => {\n  appStore.showModal({\n    title: '确认删除',\n    content: '确定要删除这张照片吗？'\n  }).then((confirmed) => {\n    if (confirmed) {\n      formData.photo = ''\n    }\n  })\n}\n\n// 上传图片到服务器\nconst uploadImage = async (filePath: string) => {\n  try {\n    appStore.showLoading('上传中...')\n    \n    const response = await api.user.uploadPhoto(filePath)\n    formData.photo = response.data.url\n    \n    appStore.hideLoading()\n    appStore.showToast('照片上传成功', 'success')\n  } catch (error: any) {\n    appStore.hideLoading()\n    appStore.showToast(error.message || '照片上传失败，请重试')\n    formData.photo = ''\n  }\n}\n\n// 跳过提交\nconst skipSubmit = () => {\n  appStore.showModal({\n    title: '确认跳过',\n    content: '跳过资料提交后，您只能使用部分功能。建议完善资料后使用完整功能。',\n    confirmText: '确认跳过',\n    cancelText: '继续完善'\n  }).then((confirmed) => {\n    if (confirmed) {\n      // 跳转到学习中心\n      appStore.switchTab(PAGE_PATHS.STUDY)\n    }\n  })\n}\n\n// 提交表单\nconst handleSubmit = async () => {\n  try {\n    // 表单验证\n    const valid = await formRef.value.validate()\n    if (!valid) return\n    \n    // 检查照片是否上传\n    if (!formData.photo) {\n      appStore.showToast('请上传本人照片')\n      return\n    }\n    \n    isSubmitting.value = true\n    \n    // 提交到服务器\n    const response = await api.user.submitProfile(formData)\n    \n    // 更新用户信息\n    userStore.updateProfile(response.data)\n    \n    appStore.showToast('资料提交成功', 'success')\n    \n    // 跳转到个人中心\n    setTimeout(() => {\n      appStore.switchTab(PAGE_PATHS.PERSONAL)\n    }, 1500)\n  } catch (error: any) {\n    uni.__f__('error','at pages/profile/profile.vue:376','提交失败:', error)\n    appStore.showToast(error.message || '提交失败，请重试')\n  } finally {\n    isSubmitting.value = false\n  }\n}\n\n// 页面加载时初始化用户信息\nonMounted(() => {\n  if (userStore.userInfo) {\n    const user = userStore.userInfo\n    formData.realName = user.realName || ''\n    formData.phone = user.phone || ''\n    formData.idCard = user.idCard || ''\n    formData.organization = user.organization || ''\n    formData.position = user.position || ''\n    formData.photo = user.photo || ''\n  }\n})\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../src/styles/global.scss';\n\n.profile-submit-container {\n  min-height: 100vh;\n  background: $acdc-bg-primary;\n}\n\n.main-content {\n  padding: 40rpx 30rpx;\n}\n\n.progress-section {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-bottom: 60rpx;\n  padding: 40rpx 20rpx;\n  background: #fff;\n  border-radius: 24rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  \n  .progress-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    flex: 1;\n    \n    .progress-icon {\n      width: 60rpx;\n      height: 60rpx;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 24rpx;\n      font-weight: bold;\n      margin-bottom: 16rpx;\n      background: #e9ecef;\n      color: #6c757d;\n    }\n    \n    .progress-text {\n      font-size: 24rpx;\n      color: #6c757d;\n    }\n    \n    &.active {\n      .progress-icon {\n        background: $acdc-primary;\n        color: #fff;\n      }\n      \n      .progress-text {\n        color: $acdc-primary;\n        font-weight: bold;\n      }\n    }\n    \n    &.current {\n      .progress-icon {\n        background: $uni-color-warning;\n        color: #fff;\n      }\n      \n      .progress-text {\n        color: $uni-color-warning;\n        font-weight: bold;\n      }\n    }\n  }\n  \n  .progress-line {\n    width: 60rpx;\n    height: 4rpx;\n    background: #e9ecef;\n    margin: 0 20rpx;\n    margin-bottom: 30rpx;\n  }\n}\n\n.form-container {\n  background: #fff;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n}\n\n.form-section {\n  margin-bottom: 60rpx;\n  \n  &:last-child {\n    margin-bottom: 40rpx;\n  }\n  \n  .section-title {\n    display: flex;\n    align-items: center;\n    margin-bottom: 40rpx;\n    padding-bottom: 20rpx;\n    border-bottom: 2rpx solid #f0f0f0;\n    \n    text {\n      margin-left: 16rpx;\n      font-size: 32rpx;\n      font-weight: bold;\n      color: $acdc-text-primary;\n    }\n  }\n}\n\n.photo-upload-section {\n  .upload-tips {\n    display: flex;\n    align-items: flex-start;\n    padding: 20rpx;\n    background: #fff7e6;\n    border-radius: 16rpx;\n    border-left: 6rpx solid $uni-color-warning;\n    margin-bottom: 40rpx;\n    \n    .tips-text {\n      flex: 1;\n      font-size: 24rpx;\n      color: #666;\n      line-height: 1.5;\n      margin-left: 12rpx;\n    }\n  }\n  \n  .upload-area {\n    position: relative;\n    width: 200rpx;\n    height: 200rpx;\n    border: 2rpx dashed #ddd;\n    border-radius: 16rpx;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin: 0 auto;\n    \n    .upload-placeholder {\n      display: flex;\n      flex-direction: column;\n      align-items: center;\n      text-align: center;\n      \n      .upload-text {\n        font-size: 24rpx;\n        color: #999;\n        margin: 16rpx 0 8rpx;\n      }\n      \n      .upload-size {\n        font-size: 20rpx;\n        color: #ccc;\n      }\n    }\n    \n    .uploaded-image {\n      width: 100%;\n      height: 100%;\n      border-radius: 14rpx;\n    }\n    \n    .image-actions {\n      position: absolute;\n      bottom: 8rpx;\n      right: 8rpx;\n      display: flex;\n      gap: 8rpx;\n      \n      .action-btn {\n        width: 48rpx;\n        height: 48rpx;\n        background: rgba(0, 0, 0, 0.6);\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n    }\n  }\n}\n\n.action-buttons {\n  display: flex;\n  gap: 24rpx;\n  margin-top: 60rpx;\n  \n  .skip-btn {\n    flex: 1;\n  }\n  \n  .submit-btn {\n    flex: 2;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/profile/profile.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useAppStore", "ref", "reactive", "validatePhone", "validateIdCard", "uni", "UPLOAD_CONFIG", "api", "PAGE_PATHS", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiMA,UAAM,YAAYA,gBAAAA;AAClB,UAAM,WAAWC,eAAAA;AAGX,UAAA,eAAeC,kBAAI,KAAK;AACxB,UAAA,yBAAyBA,kBAAI,KAAK;AAClC,UAAA,qBAAqBA,kBAAI,KAAK;AAGpC,UAAM,WAAWC,cAAAA,SAAS;AAAA,MACxB,UAAU;AAAA,MACV,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,cAAc;AAAA,MACd,UAAU;AAAA,MACV,OAAO;AAAA,IAAA,CACR;AAGD,UAAM,QAAQ;AAAA,MACZ,UAAU;AAAA,QACR,EAAE,UAAU,MAAM,SAAS,SAAS,SAAS,OAAO;AAAA,QACpD,EAAE,KAAK,GAAG,KAAK,IAAI,SAAS,iBAAiB,SAAS,OAAO;AAAA,MAC/D;AAAA,MACA,OAAO;AAAA,QACL,EAAE,UAAU,MAAM,SAAS,WAAW,SAAS,OAAO;AAAA,QACtD;AAAA,UACE,WAAW,CAAC,MAAW,UAAkBC,gBAAAA,cAAc,KAAK;AAAA,UAC5D,SAAS;AAAA,UACT,SAAS;AAAA,QACX;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,EAAE,UAAU,MAAM,SAAS,YAAY,SAAS,OAAO;AAAA,QACvD;AAAA,UACE,WAAW,CAAC,MAAW,UAAkBC,gBAAAA,eAAe,KAAK;AAAA,UAC7D,SAAS;AAAA,UACT,SAAS;AAAA,QACX;AAAA,MACF;AAAA,MACA,cAAc;AAAA,QACZ,EAAE,UAAU,MAAM,SAAS,WAAW,SAAS,SAAS;AAAA,MAC1D;AAAA,MACA,UAAU;AAAA,QACR,EAAE,UAAU,MAAM,SAAS,SAAS,SAAS,SAAS;AAAA,MACxD;AAAA,IAAA;AAIF,UAAM,mBAAmB;AAAA,MACvB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAIF,UAAM,eAAe;AAAA,MACnB;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IAAA;AAGF,UAAM,UAAUH,cAAAA;AAGV,UAAA,wBAAwB,CAAC,MAAW;AACxC,eAAS,eAAe,iBAAiB,EAAE,MAAM,CAAC,CAAC;AACnD,6BAAuB,QAAQ;AAAA,IAAA;AAI3B,UAAA,oBAAoB,CAAC,MAAW;AACpC,eAAS,WAAW,aAAa,EAAE,MAAM,CAAC,CAAC;AAC3C,yBAAmB,QAAQ;AAAA,IAAA;AAI7B,UAAM,cAAc,MAAM;AACxBI,oBAAAA,MAAI,YAAY;AAAA,QACd,OAAO;AAAA,QACP,UAAU,CAAC,YAAY;AAAA,QACvB,YAAY,CAAC,SAAS,QAAQ;AAAA,QAC9B,SAAS,CAAC,QAAQ;AACV,gBAAA,eAAe,IAAI,cAAc,CAAC;AAGxCA,wBAAAA,MAAI,YAAY;AAAA,YACd,UAAU;AAAA,YACV,SAAS,CAAC,aAAa;AACjB,kBAAA,SAAS,OAAOC,oBAAA,cAAc,UAAU;AAC1C,yBAAS,UAAU,eAAe;AAClC;AAAA,cACF;AAEA,uBAAS,QAAQ;AACjB,0BAAY,YAAY;AAAA,YAC1B;AAAA,UAAA,CACD;AAAA,QACH;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,cAAc,MAAM;AACxB,eAAS,UAAU;AAAA,QACjB,OAAO;AAAA,QACP,SAAS;AAAA,MAAA,CACV,EAAE,KAAK,CAAC,cAAc;AACrB,YAAI,WAAW;AACb,mBAAS,QAAQ;AAAA,QACnB;AAAA,MAAA,CACD;AAAA,IAAA;AAIG,UAAA,cAAc,CAAO,aAAqB;AAC1C,UAAA;AACF,iBAAS,YAAY,QAAQ;AAE7B,cAAM,WAAW,MAAMC,cAAI,IAAA,KAAK,YAAY,QAAQ;AAC3C,iBAAA,QAAQ,SAAS,KAAK;AAE/B,iBAAS,YAAY;AACZ,iBAAA,UAAU,UAAU,SAAS;AAAA,eAC/B,OAAY;AACnB,iBAAS,YAAY;AACZ,iBAAA,UAAU,MAAM,WAAW,YAAY;AAChD,iBAAS,QAAQ;AAAA,MACnB;AAAA,IAAA;AAIF,UAAM,aAAa,MAAM;AACvB,eAAS,UAAU;AAAA,QACjB,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,MAAA,CACb,EAAE,KAAK,CAAC,cAAc;AACrB,YAAI,WAAW;AAEJ,mBAAA,UAAUC,+BAAW,KAAK;AAAA,QACrC;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,eAAe,MAAY;AAC3B,UAAA;AAEF,cAAM,QAAQ,MAAM,QAAQ,MAAM,SAAS;AAC3C,YAAI,CAAC;AAAO;AAGR,YAAA,CAAC,SAAS,OAAO;AACnB,mBAAS,UAAU,SAAS;AAC5B;AAAA,QACF;AAEA,qBAAa,QAAQ;AAGrB,cAAM,WAAW,MAAMD,cAAI,IAAA,KAAK,cAAc,QAAQ;AAG5C,kBAAA,cAAc,SAAS,IAAI;AAE5B,iBAAA,UAAU,UAAU,SAAS;AAGtC,mBAAW,MAAM;AACN,mBAAA,UAAUC,+BAAW,QAAQ;AAAA,WACrC,IAAI;AAAA,eACA,OAAY;AACnBH,sBAAA,MAAI,MAAM,SAAQ,oCAAmC,SAAS,KAAK;AAC1D,iBAAA,UAAU,MAAM,WAAW,UAAU;AAAA,MAAA,UAC9C;AACA,qBAAa,QAAQ;AAAA,MACvB;AAAA,IAAA;AAIFI,kBAAAA,UAAU,MAAM;AACd,UAAI,UAAU,UAAU;AACtB,cAAM,OAAO,UAAU;AACd,iBAAA,WAAW,KAAK,YAAY;AAC5B,iBAAA,QAAQ,KAAK,SAAS;AACtB,iBAAA,SAAS,KAAK,UAAU;AACxB,iBAAA,eAAe,KAAK,gBAAgB;AACpC,iBAAA,WAAW,KAAK,YAAY;AAC5B,iBAAA,QAAQ,KAAK,SAAS;AAAA,MACjC;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxYD,GAAG,WAAW,eAAe;"}