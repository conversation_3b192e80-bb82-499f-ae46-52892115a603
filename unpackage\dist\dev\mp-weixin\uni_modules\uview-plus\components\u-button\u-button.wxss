/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-5ce41ee6,
.u-empty__wrap.data-v-5ce41ee6,
.u-tabs.data-v-5ce41ee6,
.u-tabs__wrapper.data-v-5ce41ee6,
.u-tabs__wrapper__scroll-view-wrapper.data-v-5ce41ee6,
.u-tabs__wrapper__scroll-view.data-v-5ce41ee6,
.u-tabs__wrapper__nav.data-v-5ce41ee6,
.u-tabs__wrapper__nav__line.data-v-5ce41ee6,
.up-empty.data-v-5ce41ee6,
.up-empty__wrap.data-v-5ce41ee6,
.up-tabs.data-v-5ce41ee6,
.up-tabs__wrapper.data-v-5ce41ee6,
.up-tabs__wrapper__scroll-view-wrapper.data-v-5ce41ee6,
.up-tabs__wrapper__scroll-view.data-v-5ce41ee6,
.up-tabs__wrapper__nav.data-v-5ce41ee6,
.up-tabs__wrapper__nav__line.data-v-5ce41ee6 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-button.data-v-5ce41ee6 {
  width: 100%;
  white-space: nowrap;
}
.u-button__text.data-v-5ce41ee6 {
  white-space: nowrap;
  line-height: 1;
}
.u-button.data-v-5ce41ee6:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  border: inherit;
  border-radius: inherit;
  transform: translate(-50%, -50%);
  opacity: 0;
  content: " ";
  background-color: #000;
  border-color: #000;
}
.u-button--active.data-v-5ce41ee6:before {
  opacity: 0.15;
}
.u-button__icon + .u-button__text.data-v-5ce41ee6:not(:empty), .u-button__loading-text.data-v-5ce41ee6 {
  margin-left: 4px;
}
.u-button--plain.u-button--primary.data-v-5ce41ee6 {
  color: #3c9cff;
}
.u-button--plain.u-button--info.data-v-5ce41ee6 {
  color: #909399;
}
.u-button--plain.u-button--success.data-v-5ce41ee6 {
  color: #5ac725;
}
.u-button--plain.u-button--error.data-v-5ce41ee6 {
  color: #f56c6c;
}
.u-button--plain.u-button--warning.data-v-5ce41ee6 {
  color: #f9ae3d;
}
.u-button.data-v-5ce41ee6 {
  height: 40px;
  position: relative;
  align-items: center;
  justify-content: center;
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  flex-direction: row;
}
.u-button__text.data-v-5ce41ee6 {
  font-size: 15px;
}
.u-button__loading-text.data-v-5ce41ee6 {
  font-size: 15px;
  margin-left: 4px;
}
.u-button--large.data-v-5ce41ee6 {
  width: 100%;
  height: 50px;
  padding: 0 15px;
}
.u-button--normal.data-v-5ce41ee6 {
  padding: 0 12px;
  font-size: 14px;
}
.u-button--small.data-v-5ce41ee6 {
  min-width: 60px;
  height: 30px;
  padding: 0px 8px;
  font-size: 12px;
}
.u-button--mini.data-v-5ce41ee6 {
  height: 22px;
  font-size: 10px;
  min-width: 50px;
  padding: 0px 8px;
}
.u-button--disabled.data-v-5ce41ee6 {
  opacity: 0.5;
}
.u-button--info.data-v-5ce41ee6 {
  color: #323233;
  background-color: #fff;
  border-color: #ebedf0;
  border-width: 1px;
  border-style: solid;
}
.u-button--success.data-v-5ce41ee6 {
  color: #fff;
  background-color: #5ac725;
  border-color: #5ac725;
  border-width: 1px;
  border-style: solid;
}
.u-button--primary.data-v-5ce41ee6 {
  color: #fff;
  background-color: #3c9cff;
  border-color: #3c9cff;
  border-width: 1px;
  border-style: solid;
}
.u-button--error.data-v-5ce41ee6 {
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
  border-width: 1px;
  border-style: solid;
}
.u-button--warning.data-v-5ce41ee6 {
  color: #fff;
  background-color: #f9ae3d;
  border-color: #f9ae3d;
  border-width: 1px;
  border-style: solid;
}
.u-button--block.data-v-5ce41ee6 {
  display: flex;
  flex-direction: row;
  width: 100%;
}
.u-button--circle.data-v-5ce41ee6 {
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
}
.u-button--square.data-v-5ce41ee6 {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}
.u-button__icon.data-v-5ce41ee6 {
  min-width: 1em;
  line-height: inherit !important;
  vertical-align: top;
}
.u-button--plain.data-v-5ce41ee6 {
  background-color: #fff;
}
.u-button--hairline.data-v-5ce41ee6 {
  border-width: 0.5px !important;
}