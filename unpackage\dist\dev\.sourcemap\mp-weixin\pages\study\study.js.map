{"version": 3, "file": "study.js", "sources": ["pages/study/study.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvc3R1ZHkvc3R1ZHkudnVl"], "sourcesContent": ["<template>\n  <view class=\"study-center-container\">\n    <!-- 状态栏安全区域 -->\n    <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n    \n    <!-- 自定义头部 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <text class=\"page-title\">学习中心</text>\n        <view class=\"header-actions\">\n          <view class=\"study-stats\">\n            <text class=\"stats-text\">今日已练习 {{ studyStore.dailyPracticeCount }}/{{ maxDailyPractice }} 组</text>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 用户状态提示 -->\n    <UserStatusBanner :showAction=\"true\" />\n    \n    <!-- 主要内容 -->\n    <view class=\"main-content\">\n      <!-- 学习统计卡片 -->\n      <view class=\"stats-card\">\n        <view class=\"stats-item\">\n          <text class=\"stats-number\">{{ studyStats.totalQuestions }}</text>\n          <text class=\"stats-label\">累计练习</text>\n        </view>\n        <view class=\"stats-divider\"></view>\n        <view class=\"stats-item\">\n          <text class=\"stats-number\">{{ studyStats.accuracy }}%</text>\n          <text class=\"stats-label\">正确率</text>\n        </view>\n        <view class=\"stats-divider\"></view>\n        <view class=\"stats-item\">\n          <text class=\"stats-number\">{{ studyStats.totalSessions }}</text>\n          <text class=\"stats-label\">练习次数</text>\n        </view>\n      </view>\n      \n      <!-- 学习模块 -->\n      <view class=\"study-modules\">\n        <!-- 教材学习模块（预留） -->\n        <view class=\"module-card disabled\" @click=\"handleTextbookClick\">\n          <view class=\"module-icon textbook\">\n            <u-icon name=\"book\" color=\"#4A90E2\" size=\"80\" />\n            <view class=\"coming-soon-badge\">\n              <text>即将上线</text>\n            </view>\n          </view>\n          <view class=\"module-content\">\n            <text class=\"module-title\">教材学习</text>\n            <text class=\"module-desc\">系统性学习专业教材内容</text>\n            <text class=\"module-status\">功能建设中，敬请期待</text>\n          </view>\n          <u-icon name=\"arrow-right\" color=\"#c0c4cc\" size=\"32\" />\n        </view>\n        \n        <!-- 题库练习模块 -->\n        <PermissionWrapper permission=\"auth\" :showFallback=\"false\">\n          <view class=\"module-card\" @click=\"handleQuestionBankClick\">\n            <view class=\"module-icon question-bank\">\n              <u-icon name=\"edit-pen\" color=\"#FF9500\" size=\"80\" />\n              <view v-if=\"!studyStore.canPracticeToday\" class=\"limit-badge\">\n                <text>今日已达上限</text>\n              </view>\n            </view>\n            <view class=\"module-content\">\n              <text class=\"module-title\">题库练习</text>\n              <text class=\"module-desc\">分类题库专项练习，即时反馈</text>\n              <text class=\"module-info\">{{ getQuestionBankInfo() }}</text>\n            </view>\n            <u-icon name=\"arrow-right\" color=\"#4A90E2\" size=\"32\" />\n          </view>\n        </PermissionWrapper>\n        \n        <!-- 未登录用户提示 -->\n        <PermissionWrapper permission=\"auth\" :showFallback=\"true\">\n          <template #fallback>\n            <view class=\"module-card login-prompt\" @click=\"goToLogin\">\n              <view class=\"module-icon login\">\n                <u-icon name=\"account\" color=\"#2E8B57\" size=\"80\" />\n              </view>\n              <view class=\"module-content\">\n                <text class=\"module-title\">登录后开始学习</text>\n                <text class=\"module-desc\">登录后可使用题库练习功能</text>\n                <text class=\"module-info\">每日免费练习3组题目</text>\n              </view>\n              <u-icon name=\"arrow-right\" color=\"#2E8B57\" size=\"32\" />\n            </view>\n          </template>\n        </PermissionWrapper>\n      </view>\n      \n      <!-- VIP特权预告（预留） -->\n      <view v-if=\"userStore.isLoggedIn\" class=\"vip-preview\">\n        <view class=\"vip-card\">\n          <view class=\"vip-header\">\n            <view class=\"vip-icon\">\n              <u-icon name=\"diamond\" color=\"#FFD700\" size=\"60\" />\n            </view>\n            <view class=\"vip-content\">\n              <text class=\"vip-title\">VIP会员特权</text>\n              <text class=\"vip-desc\">无限练习次数 · 更多题库内容 · 专属学习报告</text>\n            </view>\n          </view>\n          <view class=\"vip-features\">\n            <view class=\"feature-item\">\n              <u-icon name=\"checkmark-circle\" color=\"#4CAF50\" size=\"32\" />\n              <text>无限制刷题练习</text>\n            </view>\n            <view class=\"feature-item\">\n              <u-icon name=\"checkmark-circle\" color=\"#4CAF50\" size=\"32\" />\n              <text>专享VIP题库</text>\n            </view>\n            <view class=\"feature-item\">\n              <u-icon name=\"checkmark-circle\" color=\"#4CAF50\" size=\"32\" />\n              <text>详细学习报告</text>\n            </view>\n          </view>\n          <u-button \n            class=\"vip-btn\" \n            type=\"warning\"\n            @click=\"handleVipClick\"\n          >\n            即将开放，敬请期待\n          </u-button>\n        </view>\n      </view>\n      \n      <!-- 最近练习记录 -->\n      <view v-if=\"userStore.isLoggedIn && studyStore.practiceHistory.length > 0\" class=\"recent-practices\">\n        <view class=\"section-header\">\n          <text class=\"section-title\">最近练习</text>\n          <text class=\"section-more\" @click=\"viewAllPractices\">查看全部</text>\n        </view>\n        \n        <view class=\"practice-list\">\n          <view \n            v-for=\"practice in recentPractices\" \n            :key=\"practice.id\"\n            class=\"practice-item\"\n            @click=\"viewPracticeDetail(practice)\"\n          >\n            <view class=\"practice-info\">\n              <text class=\"practice-category\">{{ getCategoryName(practice.categoryId) }}</text>\n              <text class=\"practice-time\">{{ formatRelativeTime(practice.endTime || practice.startTime) }}</text>\n            </view>\n            <view class=\"practice-result\">\n              <text class=\"score\" :class=\"getScoreClass(practice.score || 0)\">\n                {{ practice.score || 0 }}分\n              </text>\n              <text class=\"accuracy\">{{ practice.correctCount || 0 }}/{{ practice.totalCount }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport { onShow } from '@dcloudio/uni-app'\nimport { useUserStore } from '../../src/stores/user'\nimport { useAppStore } from '../../src/stores/app'\nimport { useStudyStore } from '../../src/stores/study'\nimport { permissionManager } from '../../src/utils/permission'\nimport { formatRelativeTime } from '../../src/utils'\nimport { PAGE_PATHS, PRACTICE_CONFIG } from '../../src/constants'\nimport api from '../../src/api'\n\n// 导入组件\nimport UserStatusBanner from '../../src/components/common/UserStatusBanner.vue'\nimport PermissionWrapper from '../../src/components/common/PermissionWrapper.vue'\n\n// Store\nconst userStore = useUserStore()\nconst appStore = useAppStore()\nconst studyStore = useStudyStore()\n\n// 系统信息\nconst statusBarHeight = ref(0)\n\n// 配置\nconst maxDailyPractice = PRACTICE_CONFIG.QUESTIONS_PER_SESSION\n\n// 计算属性\nconst studyStats = computed(() => studyStore.getSessionStats())\n\nconst recentPractices = computed(() => {\n  return studyStore.practiceHistory.slice(0, 3)\n})\n\nonMounted(() => {\n  // 获取系统信息\n  const systemInfo = uni.getSystemInfoSync()\n  statusBarHeight.value = systemInfo.statusBarHeight || 0\n  \n  // 初始化数据\n  initData()\n})\n\nonShow(() => {\n  // 页面显示时刷新数据\n  if (userStore.isLoggedIn) {\n    loadStudyData()\n  }\n})\n\n// 初始化数据\nconst initData = async () => {\n  if (userStore.isLoggedIn) {\n    await loadStudyData()\n    await loadCategories()\n  }\n}\n\n// 加载学习数据\nconst loadStudyData = async () => {\n  try {\n    // 这里可以调用API获取最新的学习统计\n    // const response = await api.study.getStudyStats()\n    // 更新本地统计数据\n  } catch (error) {\n    uni.__f__('error','at pages/study/study.vue:226','加载学习数据失败:', error)\n  }\n}\n\n// 加载题库分类\nconst loadCategories = async () => {\n  try {\n    const response = await api.study.getCategories()\n    studyStore.setCategories(response.data)\n  } catch (error) {\n    uni.__f__('error','at pages/study/study.vue:236','加载题库分类失败:', error)\n  }\n}\n\n// 获取题库信息\nconst getQuestionBankInfo = () => {\n  if (!userStore.isLoggedIn) {\n    return '登录后可免费练习'\n  }\n  \n  if (userStore.isAuthenticated) {\n    return '认证用户可无限练习'\n  }\n  \n  if (!studyStore.canPracticeToday) {\n    return '免费练习已用完，明日可继续'\n  }\n  \n  return `免费练习 ${studyStore.remainingPracticeCount} 组剩余`\n}\n\n// 获取分类名称\nconst getCategoryName = (categoryId: string) => {\n  const category = studyStore.categories.find(c => c.id === categoryId)\n  return category?.name || '未知分类'\n}\n\n// 获取分数样式类\nconst getScoreClass = (score: number) => {\n  if (score >= 80) return 'good'\n  if (score >= 60) return 'normal'\n  return 'poor'\n}\n\n// 教材点击事件\nconst handleTextbookClick = () => {\n  appStore.showToast('教材功能正在建设中，敬请期待')\n}\n\n// 题库练习点击事件\nconst handleQuestionBankClick = () => {\n  // 检查权限\n  if (!permissionManager.enforceFeatureAccess('practice')) {\n    return\n  }\n  \n  if (!userStore.isAuthenticated && !studyStore.canPracticeToday) {\n    appStore.showModal({\n      title: '今日练习已达上限',\n      content: '免费用户每天可练习3组题目，明天可继续练习。完善个人资料并通过审核后可享受无限练习特权。',\n      confirmText: '完善资料',\n      cancelText: '我知道了'\n    }).then((confirmed) => {\n      if (confirmed) {\n        appStore.switchTab(PAGE_PATHS.PERSONAL)\n      }\n    })\n    return\n  }\n  \n  // 跳转到题库分类页面\n  appStore.navigateTo(PAGE_PATHS.STUDY_CATEGORY)\n}\n\n// 登录\nconst goToLogin = () => {\n  appStore.redirectTo(PAGE_PATHS.LOGIN)\n}\n\n// VIP点击事件\nconst handleVipClick = () => {\n  appStore.showModal({\n    title: 'VIP功能即将上线',\n    content: 'VIP会员功能正在开发中，上线后将为您提供更丰富的学习功能和特权。',\n    confirmText: '期待上线',\n    showCancel: false\n  })\n}\n\n// 查看所有练习记录\nconst viewAllPractices = () => {\n  appStore.showToast('练习历史功能开发中')\n}\n\n// 查看练习详情\nconst viewPracticeDetail = (practice: any) => {\n  appStore.navigateTo(PAGE_PATHS.STUDY_SUMMARY, { sessionId: practice.id })\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../src/styles/global.scss';\n\n.study-center-container {\n  min-height: 100vh;\n  background: $acdc-bg-primary;\n}\n\n.status-bar {\n  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);\n}\n\n.header {\n  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);\n  padding: 20rpx 30rpx 40rpx;\n  \n  .header-content {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    \n    .page-title {\n      font-size: 36rpx;\n      font-weight: bold;\n      color: #fff;\n    }\n    \n    .header-actions {\n      .study-stats {\n        .stats-text {\n          font-size: 24rpx;\n          color: rgba(255, 255, 255, 0.8);\n        }\n      }\n    }\n  }\n}\n\n.main-content {\n  padding: 30rpx;\n  padding-bottom: 120rpx; // 为底部导航留空间\n}\n\n.stats-card {\n  background: #fff;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);\n  display: flex;\n  align-items: center;\n  \n  .stats-item {\n    flex: 1;\n    text-align: center;\n    \n    .stats-number {\n      display: block;\n      font-size: 48rpx;\n      font-weight: bold;\n      color: #4A90E2;\n      margin-bottom: 8rpx;\n    }\n    \n    .stats-label {\n      font-size: 24rpx;\n      color: #666;\n    }\n  }\n  \n  .stats-divider {\n    width: 2rpx;\n    height: 60rpx;\n    background: #f0f0f0;\n  }\n}\n\n.study-modules {\n  margin-bottom: 40rpx;\n  \n  .module-card {\n    background: #fff;\n    border-radius: 24rpx;\n    padding: 40rpx;\n    margin-bottom: 24rpx;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n    display: flex;\n    align-items: center;\n    \n    &.disabled {\n      opacity: 0.6;\n      \n      .module-content {\n        .module-status {\n          color: #999;\n          font-size: 24rpx;\n        }\n      }\n    }\n    \n    &.login-prompt {\n      border: 2rpx dashed $acdc-primary;\n      background: rgba(46, 139, 87, 0.05);\n    }\n    \n    .module-icon {\n      position: relative;\n      margin-right: 40rpx;\n      \n      &.textbook {\n        padding: 24rpx;\n        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);\n        border-radius: 20rpx;\n      }\n      \n      &.question-bank {\n        padding: 24rpx;\n        background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);\n        border-radius: 20rpx;\n      }\n      \n      &.login {\n        padding: 24rpx;\n        background: linear-gradient(135deg, rgba(46, 139, 87, 0.1) 0%, rgba(46, 139, 87, 0.2) 100%);\n        border-radius: 20rpx;\n      }\n      \n      .coming-soon-badge,\n      .limit-badge {\n        position: absolute;\n        top: -10rpx;\n        right: -10rpx;\n        border-radius: 12rpx;\n        padding: 4rpx 12rpx;\n        \n        text {\n          font-size: 18rpx;\n          color: #fff;\n          font-weight: bold;\n        }\n      }\n      \n      .coming-soon-badge {\n        background: #FF9500;\n      }\n      \n      .limit-badge {\n        background: #f56c6c;\n      }\n    }\n    \n    .module-content {\n      flex: 1;\n      \n      .module-title {\n        display: block;\n        font-size: 32rpx;\n        font-weight: bold;\n        color: #333;\n        margin-bottom: 12rpx;\n      }\n      \n      .module-desc {\n        display: block;\n        font-size: 26rpx;\n        color: #666;\n        margin-bottom: 8rpx;\n      }\n      \n      .module-info {\n        display: block;\n        font-size: 24rpx;\n        color: #4A90E2;\n      }\n      \n      .module-status {\n        display: block;\n        font-size: 24rpx;\n        color: #999;\n      }\n    }\n  }\n}\n\n.vip-preview {\n  margin-bottom: 40rpx;\n  \n  .vip-card {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    border-radius: 24rpx;\n    padding: 40rpx;\n    color: #fff;\n    \n    .vip-header {\n      display: flex;\n      align-items: center;\n      margin-bottom: 40rpx;\n      \n      .vip-icon {\n        margin-right: 24rpx;\n      }\n      \n      .vip-content {\n        flex: 1;\n        \n        .vip-title {\n          display: block;\n          font-size: 32rpx;\n          font-weight: bold;\n          margin-bottom: 12rpx;\n        }\n        \n        .vip-desc {\n          font-size: 24rpx;\n          opacity: 0.8;\n        }\n      }\n    }\n    \n    .vip-features {\n      margin-bottom: 40rpx;\n      \n      .feature-item {\n        display: flex;\n        align-items: center;\n        margin-bottom: 16rpx;\n        \n        &:last-child {\n          margin-bottom: 0;\n        }\n        \n        text {\n          margin-left: 16rpx;\n          font-size: 26rpx;\n        }\n      }\n    }\n    \n    .vip-btn {\n      width: 100%;\n      height: 80rpx;\n      border-radius: 40rpx;\n      background: rgba(255, 255, 255, 0.2);\n      color: #fff;\n      border: 2rpx solid rgba(255, 255, 255, 0.3);\n    }\n  }\n}\n\n.recent-practices {\n  .section-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 24rpx;\n    \n    .section-title {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n    }\n    \n    .section-more {\n      font-size: 26rpx;\n      color: #4A90E2;\n    }\n  }\n  \n  .practice-list {\n    .practice-item {\n      background: #fff;\n      border-radius: 16rpx;\n      padding: 32rpx;\n      margin-bottom: 16rpx;\n      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n      \n      .practice-info {\n        flex: 1;\n        \n        .practice-category {\n          display: block;\n          font-size: 28rpx;\n          font-weight: bold;\n          color: #333;\n          margin-bottom: 8rpx;\n        }\n        \n        .practice-time {\n          font-size: 24rpx;\n          color: #999;\n        }\n      }\n      \n      .practice-result {\n        text-align: right;\n        \n        .score {\n          display: block;\n          font-size: 32rpx;\n          font-weight: bold;\n          margin-bottom: 4rpx;\n          \n          &.good {\n            color: #4CAF50;\n          }\n          \n          &.normal {\n            color: #FF9500;\n          }\n          \n          &.poor {\n            color: #f56c6c;\n          }\n        }\n        \n        .accuracy {\n          font-size: 24rpx;\n          color: #999;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/study/study.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useAppStore", "useStudyStore", "ref", "PRACTICE_CONFIG", "computed", "onMounted", "uni", "onShow", "api", "permission<PERSON>anager", "PAGE_PATHS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6KA,MAAA,mBAA6B,MAAA;AAC7B,MAAA,oBAA8B,MAAA;;;;AAG9B,UAAM,YAAYA,gBAAAA;AAClB,UAAM,WAAWC,eAAAA;AACjB,UAAM,aAAaC,iBAAAA;AAGb,UAAA,kBAAkBC,kBAAI,CAAC;AAG7B,UAAM,mBAAmBC,oBAAgB,gBAAA;AAGzC,UAAM,aAAaC,cAAA,SAAS,MAAM,WAAW,gBAAiB,CAAA;AAExD,UAAA,kBAAkBA,cAAAA,SAAS,MAAM;AACrC,aAAO,WAAW,gBAAgB,MAAM,GAAG,CAAC;AAAA,IAAA,CAC7C;AAEDC,kBAAAA,UAAU,MAAM;AAER,YAAA,aAAaC,oBAAI;AACP,sBAAA,QAAQ,WAAW,mBAAmB;AAG7C;IAAA,CACV;AAEDC,kBAAAA,OAAO,MAAM;AAEX,UAAI,UAAU,YAAY;AACV;MAChB;AAAA,IAAA,CACD;AAGD,UAAM,WAAW,MAAY;AAC3B,UAAI,UAAU,YAAY;AACxB,cAAM,cAAc;AACpB,cAAM,eAAe;AAAA,MACvB;AAAA,IAAA;AAIF,UAAM,gBAAgB,MAAY;AAAA,IAOhC;AAIF,UAAM,iBAAiB,MAAY;AAC7B,UAAA;AACF,cAAM,WAAW,MAAMC,cAAAA,IAAI,MAAM,cAAc;AACpC,mBAAA,cAAc,SAAS,IAAI;AAAA,eAC/B,OAAO;AACdF,sBAAA,MAAI,MAAM,SAAQ,gCAA+B,aAAa,KAAK;AAAA,MACrE;AAAA,IAAA;AAIF,UAAM,sBAAsB,MAAM;AAC5B,UAAA,CAAC,UAAU,YAAY;AAClB,eAAA;AAAA,MACT;AAEA,UAAI,UAAU,iBAAiB;AACtB,eAAA;AAAA,MACT;AAEI,UAAA,CAAC,WAAW,kBAAkB;AACzB,eAAA;AAAA,MACT;AAEO,aAAA,QAAQ,WAAW,sBAAsB;AAAA,IAAA;AAI5C,UAAA,kBAAkB,CAAC,eAAuB;AAC9C,YAAM,WAAW,WAAW,WAAW,KAAK,CAAK,MAAA,EAAE,OAAO,UAAU;AACpE,cAAO,qCAAU,SAAQ;AAAA,IAAA;AAIrB,UAAA,gBAAgB,CAAC,UAAkB;AACvC,UAAI,SAAS;AAAW,eAAA;AACxB,UAAI,SAAS;AAAW,eAAA;AACjB,aAAA;AAAA,IAAA;AAIT,UAAM,sBAAsB,MAAM;AAChC,eAAS,UAAU,gBAAgB;AAAA,IAAA;AAIrC,UAAM,0BAA0B,MAAM;AAEpC,UAAI,CAACG,qBAAA,kBAAkB,qBAAqB,UAAU,GAAG;AACvD;AAAA,MACF;AAEA,UAAI,CAAC,UAAU,mBAAmB,CAAC,WAAW,kBAAkB;AAC9D,iBAAS,UAAU;AAAA,UACjB,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,QAAA,CACb,EAAE,KAAK,CAAC,cAAc;AACrB,cAAI,WAAW;AACJ,qBAAA,UAAUC,+BAAW,QAAQ;AAAA,UACxC;AAAA,QAAA,CACD;AACD;AAAA,MACF;AAGS,eAAA,WAAWA,+BAAW,cAAc;AAAA,IAAA;AAI/C,UAAM,YAAY,MAAM;AACb,eAAA,WAAWA,+BAAW,KAAK;AAAA,IAAA;AAItC,UAAM,iBAAiB,MAAM;AAC3B,eAAS,UAAU;AAAA,QACjB,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,MAAA,CACb;AAAA,IAAA;AAIH,UAAM,mBAAmB,MAAM;AAC7B,eAAS,UAAU,WAAW;AAAA,IAAA;AAI1B,UAAA,qBAAqB,CAAC,aAAkB;AAC5C,eAAS,WAAWA,oBAAAA,WAAW,eAAe,EAAE,WAAW,SAAS,IAAI;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChU1E,GAAG,WAAW,eAAe;"}