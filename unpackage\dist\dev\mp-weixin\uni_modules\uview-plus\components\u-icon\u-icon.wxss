/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-ac70166d,
.u-empty__wrap.data-v-ac70166d,
.u-tabs.data-v-ac70166d,
.u-tabs__wrapper.data-v-ac70166d,
.u-tabs__wrapper__scroll-view-wrapper.data-v-ac70166d,
.u-tabs__wrapper__scroll-view.data-v-ac70166d,
.u-tabs__wrapper__nav.data-v-ac70166d,
.u-tabs__wrapper__nav__line.data-v-ac70166d,
.up-empty.data-v-ac70166d,
.up-empty__wrap.data-v-ac70166d,
.up-tabs.data-v-ac70166d,
.up-tabs__wrapper.data-v-ac70166d,
.up-tabs__wrapper__scroll-view-wrapper.data-v-ac70166d,
.up-tabs__wrapper__scroll-view.data-v-ac70166d,
.up-tabs__wrapper__nav.data-v-ac70166d,
.up-tabs__wrapper__nav__line.data-v-ac70166d {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-icon.data-v-ac70166d {
  display: flex;
  align-items: center;
}
.u-icon--left.data-v-ac70166d {
  flex-direction: row-reverse;
  align-items: center;
}
.u-icon--right.data-v-ac70166d {
  flex-direction: row;
  align-items: center;
}
.u-icon--top.data-v-ac70166d {
  flex-direction: column-reverse;
  justify-content: center;
}
.u-icon--bottom.data-v-ac70166d {
  flex-direction: column;
  justify-content: center;
}
.u-icon__icon.data-v-ac70166d {
  font-family: uicon-iconfont;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-icon__icon--primary.data-v-ac70166d {
  color: #3c9cff;
}
.u-icon__icon--success.data-v-ac70166d {
  color: #5ac725;
}
.u-icon__icon--error.data-v-ac70166d {
  color: #f56c6c;
}
.u-icon__icon--warning.data-v-ac70166d {
  color: #f9ae3d;
}
.u-icon__icon--info.data-v-ac70166d {
  color: #909399;
}
.u-icon__img.data-v-ac70166d {
  height: auto;
  will-change: transform;
}
.u-icon__label.data-v-ac70166d {
  line-height: 1;
}