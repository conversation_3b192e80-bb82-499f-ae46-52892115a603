{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-input/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\n\nexport const props = defineMixin({\n\tprops: {\n\t\t// #ifdef VUE3\n\t\t// 绑定的值\n\t\tmodelValue: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.input.value\n\t\t},\n\t\t// #endif\n\t\t// #ifdef VUE2\n\t\t// 绑定的值\n\t\tvalue: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.input.value\n\t\t},\n\t\t// #endif\n\t\t// number-数字输入键盘，app-vue下可以输入浮点数，app-nvue和小程序平台下只能输入整数\n\t\t// idcard-身份证输入键盘，微信、支付宝、百度、QQ小程序\n\t\t// digit-带小数点的数字键盘，App的nvue页面、微信、支付宝、百度、头条、QQ小程序\n\t\t// text-文本输入键盘\n\t\ttype: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.input.type\n\t\t},\n\t\t// 如果 textarea 是在一个 position:fixed 的区域，需要显示指定属性 fixed 为 true，\n\t\t// 兼容性：微信小程序、百度小程序、字节跳动小程序、QQ小程序\n\t\tfixed: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.input.fixed\n\t\t},\n\t\t// 是否禁用输入框\n\t\tdisabled: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.input.disabled\n\t\t},\n\t\t// 禁用状态时的背景色\n\t\tdisabledColor: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.input.disabledColor\n\t\t},\n\t\t// 是否显示清除控件\n\t\tclearable: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.input.clearable\n\t\t},\n\t\t// 是否密码类型\n\t\tpassword: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.input.password\n\t\t},\n\t\t// 最大输入长度，设置为 -1 的时候不限制最大长度\n\t\tmaxlength: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.input.maxlength\n\t\t},\n\t\t// \t输入框为空时的占位符\n\t\tplaceholder: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.input.placeholder\n\t\t},\n\t\t// 指定placeholder的样式类，注意页面或组件的style中写了scoped时，需要在类名前写/deep/\n\t\tplaceholderClass: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.input.placeholderClass\n\t\t},\n\t\t// 指定placeholder的样式\n\t\tplaceholderStyle: {\n\t\t\ttype: [String, Object],\n\t\t\tdefault: () => defProps.input.placeholderStyle\n\t\t},\n\t\t// 是否显示输入字数统计，只在 type =\"text\"或type =\"textarea\"时有效\n\t\tshowWordLimit: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.input.showWordLimit\n\t\t},\n\t\t// 设置右下角按钮的文字，有效值：send|search|next|go|done，兼容性详见uni-app文档\n\t\t// https://uniapp.dcloud.io/component/input\n\t\t// https://uniapp.dcloud.io/component/textarea\n\t\tconfirmType: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.input.confirmType\n\t\t},\n\t\t// 点击键盘右下角按钮时是否保持键盘不收起，H5无效\n\t\tconfirmHold: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.input.confirmHold\n\t\t},\n\t\t// focus时，点击页面的时候不收起键盘，微信小程序有效\n\t\tholdKeyboard: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.input.holdKeyboard\n\t\t},\n\t\t// 自动获取焦点\n\t\t// 在 H5 平台能否聚焦以及软键盘是否跟随弹出，取决于当前浏览器本身的实现。nvue 页面不支持，需使用组件的 focus()、blur() 方法控制焦点\n\t\tfocus: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.input.focus\n\t\t},\n\t\t// 键盘收起时，是否自动失去焦点，目前仅App3.0.0+有效\n\t\tautoBlur: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.input.autoBlur\n\t\t},\n\t\t// 是否去掉 iOS 下的默认内边距，仅微信小程序，且type=textarea时有效\n\t\tdisableDefaultPadding: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.input.disableDefaultPadding\n\t\t},\n\t\t// 指定focus时光标的位置\n\t\tcursor: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.input.cursor\n\t\t},\n\t\t// 输入框聚焦时底部与键盘的距离\n\t\tcursorSpacing: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.input.cursorSpacing\n\t\t},\n\t\t// 光标起始位置，自动聚集时有效，需与selection-end搭配使用\n\t\tselectionStart: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.input.selectionStart\n\t\t},\n\t\t// 光标结束位置，自动聚集时有效，需与selection-start搭配使用\n\t\tselectionEnd: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.input.selectionEnd\n\t\t},\n\t\t// 键盘弹起时，是否自动上推页面\n\t\tadjustPosition: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.input.adjustPosition\n\t\t},\n\t\t// 输入框内容对齐方式，可选值为：left|center|right\n\t\tinputAlign: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.input.inputAlign\n\t\t},\n\t\t// 输入框字体的大小\n\t\tfontSize: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.input.fontSize\n\t\t},\n\t\t// 输入框字体颜色\n\t\tcolor: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.input.color\n\t\t},\n\t\t// 输入框前置图标\n\t\tprefixIcon: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.input.prefixIcon\n\t\t},\n\t\t// 前置图标样式，对象或字符串\n\t\tprefixIconStyle: {\n\t\t\ttype: [String, Object],\n\t\t\tdefault: () => defProps.input.prefixIconStyle\n\t\t},\n\t\t// 输入框后置图标\n\t\tsuffixIcon: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.input.suffixIcon\n\t\t},\n\t\t// 后置图标样式，对象或字符串\n\t\tsuffixIconStyle: {\n\t\t\ttype: [String, Object],\n\t\t\tdefault: () => defProps.input.suffixIconStyle\n\t\t},\n\t\t// 边框类型，surround-四周边框，bottom-底部边框，none-无边框\n\t\tborder: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.input.border\n\t\t},\n\t\t// 是否只读，与disabled不同之处在于disabled会置灰组件，而readonly则不会\n\t\treadonly: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.input.readonly\n\t\t},\n\t\t// 输入框形状，circle-圆形，square-方形\n\t\tshape: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.input.shape\n\t\t},\n\t\t// 用于处理或者过滤输入框内容的方法\n\t\tformatter: {\n\t\t\ttype: [Function, null],\n\t\t\tdefault: () => defProps.input.formatter\n\t\t},\n\t\t// 是否忽略组件内对文本合成系统事件的处理\n\t\tignoreCompositionEvent: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t}\n\t}\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAGY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAChC,OAAO;AAAA;AAAA,IAGN,YAAY;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMC,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA;AAAA,IAaD,MAAM;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA;AAAA,IAGD,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA;AAAA;AAAA,IAID,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA;AAAA,IAGD,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,uBAAuB;AAAA,MACtB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,iBAAiB;AAAA,MAChB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,iBAAiB;AAAA,MAChB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM,CAAC,UAAU,IAAI;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,wBAAwB;AAAA,MACvB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,EACD;AACF,CAAC;;"}