"use strict";
const common_vendor = require("../../../common/vendor.js");
const src_constants_index = require("../../constants/index.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "StatusTag",
  props: {
    type: {},
    status: {},
    text: {}
  },
  setup(__props) {
    const props = __props;
    const statusText = common_vendor.computed(() => {
      if (props.text)
        return props.text;
      switch (props.type) {
        case "user":
          return src_constants_index.USER_STATUS_TEXT[props.status] || props.status;
        case "exam":
          return src_constants_index.EXAM_STATUS_TEXT[props.status] || props.status;
        case "exam-record":
          return src_constants_index.EXAM_RECORD_STATUS_TEXT[props.status] || props.status;
        case "certificate":
          return src_constants_index.CERTIFICATE_STATUS_TEXT[props.status] || props.status;
        default:
          return props.status;
      }
    });
    const statusClass = common_vendor.computed(() => {
      const baseClass = "status-tag";
      switch (props.type) {
        case "user":
          switch (props.status) {
            case "not_submitted":
              return `${baseClass} status-gray`;
            case "pending":
              return `${baseClass} status-warning`;
            case "approved":
              return `${baseClass} status-success`;
            case "rejected":
              return `${baseClass} status-error`;
            default:
              return `${baseClass} status-gray`;
          }
        case "exam":
          switch (props.status) {
            case "not_started":
              return `${baseClass} status-gray`;
            case "in_progress":
              return `${baseClass} status-primary`;
            case "completed":
              return `${baseClass} status-success`;
            case "expired":
              return `${baseClass} status-error`;
            default:
              return `${baseClass} status-gray`;
          }
        case "exam-record":
          switch (props.status) {
            case "not_started":
              return `${baseClass} status-gray`;
            case "in_progress":
              return `${baseClass} status-primary`;
            case "submitted":
              return `${baseClass} status-warning`;
            case "passed":
              return `${baseClass} status-success`;
            case "failed":
              return `${baseClass} status-error`;
            default:
              return `${baseClass} status-gray`;
          }
        case "certificate":
          switch (props.status) {
            case "pending":
              return `${baseClass} status-warning`;
            case "active":
              return `${baseClass} status-success`;
            case "expired":
              return `${baseClass} status-error`;
            case "revoked":
              return `${baseClass} status-error`;
            default:
              return `${baseClass} status-gray`;
          }
        default:
          return `${baseClass} status-gray`;
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(statusText.value),
        b: common_vendor.n(statusClass.value)
      };
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-4f0e1aea"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/src/components/common/StatusTag.js.map
