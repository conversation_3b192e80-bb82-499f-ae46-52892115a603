{"version": 3, "file": "swipterIndicator.js", "sources": ["uni_modules/uview-plus/components/u-swiper-indicator/swipterIndicator.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:22:07\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/swiperIndicator.js\n */\nexport default {\n    // swiperIndicator 组件\n    swiperIndicator: {\n        length: 0,\n        current: 0,\n        indicatorActiveColor: '',\n        indicatorInactiveColor: '',\n\t\tindicatorMode: 'line'\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,mBAAA;AAAA;AAAA,EAEX,iBAAiB;AAAA,IACb,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,sBAAsB;AAAA,IACtB,wBAAwB;AAAA,IAC9B,eAAe;AAAA,EACZ;AACL;;"}