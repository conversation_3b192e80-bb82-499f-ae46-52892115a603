"use strict";
const common_vendor = require("../../../common/vendor.js");
const src_stores_user = require("../../stores/user.js");
const src_stores_app = require("../../stores/app.js");
const src_constants_index = require("../../constants/index.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_icon2 + _easycom_u_button2)();
}
const _easycom_u_icon = () => "../../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "UserStatusBanner",
  props: {
    alwaysShow: { type: Boolean, default: false },
    showAction: { type: Boolean, default: true },
    customClass: { default: "" }
  },
  setup(__props) {
    const props = __props;
    const userStore = src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const shouldShowBanner = common_vendor.computed(() => {
      var _a;
      if (!userStore.isLoggedIn)
        return false;
      if (props.alwaysShow)
        return true;
      const status = (_a = userStore.userInfo) == null ? void 0 : _a.status;
      return status !== "approved";
    });
    const bannerClass = common_vendor.computed(() => {
      var _a;
      const status = (_a = userStore.userInfo) == null ? void 0 : _a.status;
      const baseClass = "banner";
      switch (status) {
        case "not_submitted":
          return `${baseClass} banner-warning ${props.customClass}`;
        case "pending":
          return `${baseClass} banner-info ${props.customClass}`;
        case "approved":
          return `${baseClass} banner-success ${props.customClass}`;
        case "rejected":
          return `${baseClass} banner-error ${props.customClass}`;
        default:
          return `${baseClass} banner-default ${props.customClass}`;
      }
    });
    const statusIcon = common_vendor.computed(() => {
      var _a;
      const status = (_a = userStore.userInfo) == null ? void 0 : _a.status;
      switch (status) {
        case "not_submitted":
          return "edit-pen";
        case "pending":
          return "clock";
        case "approved":
          return "checkmark-circle";
        case "rejected":
          return "close-circle";
        default:
          return "info-circle";
      }
    });
    const iconColor = common_vendor.computed(() => {
      var _a;
      const status = (_a = userStore.userInfo) == null ? void 0 : _a.status;
      switch (status) {
        case "not_submitted":
          return "#FAAD14";
        case "pending":
          return "#1890FF";
        case "approved":
          return "#52C41A";
        case "rejected":
          return "#F5222D";
        default:
          return "#8A8A8A";
      }
    });
    const statusTitle = common_vendor.computed(() => {
      var _a;
      const status = (_a = userStore.userInfo) == null ? void 0 : _a.status;
      switch (status) {
        case "not_submitted":
          return "资料未完善";
        case "pending":
          return "资料审核中";
        case "approved":
          return "认证成功";
        case "rejected":
          return "审核未通过";
        default:
          return "未知状态";
      }
    });
    const statusMessage = common_vendor.computed(() => {
      var _a;
      const status = (_a = userStore.userInfo) == null ? void 0 : _a.status;
      switch (status) {
        case "not_submitted":
          return "请完善个人资料以使用完整功能";
        case "pending":
          return "机构正在审核您的资料，请耐心等待";
        case "approved":
          return "您已通过机构认证，可使用所有功能";
        case "rejected":
          return "资料审核未通过，请重新提交正确信息";
        default:
          return "";
      }
    });
    const actionText = common_vendor.computed(() => {
      var _a;
      const status = (_a = userStore.userInfo) == null ? void 0 : _a.status;
      switch (status) {
        case "not_submitted":
          return "立即完善";
        case "pending":
          return "查看详情";
        case "approved":
          return "查看证书";
        case "rejected":
          return "重新提交";
        default:
          return "查看详情";
      }
    });
    const actionButtonType = common_vendor.computed(() => {
      var _a;
      const status = (_a = userStore.userInfo) == null ? void 0 : _a.status;
      switch (status) {
        case "not_submitted":
          return "warning";
        case "pending":
          return "info";
        case "approved":
          return "success";
        case "rejected":
          return "error";
        default:
          return "default";
      }
    });
    const handleAction = () => {
      var _a;
      const status = (_a = userStore.userInfo) == null ? void 0 : _a.status;
      switch (status) {
        case "not_submitted":
          appStore.redirectTo(src_constants_index.PAGE_PATHS.PROFILE);
          break;
        case "pending":
        case "approved":
          appStore.switchTab(src_constants_index.PAGE_PATHS.PERSONAL);
          break;
        case "rejected":
          appStore.redirectTo(src_constants_index.PAGE_PATHS.PROFILE);
          break;
        default:
          appStore.switchTab(src_constants_index.PAGE_PATHS.PERSONAL);
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: shouldShowBanner.value
      }, shouldShowBanner.value ? common_vendor.e({
        b: common_vendor.p({
          name: statusIcon.value,
          color: iconColor.value,
          size: "32"
        }),
        c: common_vendor.t(statusTitle.value),
        d: common_vendor.t(statusMessage.value),
        e: _ctx.showAction
      }, _ctx.showAction ? {
        f: common_vendor.t(actionText.value),
        g: common_vendor.o(handleAction),
        h: common_vendor.p({
          type: actionButtonType.value,
          size: "mini"
        })
      } : {}, {
        i: common_vendor.n(bannerClass.value)
      }) : {});
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-7c95bf63"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/src/components/common/UserStatusBanner.js.map
