/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-9112bed9,
.u-empty__wrap.data-v-9112bed9,
.u-tabs.data-v-9112bed9,
.u-tabs__wrapper.data-v-9112bed9,
.u-tabs__wrapper__scroll-view-wrapper.data-v-9112bed9,
.u-tabs__wrapper__scroll-view.data-v-9112bed9,
.u-tabs__wrapper__nav.data-v-9112bed9,
.u-tabs__wrapper__nav__line.data-v-9112bed9,
.up-empty.data-v-9112bed9,
.up-empty__wrap.data-v-9112bed9,
.up-tabs.data-v-9112bed9,
.up-tabs__wrapper.data-v-9112bed9,
.up-tabs__wrapper__scroll-view-wrapper.data-v-9112bed9,
.up-tabs__wrapper__scroll-view.data-v-9112bed9,
.up-tabs__wrapper__nav.data-v-9112bed9,
.up-tabs__wrapper__nav__line.data-v-9112bed9 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-overlay.data-v-9112bed9 {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
}