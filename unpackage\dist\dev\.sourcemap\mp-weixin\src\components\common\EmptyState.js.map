{"version": 3, "file": "EmptyState.js", "sources": ["src/components/common/EmptyState.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovcHJvamVjdC9BQ0RDZXhhbS9zcmMvY29tcG9uZW50cy9jb21tb24vRW1wdHlTdGF0ZS52dWU"], "sourcesContent": ["<template>\n  <view class=\"empty-state\">\n    <image \n      :src=\"imageUrl\" \n      class=\"empty-image\" \n      mode=\"aspectFit\"\n    />\n    <text class=\"empty-title\">{{ title }}</text>\n    <text v-if=\"description\" class=\"empty-description\">{{ description }}</text>\n    <u-button \n      v-if=\"showButton\" \n      :text=\"buttonText\" \n      type=\"primary\"\n      size=\"normal\"\n      @click=\"handleButtonClick\"\n      class=\"empty-button\"\n    />\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed } from 'vue'\nimport { EMPTY_IMAGES } from '../../constants'\n\ninterface Props {\n  type?: 'no-data' | 'no-network' | 'no-permission' | 'custom'\n  title?: string\n  description?: string\n  image?: string\n  showButton?: boolean\n  buttonText?: string\n}\n\nconst props = withDefaults(defineProps<Props>(), {\n  type: 'no-data',\n  title: '',\n  description: '',\n  image: '',\n  showButton: false,\n  buttonText: '重试',\n})\n\nconst emit = defineEmits<{\n  buttonClick: []\n}>()\n\nconst imageUrl = computed(() => {\n  if (props.image) return props.image\n  \n  switch (props.type) {\n    case 'no-data':\n      return EMPTY_IMAGES.NO_DATA\n    case 'no-network':\n      return EMPTY_IMAGES.NO_NETWORK\n    case 'no-permission':\n      return EMPTY_IMAGES.NO_PERMISSION\n    default:\n      return EMPTY_IMAGES.NO_DATA\n  }\n})\n\nconst title = computed(() => {\n  if (props.title) return props.title\n  \n  switch (props.type) {\n    case 'no-data':\n      return '暂无数据'\n    case 'no-network':\n      return '网络连接失败'\n    case 'no-permission':\n      return '暂无权限'\n    default:\n      return '暂无数据'\n  }\n})\n\nconst handleButtonClick = () => {\n  emit('buttonClick')\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.empty-state {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  padding: 120rpx 60rpx;\n  text-align: center;\n}\n\n.empty-image {\n  width: 200rpx;\n  height: 200rpx;\n  margin-bottom: 32rpx;\n  opacity: 0.6;\n}\n\n.empty-title {\n  font-size: 32rpx;\n  color: #8A8A8A;\n  margin-bottom: 16rpx;\n  font-weight: 500;\n}\n\n.empty-description {\n  font-size: 28rpx;\n  color: #BFBFBF;\n  line-height: 1.5;\n  margin-bottom: 48rpx;\n  max-width: 400rpx;\n}\n\n.empty-button {\n  margin-top: 32rpx;\n}\n</style>\n", "import Component from 'E:/project/ACDCexam/src/components/common/EmptyState.vue'\nwx.createComponent(Component)"], "names": ["computed", "EMPTY_IMAGES"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAiCA,UAAM,QAAQ;AASd,UAAM,OAAO;AAIP,UAAA,WAAWA,cAAAA,SAAS,MAAM;AAC9B,UAAI,MAAM;AAAO,eAAO,MAAM;AAE9B,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK;AACH,iBAAOC,oBAAAA,aAAa;AAAA,QACtB,KAAK;AACH,iBAAOA,oBAAAA,aAAa;AAAA,QACtB,KAAK;AACH,iBAAOA,oBAAAA,aAAa;AAAA,QACtB;AACE,iBAAOA,oBAAAA,aAAa;AAAA,MACxB;AAAA,IAAA,CACD;AAEK,UAAA,QAAQD,cAAAA,SAAS,MAAM;AAC3B,UAAI,MAAM;AAAO,eAAO,MAAM;AAE9B,cAAQ,MAAM,MAAM;AAAA,QAClB,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT;AACS,iBAAA;AAAA,MACX;AAAA,IAAA,CACD;AAED,UAAM,oBAAoB,MAAM;AAC9B,WAAK,aAAa;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;AC5EpB,GAAG,gBAAgB,SAAS;"}