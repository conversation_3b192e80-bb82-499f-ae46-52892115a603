{"version": 3, "file": "transitionMixin.js", "sources": ["uni_modules/uview-plus/components/u-transition/transitionMixin.js"], "sourcesContent": ["// 定义一个一定时间后自动成功的promise，让调用nextTick方法处，进入下一个then方法\r\nconst waitTick = () => new Promise(resolve => setTimeout(resolve, 1000 / 50))\r\n// nvue动画模块实现细节抽离在外部文件\r\n// #ifdef APP-NVUE\r\nimport animationMap from './nvue-ani-map.js'\r\n// #endif\r\n\r\n// #ifndef APP-NVUE\r\n// 定义类名，通过给元素动态切换类名，赋予元素一定的css动画样式\r\nconst getClassNames = (name) => ({\r\n    enter: `u-${name}-enter u-${name}-enter-active`,\r\n    'enter-to': `u-${name}-enter-to u-${name}-enter-active`,\r\n    leave: `u-${name}-leave u-${name}-leave-active`,\r\n    'leave-to': `u-${name}-leave-to u-${name}-leave-active`\r\n})\r\n// #endif\r\n\r\n// #ifdef APP-NVUE\r\n// 引入nvue(weex)的animation动画模块，文档见：\r\n// https://weex.apache.org/zh/docs/modules/animation.html#transition\r\nconst animation = uni.requireNativePlugin('animation')\r\nconst getStyle = (name) => animationMap[name]\r\n// #endif\r\n\r\nimport { nextTick } from 'vue'\r\nimport { sleep } from '../../libs/function/index';\r\nexport default {\r\n    methods: {\r\n        // 组件被点击发出事件\r\n        clickHandler() {\r\n            this.$emit('click')\r\n        },\r\n        // #ifndef APP-NVUE\r\n        // vue版本的组件进场处理\r\n        async vueEnter() {\r\n            // 动画进入时的类名\r\n            const classNames = getClassNames(this.mode)\r\n            // 定义状态和发出动画进入前事件\r\n            this.status = 'enter'\r\n            this.$emit('beforeEnter')\r\n            this.inited = true\r\n            this.display = true\r\n            this.classes = classNames.enter\r\n\t\t\tawait nextTick();\r\n\t\t\t{\r\n                // https://github.com/umicro/uView2.0/issues/545\r\n\t\t\t\tawait sleep(20)\r\n                // 标识动画尚未结束\r\n                this.$emit('enter')\r\n                this.transitionEnded = false\r\n\t\t\t\t// 组件动画进入后触发的事件\r\n                this.$emit('afterEnter')\r\n                // 赋予组件enter-to类名\r\n                this.classes = classNames['enter-to']\r\n            }\r\n        },\r\n        // 动画离场处理\r\n        async vueLeave() {\r\n            // 如果不是展示状态，无需执行逻辑\r\n            if (!this.display) return\r\n            const classNames = getClassNames(this.mode)\r\n            // 标记离开状态和发出事件\r\n            this.status = 'leave'\r\n            this.$emit('beforeLeave')\r\n            // 获得类名\r\n            this.classes = classNames.leave\r\n\r\n            await nextTick();\r\n\t\t\t{\r\n               // 动画正在离场的状态\r\n               this.transitionEnded = false\r\n               this.$emit('leave')\r\n                // 组件执行动画，到了执行的执行时间后，执行一些额外处理\r\n                setTimeout(this.onTransitionEnd, this.duration)\r\n                this.classes = classNames['leave-to']\r\n            }\r\n        },\r\n        // #endif\r\n        // #ifdef APP-NVUE\r\n        // nvue版本动画进场\r\n        async nvueEnter() {\r\n            // 获得样式的名称\r\n            const currentStyle = getStyle(this.mode)\r\n            // 组件动画状态和发出事件\r\n            this.status = 'enter'\r\n            this.$emit('beforeEnter')\r\n            // 展示生成组件元素\r\n            this.inited = true\r\n            this.display = true\r\n            // 在nvue安卓上，由于渲染速度慢，在弹窗，键盘，日历等组件中，渲染其中的内容需要时间\r\n            // 导致出现弹窗卡顿，这里让其一开始为透明状态，等一定时间渲染完成后，再让其隐藏起来，再让其按正常逻辑出现\r\n            this.viewStyle = {\r\n                opacity: 0\r\n            }\r\n            // 等待弹窗内容渲染完成\r\n            await nextTick();\r\n\t\t\t{\r\n                // 合并样式\r\n                this.viewStyle = currentStyle.enter\r\n                Promise.resolve()\r\n                    .then(waitTick)\r\n                    .then(() => {\r\n                        // 组件开始进入前的事件\r\n                        this.$emit('enter')\r\n                        // nvue的transition动画模块需要通过ref调用组件，注意此处的ref不同于vue的this.$refs['u-transition']用法\r\n                        animation.transition(this.$refs['u-transition'].ref, {\r\n                            styles: currentStyle['enter-to'],\r\n                            duration: this.duration,\r\n                            timingFunction: this.timingFunction,\r\n                            needLayout: false,\r\n                            delay: 0\r\n                        }, () => {\r\n                            // 动画执行完毕，发出事件\r\n                            this.$emit('afterEnter')\r\n                        })\r\n                    })\r\n                    .catch(() => {})\r\n            }\r\n        },\r\n        nvueLeave() {\r\n            if (!this.display) {\r\n                return\r\n            }\r\n            const currentStyle = getStyle(this.mode)\r\n            // 定义状态和事件\r\n            this.status = 'leave'\r\n            this.$emit('beforeLeave')\r\n            // 合并样式\r\n            this.viewStyle = currentStyle.leave\r\n            // 放到promise中处理执行过程\r\n            Promise.resolve()\r\n                .then(waitTick) // 等待几十ms\r\n                .then(() => {\r\n                    this.transitionEnded = false\r\n                    // 动画正在离场的状态\r\n                    this.$emit('leave')\r\n                    animation.transition(this.$refs['u-transition'].ref, {\r\n                        styles: currentStyle['leave-to'],\r\n                        duration: this.duration,\r\n                        timingFunction: this.timingFunction,\r\n                        needLayout: false,\r\n                        delay: 0\r\n                    }, () => {\r\n                        this.onTransitionEnd()\r\n                    })\r\n                })\r\n                .catch(() => {})\r\n        },\r\n        // #endif\r\n        // 完成过渡后触发\r\n        onTransitionEnd() {\r\n            // 如果已经是结束的状态，无需再处理\r\n            if (this.transitionEnded) return\r\n            this.transitionEnded = true\r\n            // 发出组件动画执行后的事件\r\n            this.$emit(this.status === 'leave' ? 'afterLeave' : 'afterEnter')\r\n            if (!this.show && this.display) {\r\n                this.display = false\r\n                this.inited = false\r\n            }\r\n        }\r\n    }\r\n}\r\n"], "names": ["nextTick", "sleep"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AASA,MAAM,gBAAgB,CAAC,UAAU;AAAA,EAC7B,OAAO,KAAK,IAAI,YAAY,IAAI;AAAA,EAChC,YAAY,KAAK,IAAI,eAAe,IAAI;AAAA,EACxC,OAAO,KAAK,IAAI,YAAY,IAAI;AAAA,EAChC,YAAY,KAAK,IAAI,eAAe,IAAI;AAC5C;AAYA,MAAe,kBAAA;AAAA,EACX,SAAS;AAAA;AAAA,IAEL,eAAe;AACX,WAAK,MAAM,OAAO;AAAA,IACrB;AAAA;AAAA,IAGK,WAAW;AAAA;AAEb,cAAM,aAAa,cAAc,KAAK,IAAI;AAE1C,aAAK,SAAS;AACd,aAAK,MAAM,aAAa;AACxB,aAAK,SAAS;AACd,aAAK,UAAU;AACf,aAAK,UAAU,WAAW;AACnC,cAAMA,cAAQ,WAAA;AACd;AAEC,gBAAMC,0CAAAA,MAAM,EAAE;AAEF,eAAK,MAAM,OAAO;AAClB,eAAK,kBAAkB;AAEvB,eAAK,MAAM,YAAY;AAEvB,eAAK,UAAU,WAAW,UAAU;AAAA,QACvC;AAAA,MACJ;AAAA;AAAA;AAAA,IAEK,WAAW;AAAA;AAEb,YAAI,CAAC,KAAK;AAAS;AACnB,cAAM,aAAa,cAAc,KAAK,IAAI;AAE1C,aAAK,SAAS;AACd,aAAK,MAAM,aAAa;AAExB,aAAK,UAAU,WAAW;AAE1B,cAAMD,cAAQ,WAAA;AACvB;AAEY,eAAK,kBAAkB;AACvB,eAAK,MAAM,OAAO;AAEjB,qBAAW,KAAK,iBAAiB,KAAK,QAAQ;AAC9C,eAAK,UAAU,WAAW,UAAU;AAAA,QACvC;AAAA,MACJ;AAAA;AAAA;AAAA,IA0ED,kBAAkB;AAEd,UAAI,KAAK;AAAiB;AAC1B,WAAK,kBAAkB;AAEvB,WAAK,MAAM,KAAK,WAAW,UAAU,eAAe,YAAY;AAChE,UAAI,CAAC,KAAK,QAAQ,KAAK,SAAS;AAC5B,aAAK,UAAU;AACf,aAAK,SAAS;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACL;;"}