{"version": 3, "file": "buildFullPath.js", "sources": ["uni_modules/uview-plus/libs/luch-request/core/buildFullPath.js"], "sourcesContent": ["'use strict'\r\n\r\nimport isAbsoluteURL from '../helpers/isAbsoluteURL'\r\nimport combineURLs from '../helpers/combineURLs'\r\n\r\n/**\r\n * Creates a new URL by combining the baseURL with the requestedURL,\r\n * only when the requestedURL is not already an absolute URL.\r\n * If the requestURL is absolute, this function returns the requestedURL untouched.\r\n *\r\n * @param {string} baseURL The base URL\r\n * @param {string} requestedURL Absolute or relative URL to combine\r\n * @returns {string} The combined full path\r\n */\r\nexport default function buildFullPath(baseURL, requestedURL) {\r\n    if (baseURL && !isAbsoluteURL(requestedURL)) {\r\n        return combineURLs(baseURL, requestedURL)\r\n    }\r\n    return requestedURL\r\n}\r\n"], "names": ["isAbsoluteURL", "combineURLs"], "mappings": ";;;AAce,SAAS,cAAc,SAAS,cAAc;AACzD,MAAI,WAAW,CAACA,2EAAc,YAAY,GAAG;AACzC,WAAOC,2DAAW,YAAC,SAAS,YAAY;AAAA,EAC3C;AACD,SAAO;AACX;;"}