{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-textarea/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\n\nexport const props = defineMixin({\n\tprops: {\n\t\t// 输入框的内容\n\t\tvalue: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.textarea.value\n\t\t},\n\t\t// 输入框的内容\n\t\tmodelValue: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.textarea.value\n\t\t},\n\t\t// 输入框为空时占位符\n\t\tplaceholder: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.textarea.placeholder\n\t\t},\n\t\t// 指定placeholder的样式类，注意页面或组件的style中写了scoped时，需要在类名前写/deep/\n\t\tplaceholderClass: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.input.placeholderClass\n\t\t},\n\t\t// 指定placeholder的样式\n\t\tplaceholderStyle: {\n\t\t\ttype: [String, Object],\n\t\t\tdefault: () => defProps.input.placeholderStyle\n\t\t},\n\t\t// 输入框高度\n\t\theight: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.textarea.height\n\t\t},\n\t\t// 设置键盘右下角按钮的文字，仅微信小程序，App-vue和H5有效\n\t\tconfirmType: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.textarea.confirmType\n\t\t},\n\t\t// 是否禁用\n\t\tdisabled: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.textarea.disabled\n\t\t},\n\t\t// 是否显示统计字数\n\t\tcount: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.textarea.count\n\t\t},\n\t\t// 是否自动获取焦点，nvue不支持，H5取决于浏览器的实现\n\t\tfocus: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.textarea.focus\n\t\t},\n\t\t// 是否自动增加高度\n\t\tautoHeight: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.textarea.autoHeight\n\t\t},\n\t\t// 如果textarea是在一个position:fixed的区域，需要显示指定属性fixed为true\n\t\tfixed: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.textarea.fixed\n\t\t},\n\t\t// 指定光标与键盘的距离\n\t\tcursorSpacing: {\n\t\t\ttype: Number,\n\t\t\tdefault: () => defProps.textarea.cursorSpacing\n\t\t},\n\t\t// 指定focus时的光标位置\n\t\tcursor: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.textarea.cursor\n\t\t},\n\t\t// 是否显示键盘上方带有”完成“按钮那一栏，\n\t\tshowConfirmBar: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.textarea.showConfirmBar\n\t\t},\n\t\t// 光标起始位置，自动聚焦时有效，需与selection-end搭配使用\n\t\tselectionStart: {\n\t\t\ttype: Number,\n\t\t\tdefault: () => defProps.textarea.selectionStart\n\t\t},\n\t\t// 光标结束位置，自动聚焦时有效，需与selection-start搭配使用\n\t\tselectionEnd: {\n\t\t\ttype: Number,\n\t\t\tdefault: () => defProps.textarea.selectionEnd\n\t\t},\n\t\t// 键盘弹起时，是否自动上推页面\n\t\tadjustPosition: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.textarea.adjustPosition\n\t\t},\n\t\t// 是否去掉 iOS 下的默认内边距，只微信小程序有效\n\t\tdisableDefaultPadding: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.textarea.disableDefaultPadding\n\t\t},\n\t\t// focus时，点击页面的时候不收起键盘，只微信小程序有效\n\t\tholdKeyboard: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: () => defProps.textarea.holdKeyboard\n\t\t},\n\t\t// 最大输入长度，设置为 -1 的时候不限制最大长度\n\t\tmaxlength: {\n\t\t\ttype: [String, Number],\n\t\t\tdefault: () => defProps.textarea.maxlength\n\t\t},\n\t\t// 边框类型，surround-四周边框，bottom-底部边框\n\t\tborder: {\n\t\t\ttype: String,\n\t\t\tdefault: () => defProps.textarea.border\n\t\t},\n\t\t// 用于处理或者过滤输入框内容的方法\n\t\tformatter: {\n\t\t\ttype: [Function, null],\n\t\t\tdefault: () => defProps.textarea.formatter\n\t\t},\n\t\t// 是否忽略组件内对文本合成系统事件的处理\n\t\tignoreCompositionEvent: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t}\n\t}\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAGY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAChC,OAAO;AAAA;AAAA,IAEN,OAAO;AAAA,MACN,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMC,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,kBAAkB;AAAA,MACjB,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,MAAM;AAAA,IAC9B;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,aAAa;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,UAAU;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,YAAY;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,OAAO;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,eAAe;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACf,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,uBAAuB;AAAA,MACtB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,cAAc;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,WAAW;AAAA,MACV,MAAM,CAAC,UAAU,IAAI;AAAA,MACrB,SAAS,MAAMA,8CAAS,SAAS;AAAA,IACjC;AAAA;AAAA,IAED,wBAAwB;AAAA,MACvB,MAAM;AAAA,MACN,SAAS;AAAA,IACT;AAAA,EACD;AACF,CAAC;;"}