<!DOCTYPE html>
<html>
<head>
    <title>创建底部导航图标</title>
    <style>
        .icon-container {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            padding: 20px;
        }
        .icon-item {
            text-align: center;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 5px;
        }
        button {
            margin: 5px;
            padding: 5px 10px;
        }
    </style>
</head>
<body>
    <h1>疾控医护考试系统 - 底部导航图标生成器</h1>
    <div class="icon-container">
        <div class="icon-item">
            <h3>信息中心</h3>
            <canvas id="info" width="81" height="81"></canvas>
            <canvas id="info-active" width="81" height="81"></canvas>
            <br>
            <button onclick="downloadIcon('info')">下载普通</button>
            <button onclick="downloadIcon('info-active')">下载激活</button>
        </div>
        
        <div class="icon-item">
            <h3>学习中心</h3>
            <canvas id="study" width="81" height="81"></canvas>
            <canvas id="study-active" width="81" height="81"></canvas>
            <br>
            <button onclick="downloadIcon('study')">下载普通</button>
            <button onclick="downloadIcon('study-active')">下载激活</button>
        </div>
        
        <div class="icon-item">
            <h3>考试中心</h3>
            <canvas id="exam" width="81" height="81"></canvas>
            <canvas id="exam-active" width="81" height="81"></canvas>
            <br>
            <button onclick="downloadIcon('exam')">下载普通</button>
            <button onclick="downloadIcon('exam-active')">下载激活</button>
        </div>
        
        <div class="icon-item">
            <h3>个人中心</h3>
            <canvas id="personal" width="81" height="81"></canvas>
            <canvas id="personal-active" width="81" height="81"></canvas>
            <br>
            <button onclick="downloadIcon('personal')">下载普通</button>
            <button onclick="downloadIcon('personal-active')">下载激活</button>
        </div>
    </div>

    <script>
        // 绘制图标的函数
        function drawIcon(canvasId, iconType, isActive) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            const size = 81;
            const center = size / 2;
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 设置颜色
            const color = isActive ? '#2E8B57' : '#8A8A8A';
            ctx.fillStyle = color;
            ctx.strokeStyle = color;
            ctx.lineWidth = 3;
            
            switch(iconType) {
                case 'info':
                    // 信息图标 - 圆形中间有i
                    ctx.beginPath();
                    ctx.arc(center, center, 25, 0, 2 * Math.PI);
                    ctx.stroke();
                    
                    // 绘制 i 的点
                    ctx.beginPath();
                    ctx.arc(center, center - 10, 3, 0, 2 * Math.PI);
                    ctx.fill();
                    
                    // 绘制 i 的竖线
                    ctx.fillRect(center - 2, center - 5, 4, 15);
                    break;
                    
                case 'study':
                    // 学习图标 - 书本
                    ctx.fillRect(center - 15, center - 12, 30, 24);
                    ctx.strokeRect(center - 15, center - 12, 30, 24);
                    
                    // 书页线条
                    ctx.beginPath();
                    ctx.moveTo(center - 10, center - 8);
                    ctx.lineTo(center + 10, center - 8);
                    ctx.moveTo(center - 10, center - 3);
                    ctx.lineTo(center + 10, center - 3);
                    ctx.moveTo(center - 10, center + 2);
                    ctx.lineTo(center + 10, center + 2);
                    ctx.stroke();
                    break;
                    
                case 'exam':
                    // 考试图标 - 文档和笔
                    ctx.fillRect(center - 12, center - 15, 24, 30);
                    ctx.strokeRect(center - 12, center - 15, 24, 30);
                    
                    // 复选框
                    ctx.strokeRect(center - 8, center - 10, 6, 6);
                    ctx.strokeRect(center - 8, center - 2, 6, 6);
                    ctx.strokeRect(center - 8, center + 6, 6, 6);
                    
                    // 对勾
                    ctx.beginPath();
                    ctx.moveTo(center - 6, center - 7);
                    ctx.lineTo(center - 4, center - 5);
                    ctx.lineTo(center - 2, center - 9);
                    ctx.stroke();
                    break;
                    
                case 'personal':
                    // 个人图标 - 人形
                    // 头部
                    ctx.beginPath();
                    ctx.arc(center, center - 8, 8, 0, 2 * Math.PI);
                    ctx.stroke();
                    
                    // 身体
                    ctx.beginPath();
                    ctx.arc(center, center + 8, 15, 0, Math.PI, true);
                    ctx.stroke();
                    break;
            }
        }
        
        // 下载图标
        function downloadIcon(canvasId) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = canvasId + '.png';
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 初始化所有图标
        window.onload = function() {
            drawIcon('info', 'info', false);
            drawIcon('info-active', 'info', true);
            drawIcon('study', 'study', false);
            drawIcon('study-active', 'study', true);
            drawIcon('exam', 'exam', false);
            drawIcon('exam-active', 'exam', true);
            drawIcon('personal', 'personal', false);
            drawIcon('personal-active', 'personal', true);
        };
    </script>
</body>
</html>
