{"version": 3, "file": "app.js", "sources": ["src/stores/app.ts"], "sourcesContent": ["import { defineStore } from 'pinia'\nimport { ref } from 'vue'\nimport { STORAGE_KEYS } from '../constants'\n\nexport interface AppSettings {\n  theme: 'light' | 'dark'\n  language: 'zh-CN' | 'en-US'\n  enableNotifications: boolean\n  enableSounds: boolean\n  practiceReminder: boolean\n  examReminder: boolean\n}\n\nexport const useAppStore = defineStore('app', () => {\n  // 状态\n  const loading = ref<boolean>(false)\n  const networkStatus = ref<boolean>(true)\n  const systemInfo = ref<any>(null)\n  const settings = ref<AppSettings>({\n    theme: 'light',\n    language: 'zh-CN',\n    enableNotifications: true,\n    enableSounds: true,\n    practiceReminder: true,\n    examReminder: true,\n  })\n\n  // 全局加载状态\n  const setLoading = (status: boolean) => {\n    loading.value = status\n  }\n\n  // 网络状态\n  const setNetworkStatus = (status: boolean) => {\n    networkStatus.value = status\n  }\n\n  // 系统信息\n  const setSystemInfo = (info: any) => {\n    systemInfo.value = info\n  }\n\n  // 获取系统信息 - 使用新的API\n  const getSystemInfo = async () => {\n    try {\n      let systemInfo: any = {}\n\n      // 优先使用新的分离API\n      if (uni.canIUse('getWindowInfo')) {\n        const windowInfo = uni.getWindowInfo()\n        const deviceInfo = uni.getDeviceInfo()\n        const appBaseInfo = uni.getAppBaseInfo()\n\n        // 合并信息\n        systemInfo = {\n          ...windowInfo,\n          ...deviceInfo,\n          ...appBaseInfo\n        }\n      } else {\n        // 兼容旧版本\n        const info = await uni.getSystemInfo()\n        systemInfo = info[1]\n      }\n\n      setSystemInfo(systemInfo)\n      return systemInfo\n    } catch (error) {\n      console.error('Failed to get system info:', error)\n      return null\n    }\n  }\n\n  // 设置配置\n  const updateSettings = (newSettings: Partial<AppSettings>) => {\n    settings.value = { ...settings.value, ...newSettings }\n    saveSettings()\n  }\n\n  // 保存设置到本地存储\n  const saveSettings = () => {\n    try {\n      uni.setStorageSync(STORAGE_KEYS.SETTINGS, JSON.stringify(settings.value))\n    } catch (error) {\n      console.error('Failed to save settings:', error)\n    }\n  }\n\n  // 从本地存储加载设置\n  const loadSettings = () => {\n    try {\n      const storedSettings = uni.getStorageSync(STORAGE_KEYS.SETTINGS)\n      if (storedSettings) {\n        settings.value = { ...settings.value, ...JSON.parse(storedSettings) }\n      }\n    } catch (error) {\n      console.error('Failed to load settings:', error)\n    }\n  }\n\n  // 显示Toast\n  const showToast = (title: string, icon: 'success' | 'error' | 'loading' | 'none' = 'none') => {\n    uni.showToast({\n      title,\n      icon,\n      duration: 2000,\n    })\n  }\n\n  // 显示加载提示\n  const showLoading = (title: string = '加载中...') => {\n    setLoading(true)\n    uni.showLoading({ title })\n  }\n\n  // 隐藏加载提示\n  const hideLoading = () => {\n    setLoading(false)\n    uni.hideLoading()\n  }\n\n  // 显示模态框\n  const showModal = (options: {\n    title?: string\n    content: string\n    showCancel?: boolean\n    confirmText?: string\n    cancelText?: string\n  }) => {\n    return new Promise<boolean>((resolve) => {\n      uni.showModal({\n        title: options.title || '提示',\n        content: options.content,\n        showCancel: options.showCancel !== false,\n        confirmText: options.confirmText || '确定',\n        cancelText: options.cancelText || '取消',\n        success: (res) => {\n          resolve(res.confirm)\n        },\n        fail: () => {\n          resolve(false)\n        },\n      })\n    })\n  }\n\n  // 页面跳转\n  const navigateTo = (url: string, params?: Record<string, any>) => {\n    let fullUrl = url\n    if (params) {\n      const query = Object.keys(params)\n        .map(key => `${key}=${encodeURIComponent(params[key])}`)\n        .join('&')\n      fullUrl += `?${query}`\n    }\n    \n    uni.navigateTo({ url: fullUrl })\n  }\n\n  // 页面重定向\n  const redirectTo = (url: string, params?: Record<string, any>) => {\n    let fullUrl = url\n    if (params) {\n      const query = Object.keys(params)\n        .map(key => `${key}=${encodeURIComponent(params[key])}`)\n        .join('&')\n      fullUrl += `?${query}`\n    }\n    \n    uni.redirectTo({ url: fullUrl })\n  }\n\n  // 切换Tab页面\n  const switchTab = (url: string) => {\n    uni.switchTab({ url })\n  }\n\n  // 返回上一页\n  const navigateBack = (delta: number = 1) => {\n    uni.navigateBack({ delta })\n  }\n\n  // 检查网络状态\n  const checkNetworkStatus = () => {\n    uni.getNetworkType({\n      success: (res) => {\n        setNetworkStatus(res.networkType !== 'none')\n      },\n      fail: () => {\n        setNetworkStatus(false)\n      },\n    })\n  }\n\n  // 监听网络状态变化\n  const watchNetworkStatus = () => {\n    uni.onNetworkStatusChange((res) => {\n      setNetworkStatus(res.isConnected)\n    })\n  }\n\n  // 初始化应用\n  const initApp = async () => {\n    // 加载设置\n    loadSettings()\n    \n    // 获取系统信息\n    await getSystemInfo()\n    \n    // 检查网络状态\n    checkNetworkStatus()\n    \n    // 监听网络状态变化\n    watchNetworkStatus()\n  }\n\n  return {\n    // 状态\n    loading,\n    networkStatus,\n    systemInfo,\n    settings,\n    \n    // 方法\n    setLoading,\n    setNetworkStatus,\n    setSystemInfo,\n    getSystemInfo,\n    updateSettings,\n    saveSettings,\n    loadSettings,\n    showToast,\n    showLoading,\n    hideLoading,\n    showModal,\n    navigateTo,\n    redirectTo,\n    switchTab,\n    navigateBack,\n    checkNetworkStatus,\n    watchNetworkStatus,\n    initApp,\n  }\n})\n"], "names": ["defineStore", "ref", "systemInfo", "uni", "STORAGE_KEYS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaa,MAAA,cAAcA,cAAAA,YAAY,OAAO,MAAM;AAE5C,QAAA,UAAUC,kBAAa,KAAK;AAC5B,QAAA,gBAAgBA,kBAAa,IAAI;AACjC,QAAA,aAAaA,kBAAS,IAAI;AAChC,QAAM,WAAWA,cAAAA,IAAiB;AAAA,IAChC,OAAO;AAAA,IACP,UAAU;AAAA,IACV,qBAAqB;AAAA,IACrB,cAAc;AAAA,IACd,kBAAkB;AAAA,IAClB,cAAc;AAAA,EAAA,CACf;AAGK,QAAA,aAAa,CAAC,WAAoB;AACtC,YAAQ,QAAQ;AAAA,EAAA;AAIZ,QAAA,mBAAmB,CAAC,WAAoB;AAC5C,kBAAc,QAAQ;AAAA,EAAA;AAIlB,QAAA,gBAAgB,CAAC,SAAc;AACnC,eAAW,QAAQ;AAAA,EAAA;AAIrB,QAAM,gBAAgB,MAAY;AAC5B,QAAA;AACF,UAAIC,cAAkB,CAAA;AAGlB,UAAAC,cAAA,MAAI,QAAQ,eAAe,GAAG;AAC1B,cAAA,aAAaA,oBAAI;AACjB,cAAA,aAAaA,oBAAI;AACjB,cAAA,cAAcA,oBAAI;AAGxBD,sBAAa,iDACR,aACA,aACA;AAAA,MACL,OACK;AAEC,cAAA,OAAO,MAAMC,oBAAI;AACvBD,sBAAa,KAAK,CAAC;AAAA,MACrB;AAEA,oBAAcA,WAAU;AACjBA,aAAAA;AAAAA,aACA,OAAO;AACdC,oBAAA,MAAA,MAAA,SAAA,2BAAc,8BAA8B,KAAK;AAC1C,aAAA;AAAA,IACT;AAAA,EAAA;AAII,QAAA,iBAAiB,CAAC,gBAAsC;AAC5D,aAAS,QAAQ,kCAAK,SAAS,QAAU;AAC5B;EAAA;AAIf,QAAM,eAAe,MAAM;AACrB,QAAA;AACFA,0BAAI,eAAeC,oBAAAA,aAAa,UAAU,KAAK,UAAU,SAAS,KAAK,CAAC;AAAA,aACjE,OAAO;AACdD,oBAAA,gDAAc,4BAA4B,KAAK;AAAA,IACjD;AAAA,EAAA;AAIF,QAAM,eAAe,MAAM;AACrB,QAAA;AACF,YAAM,iBAAiBA,cAAA,MAAI,eAAeC,oBAAA,aAAa,QAAQ;AAC/D,UAAI,gBAAgB;AACT,iBAAA,QAAQ,kCAAK,SAAS,QAAU,KAAK,MAAM,cAAc;AAAA,MACpE;AAAA,aACO,OAAO;AACdD,oBAAA,gDAAc,4BAA4B,KAAK;AAAA,IACjD;AAAA,EAAA;AAIF,QAAM,YAAY,CAAC,OAAe,OAAiD,WAAW;AAC5FA,kBAAAA,MAAI,UAAU;AAAA,MACZ;AAAA,MACA;AAAA,MACA,UAAU;AAAA,IAAA,CACX;AAAA,EAAA;AAIG,QAAA,cAAc,CAAC,QAAgB,aAAa;AAChD,eAAW,IAAI;AACXA,kBAAAA,MAAA,YAAY,EAAE,MAAA,CAAO;AAAA,EAAA;AAI3B,QAAM,cAAc,MAAM;AACxB,eAAW,KAAK;AAChBA,kBAAA,MAAI,YAAY;AAAA,EAAA;AAIZ,QAAA,YAAY,CAAC,YAMb;AACG,WAAA,IAAI,QAAiB,CAAC,YAAY;AACvCA,oBAAAA,MAAI,UAAU;AAAA,QACZ,OAAO,QAAQ,SAAS;AAAA,QACxB,SAAS,QAAQ;AAAA,QACjB,YAAY,QAAQ,eAAe;AAAA,QACnC,aAAa,QAAQ,eAAe;AAAA,QACpC,YAAY,QAAQ,cAAc;AAAA,QAClC,SAAS,CAAC,QAAQ;AAChB,kBAAQ,IAAI,OAAO;AAAA,QACrB;AAAA,QACA,MAAM,MAAM;AACV,kBAAQ,KAAK;AAAA,QACf;AAAA,MAAA,CACD;AAAA,IAAA,CACF;AAAA,EAAA;AAIG,QAAA,aAAa,CAAC,KAAa,WAAiC;AAChE,QAAI,UAAU;AACd,QAAI,QAAQ;AACV,YAAM,QAAQ,OAAO,KAAK,MAAM,EAC7B,IAAI,SAAO,GAAG,GAAG,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,EACtD,KAAK,GAAG;AACX,iBAAW,IAAI,KAAK;AAAA,IACtB;AAEAA,kBAAAA,MAAI,WAAW,EAAE,KAAK,QAAS,CAAA;AAAA,EAAA;AAI3B,QAAA,aAAa,CAAC,KAAa,WAAiC;AAChE,QAAI,UAAU;AACd,QAAI,QAAQ;AACV,YAAM,QAAQ,OAAO,KAAK,MAAM,EAC7B,IAAI,SAAO,GAAG,GAAG,IAAI,mBAAmB,OAAO,GAAG,CAAC,CAAC,EAAE,EACtD,KAAK,GAAG;AACX,iBAAW,IAAI,KAAK;AAAA,IACtB;AAEAA,kBAAAA,MAAI,WAAW,EAAE,KAAK,QAAS,CAAA;AAAA,EAAA;AAI3B,QAAA,YAAY,CAAC,QAAgB;AAC7BA,kBAAAA,MAAA,UAAU,EAAE,IAAA,CAAK;AAAA,EAAA;AAIjB,QAAA,eAAe,CAAC,QAAgB,MAAM;AACtCA,kBAAAA,MAAA,aAAa,EAAE,MAAA,CAAO;AAAA,EAAA;AAI5B,QAAM,qBAAqB,MAAM;AAC/BA,kBAAAA,MAAI,eAAe;AAAA,MACjB,SAAS,CAAC,QAAQ;AACC,yBAAA,IAAI,gBAAgB,MAAM;AAAA,MAC7C;AAAA,MACA,MAAM,MAAM;AACV,yBAAiB,KAAK;AAAA,MACxB;AAAA,IAAA,CACD;AAAA,EAAA;AAIH,QAAM,qBAAqB,MAAM;AAC3BA,wBAAA,sBAAsB,CAAC,QAAQ;AACjC,uBAAiB,IAAI,WAAW;AAAA,IAAA,CACjC;AAAA,EAAA;AAIH,QAAM,UAAU,MAAY;AAEb;AAGb,UAAM,cAAc;AAGD;AAGA;EAAA;AAGd,SAAA;AAAA;AAAA,IAEL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,IAGA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EAAA;AAEJ,CAAC;;"}