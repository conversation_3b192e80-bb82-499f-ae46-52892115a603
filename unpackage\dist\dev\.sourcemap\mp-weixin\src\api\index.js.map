{"version": 3, "file": "index.js", "sources": ["src/api/index.ts"], "sourcesContent": ["import request from '../utils/request'\nimport type {\n  User,\n  Announcement,\n  QuestionCategory,\n  Question,\n  Exam,\n  ExamRecord,\n  ExamVenue,\n  Certificate,\n  PaginationParams,\n  PaginationResponse,\n  ApiResponse\n} from '../types'\n\n// 用户相关API\nexport const userApi = {\n  // 微信登录\n  wxLogin: (data: { code: string; userInfo: any }) => \n    request.post<{ token: string; user: User }>('/auth/wx-login', data),\n\n  // 提交个人资料\n  submitProfile: (data: FormData) => \n    request.post<User>('/user/profile', data),\n\n  // 获取用户信息\n  getUserInfo: () => \n    request.get<User>('/user/info'),\n\n  // 更新用户信息\n  updateUserInfo: (data: Partial<User>) => \n    request.put<User>('/user/info', data),\n\n  // 上传头像\n  uploadAvatar: (filePath: string) => \n    request.upload({\n      url: '/user/avatar',\n      filePath,\n      name: 'avatar',\n    }),\n\n  // 上传本人照片\n  uploadPhoto: (filePath: string) => \n    request.upload({\n      url: '/user/photo',\n      filePath,\n      name: 'photo',\n    }),\n}\n\n// 信息中心API\nexport const infoApi = {\n  // 获取公告列表\n  getAnnouncements: (params: PaginationParams & { type?: string }) => \n    request.get<PaginationResponse<Announcement>>('/announcements', params),\n\n  // 获取公告详情\n  getAnnouncementDetail: (id: string) => \n    request.get<Announcement>(`/announcements/${id}`),\n\n  // 获取政策法规列表\n  getPolicies: (params: PaginationParams) => \n    request.get<PaginationResponse<Announcement>>('/policies', params),\n\n  // 获取重要通知列表\n  getNotices: (params: PaginationParams) => \n    request.get<PaginationResponse<Announcement>>('/notices', params),\n}\n\n// 学习中心API\nexport const studyApi = {\n  // 获取题库分类\n  getCategories: () => \n    request.get<QuestionCategory[]>('/study/categories'),\n\n  // 获取题目列表\n  getQuestions: (categoryId: string, count: number = 10) => \n    request.get<Question[]>('/study/questions', { categoryId, count }),\n\n  // 提交练习结果\n  submitPractice: (data: {\n    categoryId: string\n    questions: string[]\n    answers: Record<string, any>\n    score: number\n    correctCount: number\n  }) => \n    request.post('/study/practice', data),\n\n  // 获取练习历史\n  getPracticeHistory: (params: PaginationParams) => \n    request.get<PaginationResponse<any>>('/study/practice/history', params),\n\n  // 获取学习统计\n  getStudyStats: () => \n    request.get<{\n      totalSessions: number\n      totalQuestions: number\n      totalCorrect: number\n      averageScore: number\n      accuracy: number\n    }>('/study/stats'),\n}\n\n// 考试中心API\nexport const examApi = {\n  // 获取当前考试列表\n  getCurrentExams: () => \n    request.get<Exam[]>('/exams/current'),\n\n  // 获取考试详情\n  getExamDetail: (id: string) => \n    request.get<Exam>(`/exams/${id}`),\n\n  // 获取考试题目\n  getExamQuestions: (examId: string) => \n    request.get<Question[]>(`/exams/${examId}/questions`),\n\n  // 开始考试\n  startExam: (examId: string) => \n    request.post<{ recordId: string }>(`/exams/${examId}/start`),\n\n  // 人脸识别验证\n  verifyFace: (data: { examId: string; photo: string }) => \n    request.post<{ success: boolean; similarity: number }>('/exams/verify-face', data),\n\n  // 提交答案\n  submitAnswers: (data: {\n    recordId: string\n    answers: Record<string, any>\n    duration: number\n    antiCheatLogs?: any[]\n  }) => \n    request.post('/exams/submit', data),\n\n  // 获取考试记录\n  getExamRecords: (params: PaginationParams) => \n    request.get<PaginationResponse<ExamRecord>>('/exams/records', params),\n\n  // 获取线下考试场地\n  getExamVenues: (examId: string) => \n    request.get<ExamVenue[]>(`/exams/${examId}/venues`),\n\n  // 预约线下考试\n  bookOfflineExam: (data: { scheduleId: string }) => \n    request.post('/exams/book', data),\n\n  // 取消预约\n  cancelBooking: (registrationId: string) => \n    request.delete(`/exams/booking/${registrationId}`),\n\n  // 上传防作弊日志\n  uploadAntiCheatLog: (data: {\n    recordId: string\n    type: string\n    timestamp: string\n    data: any\n  }) => \n    request.post('/exams/anti-cheat-log', data),\n}\n\n// 个人中心API\nexport const personalApi = {\n  // 获取证书列表\n  getCertificates: () => \n    request.get<Certificate[]>('/certificates'),\n\n  // 获取证书详情\n  getCertificateDetail: (id: string) => \n    request.get<Certificate>(`/certificates/${id}`),\n\n  // 下载证书\n  downloadCertificate: (id: string) => \n    request.get<{ url: string }>(`/certificates/${id}/download`),\n\n  // 提交反馈\n  submitFeedback: (data: { content: string }) => \n    request.post('/feedback', data),\n\n  // 获取关于我们信息\n  getAboutInfo: () => \n    request.get<{\n      appName: string\n      version: string\n      description: string\n      contact: string\n      copyright: string\n    }>('/about'),\n}\n\n// 通用API\nexport const commonApi = {\n  // 获取机构列表\n  getOrganizations: (keyword?: string) => \n    request.get<Array<{ id: string; name: string }>>('/common/organizations', { keyword }),\n\n  // 获取职位列表\n  getPositions: (keyword?: string) => \n    request.get<Array<{ id: string; name: string }>>('/common/positions', { keyword }),\n\n  // 上传文件\n  uploadFile: (filePath: string, type: string = 'image') => \n    request.upload({\n      url: '/common/upload',\n      filePath,\n      name: 'file',\n      formData: { type },\n    }),\n\n  // 获取系统配置\n  getSystemConfig: () => \n    request.get<{\n      practiceConfig: {\n        questionsPerSession: number\n        freeSessionsPerDay: number\n      }\n      examConfig: {\n        faceVerifyMaxRetry: number\n        autoSubmitBeforeEnd: number\n        maxSwitchCount: number\n      }\n    }>('/common/config'),\n}\n\n// 导出所有API\nexport default {\n  user: userApi,\n  info: infoApi,\n  study: studyApi,\n  exam: examApi,\n  personal: personalApi,\n  common: commonApi,\n}\n"], "names": ["request"], "mappings": ";;AAgBO,MAAM,UAAU;AAAA;AAAA,EAErB,SAAS,CAAC,SACRA,kBAAAA,QAAQ,KAAoC,kBAAkB,IAAI;AAAA;AAAA,EAGpE,eAAe,CAAC,SACdA,kBAAAA,QAAQ,KAAW,iBAAiB,IAAI;AAAA;AAAA,EAG1C,aAAa,MACXA,kBAAAA,QAAQ,IAAU,YAAY;AAAA;AAAA,EAGhC,gBAAgB,CAAC,SACfA,kBAAAA,QAAQ,IAAU,cAAc,IAAI;AAAA;AAAA,EAGtC,cAAc,CAAC,aACbA,kBAAA,QAAQ,OAAO;AAAA,IACb,KAAK;AAAA,IACL;AAAA,IACA,MAAM;AAAA,EAAA,CACP;AAAA;AAAA,EAGH,aAAa,CAAC,aACZA,kBAAA,QAAQ,OAAO;AAAA,IACb,KAAK;AAAA,IACL;AAAA,IACA,MAAM;AAAA,EAAA,CACP;AACL;AAGO,MAAM,UAAU;AAAA;AAAA,EAErB,kBAAkB,CAAC,WACjBA,kBAAAA,QAAQ,IAAsC,kBAAkB,MAAM;AAAA;AAAA,EAGxE,uBAAuB,CAAC,OACtBA,0BAAQ,IAAkB,kBAAkB,EAAE,EAAE;AAAA;AAAA,EAGlD,aAAa,CAAC,WACZA,kBAAAA,QAAQ,IAAsC,aAAa,MAAM;AAAA;AAAA,EAGnE,YAAY,CAAC,WACXA,kBAAAA,QAAQ,IAAsC,YAAY,MAAM;AACpE;AAGO,MAAM,WAAW;AAAA;AAAA,EAEtB,eAAe,MACbA,kBAAAA,QAAQ,IAAwB,mBAAmB;AAAA;AAAA,EAGrD,cAAc,CAAC,YAAoB,QAAgB,OACjDA,kBAAAA,QAAQ,IAAgB,oBAAoB,EAAE,YAAY,OAAO;AAAA;AAAA,EAGnE,gBAAgB,CAAC,SAOfA,kBAAAA,QAAQ,KAAK,mBAAmB,IAAI;AAAA;AAAA,EAGtC,oBAAoB,CAAC,WACnBA,kBAAAA,QAAQ,IAA6B,2BAA2B,MAAM;AAAA;AAAA,EAGxE,eAAe,MACbA,kBAAAA,QAAQ,IAML,cAAc;AACrB;AAGO,MAAM,UAAU;AAAA;AAAA,EAErB,iBAAiB,MACfA,kBAAAA,QAAQ,IAAY,gBAAgB;AAAA;AAAA,EAGtC,eAAe,CAAC,OACdA,0BAAQ,IAAU,UAAU,EAAE,EAAE;AAAA;AAAA,EAGlC,kBAAkB,CAAC,WACjBA,0BAAQ,IAAgB,UAAU,MAAM,YAAY;AAAA;AAAA,EAGtD,WAAW,CAAC,WACVA,0BAAQ,KAA2B,UAAU,MAAM,QAAQ;AAAA;AAAA,EAG7D,YAAY,CAAC,SACXA,kBAAAA,QAAQ,KAA+C,sBAAsB,IAAI;AAAA;AAAA,EAGnF,eAAe,CAAC,SAMdA,kBAAAA,QAAQ,KAAK,iBAAiB,IAAI;AAAA;AAAA,EAGpC,gBAAgB,CAAC,WACfA,kBAAAA,QAAQ,IAAoC,kBAAkB,MAAM;AAAA;AAAA,EAGtE,eAAe,CAAC,WACdA,0BAAQ,IAAiB,UAAU,MAAM,SAAS;AAAA;AAAA,EAGpD,iBAAiB,CAAC,SAChBA,kBAAAA,QAAQ,KAAK,eAAe,IAAI;AAAA;AAAA,EAGlC,eAAe,CAAC,mBACdA,0BAAQ,OAAO,kBAAkB,cAAc,EAAE;AAAA;AAAA,EAGnD,oBAAoB,CAAC,SAMnBA,kBAAAA,QAAQ,KAAK,yBAAyB,IAAI;AAC9C;AAGO,MAAM,cAAc;AAAA;AAAA,EAEzB,iBAAiB,MACfA,kBAAAA,QAAQ,IAAmB,eAAe;AAAA;AAAA,EAG5C,sBAAsB,CAAC,OACrBA,0BAAQ,IAAiB,iBAAiB,EAAE,EAAE;AAAA;AAAA,EAGhD,qBAAqB,CAAC,OACpBA,0BAAQ,IAAqB,iBAAiB,EAAE,WAAW;AAAA;AAAA,EAG7D,gBAAgB,CAAC,SACfA,kBAAAA,QAAQ,KAAK,aAAa,IAAI;AAAA;AAAA,EAGhC,cAAc,MACZA,kBAAAA,QAAQ,IAML,QAAQ;AACf;AAGO,MAAM,YAAY;AAAA;AAAA,EAEvB,kBAAkB,CAAC,YACjBA,kBAAA,QAAQ,IAAyC,yBAAyB,EAAE,SAAS;AAAA;AAAA,EAGvF,cAAc,CAAC,YACbA,kBAAA,QAAQ,IAAyC,qBAAqB,EAAE,SAAS;AAAA;AAAA,EAGnF,YAAY,CAAC,UAAkB,OAAe,YAC5CA,kBAAAA,QAAQ,OAAO;AAAA,IACb,KAAK;AAAA,IACL;AAAA,IACA,MAAM;AAAA,IACN,UAAU,EAAE,KAAK;AAAA,EAAA,CAClB;AAAA;AAAA,EAGH,iBAAiB,MACfA,kBAAAA,QAAQ,IAUL,gBAAgB;AACvB;AAGA,MAAe,MAAA;AAAA,EACb,MAAM;AAAA,EACN,MAAM;AAAA,EACN,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU;AAAA,EACV,QAAQ;AACV;;"}