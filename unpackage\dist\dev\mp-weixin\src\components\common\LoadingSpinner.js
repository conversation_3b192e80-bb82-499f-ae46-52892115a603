"use strict";
const common_vendor = require("../../../common/vendor.js");
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "LoadingSpinner",
  props: {
    size: { default: "medium" },
    text: { default: "" },
    overlay: { type: <PERSON><PERSON><PERSON>, default: false }
  },
  setup(__props) {
    const props = __props;
    const sizeClass = common_vendor.computed(() => {
      return `spinner-${props.size}`;
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.f(12, (i, k0, i0) => {
          return {
            a: i,
            b: `rotate(${i * 30}deg)`
          };
        }),
        b: common_vendor.n(sizeClass.value),
        c: _ctx.text
      }, _ctx.text ? {
        d: common_vendor.t(_ctx.text)
      } : {}, {
        e: _ctx.overlay ? 1 : ""
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-edb94d20"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/src/components/common/LoadingSpinner.js.map
