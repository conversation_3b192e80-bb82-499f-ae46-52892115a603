{"version": 3, "file": "reading.js", "sources": ["subpages/exam/online/reading.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/c3VicGFnZXNcZXhhbVxvbmxpbmVccmVhZGluZy52dWU"], "sourcesContent": ["<template>\n  <view class=\"reading-container\">\n    <!-- 自定义导航栏 -->\n    <u-navbar \n      title=\"考前须知\" \n      :autoBack=\"true\"\n      :background=\"{ background: 'linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)' }\"\n      titleStyle=\"color: #fff; font-weight: bold;\"\n    />\n    \n    <!-- 考试信息卡片 -->\n    <view v-if=\"examInfo\" class=\"exam-info-card\">\n      <view class=\"exam-header\">\n        <text class=\"exam-title\">{{ examInfo.name }}</text>\n        <view class=\"exam-type-tag online\">\n          <text>线上考试</text>\n        </view>\n      </view>\n      \n      <view class=\"exam-details\">\n        <view class=\"detail-row\">\n          <u-icon name=\"clock\" color=\"#4A90E2\" size=\"32\" />\n          <text class=\"detail-label\">考试时长：</text>\n          <text class=\"detail-value\">{{ examInfo.duration }}分钟</text>\n        </view>\n        <view class=\"detail-row\">\n          <u-icon name=\"file-text\" color=\"#4A90E2\" size=\"32\" />\n          <text class=\"detail-label\">总分：</text>\n          <text class=\"detail-value\">{{ examInfo.totalScore }}分</text>\n        </view>\n        <view class=\"detail-row\">\n          <u-icon name=\"checkmark-circle\" color=\"#4A90E2\" size=\"32\" />\n          <text class=\"detail-label\">及格分：</text>\n          <text class=\"detail-value\">{{ examInfo.passScore }}分</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 考试须知内容 -->\n    <view class=\"reading-content\">\n      <view class=\"content-section\">\n        <view class=\"section-header\">\n          <u-icon name=\"warning\" color=\"#FF9500\" size=\"32\" />\n          <text class=\"section-title\">重要提醒</text>\n        </view>\n        <view class=\"notice-list\">\n          <view class=\"notice-item important\">\n            <u-icon name=\"exclamation-circle\" color=\"#f56c6c\" size=\"24\" />\n            <text>考试过程中请保持网络连接稳定，避免因网络问题影响考试</text>\n          </view>\n          <view class=\"notice-item important\">\n            <u-icon name=\"exclamation-circle\" color=\"#f56c6c\" size=\"24\" />\n            <text>考试开始后不允许切换应用或退出考试界面，否则将被视为作弊</text>\n          </view>\n          <view class=\"notice-item important\">\n            <u-icon name=\"exclamation-circle\" color=\"#f56c6c\" size=\"24\" />\n            <text>请确保设备电量充足，建议在考试前充电至80%以上</text>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"content-section\">\n        <view class=\"section-header\">\n          <u-icon name=\"shield\" color=\"#4A90E2\" size=\"32\" />\n          <text class=\"section-title\">身份验证要求</text>\n        </view>\n        <view class=\"auth-requirements\">\n          <view class=\"requirement-item\">\n            <view class=\"requirement-icon\">\n              <u-icon name=\"camera\" color=\"#4A90E2\" size=\"40\" />\n            </view>\n            <view class=\"requirement-content\">\n              <text class=\"requirement-title\">人脸识别验证</text>\n              <text class=\"requirement-desc\">考试前需要进行人脸识别验证，请确保光线充足，面部清晰可见</text>\n            </view>\n          </view>\n          <view class=\"requirement-item\">\n            <view class=\"requirement-icon\">\n              <u-icon name=\"account\" color=\"#4A90E2\" size=\"40\" />\n            </view>\n            <view class=\"requirement-content\">\n              <text class=\"requirement-title\">本人参考</text>\n              <text class=\"requirement-desc\">必须本人参加考试，不得代考或使用他人身份信息</text>\n            </view>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"content-section\">\n        <view class=\"section-header\">\n          <u-icon name=\"list\" color=\"#4A90E2\" size=\"32\" />\n          <text class=\"section-title\">考试规则</text>\n        </view>\n        <view class=\"rules-list\">\n          <view class=\"rule-item\">\n            <text class=\"rule-number\">1</text>\n            <text class=\"rule-text\">考试时间到达后系统将自动提交答案，请合理安排答题时间</text>\n          </view>\n          <view class=\"rule-item\">\n            <text class=\"rule-number\">2</text>\n            <text class=\"rule-text\">每道题目只能作答一次，提交后不可修改，请仔细检查后再提交</text>\n          </view>\n          <view class=\"rule-item\">\n            <text class=\"rule-number\">3</text>\n            <text class=\"rule-text\">考试过程中如遇技术问题，请及时联系技术支持</text>\n          </view>\n          <view class=\"rule-item\">\n            <text class=\"rule-number\">4</text>\n            <text class=\"rule-text\">考试结束后可立即查看成绩，证书将在审核通过后发放</text>\n          </view>\n        </view>\n      </view>\n      \n      <view class=\"content-section\">\n        <view class=\"section-header\">\n          <u-icon name=\"settings\" color=\"#4A90E2\" size=\"32\" />\n          <text class=\"section-title\">设备要求</text>\n        </view>\n        <view class=\"device-requirements\">\n          <view class=\"device-item\">\n            <u-icon name=\"wifi\" color=\"#4CAF50\" size=\"32\" />\n            <view class=\"device-content\">\n              <text class=\"device-title\">网络连接</text>\n              <text class=\"device-desc\">稳定的WiFi或4G网络</text>\n            </view>\n            <view class=\"device-status\" :class=\"{ good: networkStatus }\">\n              <text>{{ networkStatus ? '正常' : '异常' }}</text>\n            </view>\n          </view>\n          <view class=\"device-item\">\n            <u-icon name=\"battery-charging\" color=\"#4CAF50\" size=\"32\" />\n            <view class=\"device-content\">\n              <text class=\"device-title\">设备电量</text>\n              <text class=\"device-desc\">建议80%以上电量</text>\n            </view>\n            <view class=\"device-status\" :class=\"{ good: batteryLevel >= 80 }\">\n              <text>{{ batteryLevel }}%</text>\n            </view>\n          </view>\n          <view class=\"device-item\">\n            <u-icon name=\"camera\" color=\"#4CAF50\" size=\"32\" />\n            <view class=\"device-content\">\n              <text class=\"device-title\">摄像头权限</text>\n              <text class=\"device-desc\">用于人脸识别验证</text>\n            </view>\n            <view class=\"device-status\" :class=\"{ good: cameraPermission }\">\n              <text>{{ cameraPermission ? '已授权' : '未授权' }}</text>\n            </view>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 确认按钮 -->\n    <view class=\"confirm-section\">\n      <view class=\"confirm-checkbox\">\n        <u-checkbox \n          v-model=\"hasRead\" \n          :customStyle=\"{ marginRight: '16rpx' }\"\n          activeColor=\"#4A90E2\"\n        />\n        <text class=\"checkbox-text\">我已仔细阅读并同意遵守以上考试规则和要求</text>\n      </view>\n      \n      <u-button \n        class=\"start-exam-btn\"\n        type=\"primary\"\n        :disabled=\"!hasRead || !canStartExam\"\n        :loading=\"isStarting\"\n        loadingText=\"准备中...\"\n        @click=\"startExam\"\n      >\n        开始考试\n      </u-button>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport { useAppStore } from '../../../src/stores/app'\nimport { PAGE_PATHS } from '../../../src/constants'\nimport api from '../../../src/api'\nimport type { Exam } from '../../../src/types'\n\n// Store\nconst appStore = useAppStore()\n\n// 页面参数\nconst props = defineProps<{\n  examId: string\n}>()\n\n// 响应式数据\nconst examInfo = ref<Exam | null>(null)\nconst hasRead = ref(false)\nconst isStarting = ref(false)\nconst networkStatus = ref(true)\nconst batteryLevel = ref(100)\nconst cameraPermission = ref(false)\n\n// 计算属性\nconst canStartExam = computed(() => {\n  return networkStatus.value && batteryLevel.value >= 20 && cameraPermission.value\n})\n\nonMounted(() => {\n  loadExamInfo()\n  checkDeviceStatus()\n})\n\n// 加载考试信息\nconst loadExamInfo = async () => {\n  try {\n    const response = await api.exam.getExamDetail(props.examId)\n    examInfo.value = response.data\n  } catch (error: any) {\n    uni.__f__('error','at subpages/exam/online/reading.vue:218','加载考试信息失败:', error)\n    appStore.showToast(error.message || '加载失败')\n    appStore.navigateBack()\n  }\n}\n\n// 检查设备状态\nconst checkDeviceStatus = async () => {\n  try {\n    // 检查网络状态\n    const networkInfo = await uni.getNetworkType()\n    networkStatus.value = networkInfo[1].networkType !== 'none'\n    \n    // 检查电池状态\n    const batteryInfo = await uni.getBatteryInfo()\n    batteryLevel.value = batteryInfo[1].level\n    \n    // 检查摄像头权限\n    const authResult = await uni.authorize({\n      scope: 'scope.camera'\n    })\n    cameraPermission.value = true\n  } catch (error) {\n    uni.__f__('error','at subpages/exam/online/reading.vue:241','设备状态检查失败:', error)\n    // 尝试获取摄像头权限\n    try {\n      await uni.authorize({\n        scope: 'scope.camera'\n      })\n      cameraPermission.value = true\n    } catch (authError) {\n      cameraPermission.value = false\n      appStore.showModal({\n        title: '需要摄像头权限',\n        content: '考试需要使用摄像头进行人脸识别验证，请在设置中开启摄像头权限。',\n        confirmText: '去设置',\n        cancelText: '取消'\n      }).then((confirmed) => {\n        if (confirmed) {\n          uni.openSetting()\n        }\n      })\n    }\n  }\n}\n\n// 开始考试\nconst startExam = async () => {\n  if (!hasRead.value) {\n    appStore.showToast('请先阅读并同意考试规则')\n    return\n  }\n  \n  if (!canStartExam.value) {\n    appStore.showToast('设备状态不满足考试要求')\n    return\n  }\n  \n  isStarting.value = true\n  \n  try {\n    // 检查考试状态\n    const examDetail = await api.exam.getExamDetail(props.examId)\n    const exam = examDetail.data\n    \n    if (exam.status !== 'not_started' && exam.status !== 'in_progress') {\n      appStore.showToast('考试已结束或不在考试时间内')\n      appStore.navigateBack()\n      return\n    }\n    \n    // 跳转到人脸识别验证页面\n    appStore.redirectTo(PAGE_PATHS.EXAM_ONLINE_FACE_VERIFY, { \n      examId: props.examId \n    })\n  } catch (error: any) {\n    uni.__f__('error','at subpages/exam/online/reading.vue:294','开始考试失败:', error)\n    appStore.showToast(error.message || '开始考试失败，请重试')\n  } finally {\n    isStarting.value = false\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../../src/styles/global.scss';\n\n.reading-container {\n  min-height: 100vh;\n  background: $acdc-bg-primary;\n  padding-bottom: 200rpx;\n}\n\n.exam-info-card {\n  margin: 24rpx;\n  background: #fff;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  \n  .exam-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 32rpx;\n    \n    .exam-title {\n      flex: 1;\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n    }\n    \n    .exam-type-tag {\n      padding: 8rpx 16rpx;\n      border-radius: 12rpx;\n      font-size: 20rpx;\n      color: #fff;\n      \n      &.online {\n        background: #4A90E2;\n      }\n    }\n  }\n  \n  .exam-details {\n    .detail-row {\n      display: flex;\n      align-items: center;\n      margin-bottom: 16rpx;\n      \n      &:last-child {\n        margin-bottom: 0;\n      }\n      \n      .detail-label {\n        margin-left: 16rpx;\n        font-size: 26rpx;\n        color: #666;\n      }\n      \n      .detail-value {\n        margin-left: 8rpx;\n        font-size: 26rpx;\n        font-weight: bold;\n        color: #4A90E2;\n      }\n    }\n  }\n}\n\n.reading-content {\n  margin: 24rpx;\n  \n  .content-section {\n    background: #fff;\n    border-radius: 24rpx;\n    padding: 40rpx;\n    margin-bottom: 24rpx;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n    \n    .section-header {\n      display: flex;\n      align-items: center;\n      margin-bottom: 32rpx;\n      \n      .section-title {\n        margin-left: 16rpx;\n        font-size: 30rpx;\n        font-weight: bold;\n        color: #333;\n      }\n    }\n  }\n}\n\n.notice-list {\n  .notice-item {\n    display: flex;\n    align-items: flex-start;\n    padding: 20rpx;\n    margin-bottom: 16rpx;\n    border-radius: 12rpx;\n    \n    &.important {\n      background: #fff7e6;\n      border: 2rpx solid #ffd591;\n    }\n    \n    text {\n      flex: 1;\n      margin-left: 16rpx;\n      font-size: 26rpx;\n      line-height: 1.5;\n      color: #333;\n    }\n  }\n}\n\n.auth-requirements {\n  .requirement-item {\n    display: flex;\n    align-items: flex-start;\n    margin-bottom: 32rpx;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n    \n    .requirement-icon {\n      width: 80rpx;\n      height: 80rpx;\n      background: rgba(74, 144, 226, 0.1);\n      border-radius: 40rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 24rpx;\n    }\n    \n    .requirement-content {\n      flex: 1;\n      \n      .requirement-title {\n        display: block;\n        font-size: 28rpx;\n        font-weight: bold;\n        color: #333;\n        margin-bottom: 8rpx;\n      }\n      \n      .requirement-desc {\n        font-size: 24rpx;\n        color: #666;\n        line-height: 1.5;\n      }\n    }\n  }\n}\n\n.rules-list {\n  .rule-item {\n    display: flex;\n    align-items: flex-start;\n    margin-bottom: 24rpx;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n    \n    .rule-number {\n      width: 40rpx;\n      height: 40rpx;\n      background: #4A90E2;\n      border-radius: 20rpx;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 20rpx;\n      font-weight: bold;\n      color: #fff;\n      margin-right: 20rpx;\n      flex-shrink: 0;\n    }\n    \n    .rule-text {\n      flex: 1;\n      font-size: 26rpx;\n      line-height: 1.5;\n      color: #333;\n    }\n  }\n}\n\n.device-requirements {\n  .device-item {\n    display: flex;\n    align-items: center;\n    padding: 24rpx 0;\n    border-bottom: 2rpx solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .device-content {\n      flex: 1;\n      margin-left: 20rpx;\n      \n      .device-title {\n        display: block;\n        font-size: 28rpx;\n        font-weight: bold;\n        color: #333;\n        margin-bottom: 4rpx;\n      }\n      \n      .device-desc {\n        font-size: 24rpx;\n        color: #666;\n      }\n    }\n    \n    .device-status {\n      padding: 8rpx 16rpx;\n      border-radius: 12rpx;\n      font-size: 24rpx;\n      background: #fff2f0;\n      color: #f56c6c;\n      \n      &.good {\n        background: #f6ffed;\n        color: #4CAF50;\n      }\n    }\n  }\n}\n\n.confirm-section {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  background: #fff;\n  padding: 32rpx;\n  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));\n  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);\n  \n  .confirm-checkbox {\n    display: flex;\n    align-items: flex-start;\n    margin-bottom: 32rpx;\n    \n    .checkbox-text {\n      flex: 1;\n      font-size: 26rpx;\n      line-height: 1.5;\n      color: #333;\n    }\n  }\n  \n  .start-exam-btn {\n    width: 100%;\n    height: 88rpx;\n    border-radius: 44rpx;\n    font-size: 32rpx;\n    font-weight: bold;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/subpages/exam/online/reading.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useAppStore", "ref", "computed", "onMounted", "api", "uni", "PAGE_PATHS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0LA,UAAM,WAAWA,eAAAA;AAGjB,UAAM,QAAQ;AAKR,UAAA,WAAWC,kBAAiB,IAAI;AAChC,UAAA,UAAUA,kBAAI,KAAK;AACnB,UAAA,aAAaA,kBAAI,KAAK;AACtB,UAAA,gBAAgBA,kBAAI,IAAI;AACxB,UAAA,eAAeA,kBAAI,GAAG;AACtB,UAAA,mBAAmBA,kBAAI,KAAK;AAG5B,UAAA,eAAeC,cAAAA,SAAS,MAAM;AAClC,aAAO,cAAc,SAAS,aAAa,SAAS,MAAM,iBAAiB;AAAA,IAAA,CAC5E;AAEDC,kBAAAA,UAAU,MAAM;AACD;AACK;IAAA,CACnB;AAGD,UAAM,eAAe,MAAY;AAC3B,UAAA;AACF,cAAM,WAAW,MAAMC,kBAAI,KAAK,cAAc,MAAM,MAAM;AAC1D,iBAAS,QAAQ,SAAS;AAAA,eACnB,OAAY;AACnBC,sBAAA,MAAI,MAAM,SAAQ,2CAA0C,aAAa,KAAK;AACrE,iBAAA,UAAU,MAAM,WAAW,MAAM;AAC1C,iBAAS,aAAa;AAAA,MACxB;AAAA,IAAA;AAIF,UAAM,oBAAoB,MAAY;AAChC,UAAA;AAEI,cAAA,cAAc,MAAMA,oBAAI;AAC9B,sBAAc,QAAQ,YAAY,CAAC,EAAE,gBAAgB;AAG/C,cAAA,cAAc,MAAMA,oBAAI;AACjB,qBAAA,QAAQ,YAAY,CAAC,EAAE;AAG9B,cAAA,aAAa,MAAMA,cAAA,MAAI,UAAU;AAAA,UACrC,OAAO;AAAA,QAAA,CACR;AACD,yBAAiB,QAAQ;AAAA,eAClB,OAAO;AACdA,sBAAA,MAAI,MAAM,SAAQ,2CAA0C,aAAa,KAAK;AAE1E,YAAA;AACF,gBAAMA,cAAAA,MAAI,UAAU;AAAA,YAClB,OAAO;AAAA,UAAA,CACR;AACD,2BAAiB,QAAQ;AAAA,iBAClB,WAAW;AAClB,2BAAiB,QAAQ;AACzB,mBAAS,UAAU;AAAA,YACjB,OAAO;AAAA,YACP,SAAS;AAAA,YACT,aAAa;AAAA,YACb,YAAY;AAAA,UAAA,CACb,EAAE,KAAK,CAAC,cAAc;AACrB,gBAAI,WAAW;AACbA,4BAAA,MAAI,YAAY;AAAA,YAClB;AAAA,UAAA,CACD;AAAA,QACH;AAAA,MACF;AAAA,IAAA;AAIF,UAAM,YAAY,MAAY;AACxB,UAAA,CAAC,QAAQ,OAAO;AAClB,iBAAS,UAAU,aAAa;AAChC;AAAA,MACF;AAEI,UAAA,CAAC,aAAa,OAAO;AACvB,iBAAS,UAAU,aAAa;AAChC;AAAA,MACF;AAEA,iBAAW,QAAQ;AAEf,UAAA;AAEF,cAAM,aAAa,MAAMD,kBAAI,KAAK,cAAc,MAAM,MAAM;AAC5D,cAAM,OAAO,WAAW;AAExB,YAAI,KAAK,WAAW,iBAAiB,KAAK,WAAW,eAAe;AAClE,mBAAS,UAAU,eAAe;AAClC,mBAAS,aAAa;AACtB;AAAA,QACF;AAGS,iBAAA,WAAWE,+BAAW,yBAAyB;AAAA,UACtD,QAAQ,MAAM;AAAA,QAAA,CACf;AAAA,eACM,OAAY;AACnBD,sBAAA,MAAI,MAAM,SAAQ,2CAA0C,WAAW,KAAK;AACnE,iBAAA,UAAU,MAAM,WAAW,YAAY;AAAA,MAAA,UAChD;AACA,mBAAW,QAAQ;AAAA,MACrB;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxSF,GAAG,WAAW,eAAe;"}