{"version": 3, "file": "login.js", "sources": ["pages/login/login.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvbG9naW4vbG9naW4udnVl"], "sourcesContent": ["<template>\n  <view class=\"login-container\">\n    <!-- 背景装饰 -->\n    <view class=\"bg-decoration\">\n      <view class=\"circle circle-1\"></view>\n      <view class=\"circle circle-2\"></view>\n      <view class=\"circle circle-3\"></view>\n    </view>\n    \n    <!-- 主要内容 -->\n    <view class=\"main-content\">\n      <!-- Logo和标题 -->\n      <view class=\"header\">\n        <image \n          class=\"logo\" \n          src=\"/static/images/logo.png\" \n          mode=\"aspectFill\"\n        />\n        <text class=\"app-name\">疾控医护考试系统</text>\n        <text class=\"app-desc\">医护人员任职资格考试平台</text>\n      </view>\n      \n      <!-- 登录表单 -->\n      <view class=\"login-form\">\n        <!-- 协议勾选 -->\n        <view class=\"agreement-section\">\n          <u-checkbox \n            v-model=\"agreedToTerms\" \n            :customStyle=\"{ marginRight: '12rpx' }\"\n            activeColor=\"#2E8B57\"\n          />\n          <text class=\"agreement-text\">\n            我已阅读并同意\n            <text class=\"link-text\" @tap=\"showUserAgreement\">《用户服务协议》</text>\n            和\n            <text class=\"link-text\" @tap=\"showPrivacyPolicy\">《隐私政策》</text>\n          </text>\n        </view>\n        \n        <!-- 登录按钮 -->\n        <u-button \n          class=\"login-btn\"\n          type=\"primary\"\n          :disabled=\"!agreedToTerms || isLogging\"\n          :loading=\"isLogging\"\n          loadingText=\"登录中...\"\n          @click=\"handleWeChatLogin\"\n        >\n          <u-icon name=\"weixin-fill\" color=\"#fff\" size=\"32\" class=\"wechat-icon\" />\n          微信授权登录\n        </u-button>\n        \n        <!-- 温馨提示 -->\n        <view class=\"tips\">\n          <u-icon name=\"info-circle\" color=\"#999\" size=\"24\" />\n          <text class=\"tips-text\">首次登录需完善个人资料，通过机构审核后可使用完整功能</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 底部版权信息 -->\n    <view class=\"footer\">\n      <text class=\"copyright\">© 2024 疾控医护考试系统</text>\n    </view>\n    \n    <!-- 用户协议弹窗 -->\n    <u-popup \n      v-model=\"showAgreementModal\" \n      mode=\"center\" \n      width=\"640rpx\" \n      height=\"800rpx\"\n      :closeOnClickOverlay=\"true\"\n    >\n      <view class=\"agreement-modal\">\n        <view class=\"modal-header\">\n          <text class=\"modal-title\">{{ currentAgreementTitle }}</text>\n          <u-icon name=\"close\" size=\"32\" @click=\"showAgreementModal = false\" />\n        </view>\n        <scroll-view class=\"modal-content\" scroll-y>\n          <text class=\"agreement-content\">{{ currentAgreementContent }}</text>\n        </scroll-view>\n        <view class=\"modal-footer\">\n          <u-button type=\"primary\" @click=\"showAgreementModal = false\">我知道了</u-button>\n        </view>\n      </view>\n    </u-popup>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted } from 'vue'\nimport { useUserStore } from '../../src/stores/user'\nimport { useAppStore } from '../../src/stores/app'\nimport api from '../../src/api'\nimport { PAGE_PATHS } from '../../src/constants'\n\n// Store\nconst userStore = useUserStore()\nconst appStore = useAppStore()\n\n// 响应式数据\nconst agreedToTerms = ref(false)\nconst isLogging = ref(false)\nconst showAgreementModal = ref(false)\nconst currentAgreementTitle = ref('')\nconst currentAgreementContent = ref('')\n\n// 用户协议内容\nconst userAgreementContent = `1. 服务条款\n本服务条款是您与疾控医护考试系统之间的协议，规定了您使用本平台服务的权利和义务。\n\n2. 用户义务\n用户承诺提供真实、准确的个人信息，不得冒用他人身份或提供虚假信息。\n\n3. 隐私保护\n我们重视您的隐私保护，严格按照相关法律法规保护您的个人信息安全。\n\n4. 服务内容\n本平台提供疾控医护人员任职资格考试相关的学习、练习、考试等服务。\n\n5. 免责声明\n在法律允许的范围内，本平台对服务中断、数据丢失等情况不承担责任。`\n\nconst privacyPolicyContent = `1. 信息收集\n我们会收集您在使用服务时主动提供的信息，包括但不限于姓名、手机号、身份证号等。\n\n2. 信息使用\n收集的信息将用于提供更好的服务，包括身份验证、考试管理、证书颁发等。\n\n3. 信息保护\n我们采用行业标准的安全措施保护您的信息，不会向第三方泄露您的个人信息。\n\n4. 信息存储\n您的个人信息将存储在安全的服务器中，并采用加密技术保护。\n\n5. 权利保障\n您有权查看、修改、删除您的个人信息，如有需要请联系客服。`\n\n// 显示用户协议\nconst showUserAgreement = () => {\n  currentAgreementTitle.value = '用户服务协议'\n  currentAgreementContent.value = userAgreementContent\n  showAgreementModal.value = true\n}\n\n// 显示隐私政策\nconst showPrivacyPolicy = () => {\n  currentAgreementTitle.value = '隐私政策'\n  currentAgreementContent.value = privacyPolicyContent\n  showAgreementModal.value = true\n}\n\n// 微信登录处理\nconst handleWeChatLogin = async () => {\n  if (!agreedToTerms.value) {\n    appStore.showToast('请先同意用户协议')\n    return\n  }\n  \n  isLogging.value = true\n  \n  try {\n    // 调用微信登录\n    const loginResult = await uni.login()\n    \n    if (loginResult[1].code) {\n      // 获取用户信息\n      const userProfile = await uni.getUserProfile({\n        desc: '用于完善用户资料'\n      })\n      \n      // 调用后端登录接口\n      const response = await api.user.wxLogin({\n        code: loginResult[1].code,\n        userInfo: userProfile[1].userInfo\n      })\n      \n      // 保存用户信息\n      userStore.login(response.data.token, response.data.user)\n      \n      // 根据用户状态跳转\n      if (response.data.user.status === 'not_submitted') {\n        // 新用户或未完善资料，跳转到资料提交页\n        appStore.redirectTo(PAGE_PATHS.PROFILE)\n      } else {\n        // 已注册用户，跳转到主页\n        appStore.switchTab(PAGE_PATHS.INFO)\n      }\n    }\n  } catch (error: any) {\n    uni.__f__('error','at pages/login/login.vue:191','登录失败:', error)\n    \n    if (error.errMsg && error.errMsg.includes('getUserProfile:fail')) {\n      appStore.showToast('需要授权后才能登录')\n    } else {\n      appStore.showToast(error.message || '登录失败，请重试')\n    }\n  } finally {\n    isLogging.value = false\n  }\n}\n\n// 页面加载时检查登录状态\nonMounted(() => {\n  // 如果已经登录，直接跳转\n  if (userStore.isLoggedIn) {\n    if (userStore.isAuthenticated) {\n      appStore.switchTab(PAGE_PATHS.INFO)\n    } else {\n      appStore.redirectTo(PAGE_PATHS.PROFILE)\n    }\n  }\n})\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../src/styles/global.scss';\n\n.login-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);\n  position: relative;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  align-items: center;\n  padding: 40rpx 60rpx;\n}\n\n.bg-decoration {\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  overflow: hidden;\n  \n  .circle {\n    position: absolute;\n    border-radius: 50%;\n    background: rgba(255, 255, 255, 0.1);\n    \n    &.circle-1 {\n      width: 200rpx;\n      height: 200rpx;\n      top: 10%;\n      right: 10%;\n      animation: float 6s ease-in-out infinite;\n    }\n    \n    &.circle-2 {\n      width: 150rpx;\n      height: 150rpx;\n      bottom: 20%;\n      left: 15%;\n      animation: float 8s ease-in-out infinite reverse;\n    }\n    \n    &.circle-3 {\n      width: 100rpx;\n      height: 100rpx;\n      top: 30%;\n      left: 10%;\n      animation: float 7s ease-in-out infinite;\n    }\n  }\n}\n\n@keyframes float {\n  0%, 100% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20rpx);\n  }\n}\n\n.main-content {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  width: 100%;\n  max-width: 500rpx;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 100rpx;\n  \n  .logo {\n    width: 120rpx;\n    height: 120rpx;\n    border-radius: 24rpx;\n    margin-bottom: 40rpx;\n    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);\n  }\n  \n  .app-name {\n    display: block;\n    font-size: 48rpx;\n    font-weight: bold;\n    color: #fff;\n    margin-bottom: 16rpx;\n    text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n  }\n  \n  .app-desc {\n    display: block;\n    font-size: 28rpx;\n    color: rgba(255, 255, 255, 0.8);\n    line-height: 1.4;\n  }\n}\n\n.login-form {\n  background: rgba(255, 255, 255, 0.95);\n  border-radius: 32rpx;\n  padding: 60rpx 40rpx;\n  backdrop-filter: blur(10px);\n  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.1);\n}\n\n.agreement-section {\n  display: flex;\n  align-items: flex-start;\n  margin-bottom: 40rpx;\n  \n  .agreement-text {\n    flex: 1;\n    font-size: 26rpx;\n    color: #666;\n    line-height: 1.5;\n    \n    .link-text {\n      color: #2E8B57;\n      text-decoration: underline;\n    }\n  }\n}\n\n.login-btn {\n  width: 100%;\n  height: 88rpx;\n  border-radius: 44rpx;\n  background: linear-gradient(135deg, #2E8B57 0%, #228B22 100%);\n  box-shadow: 0 8rpx 24rpx rgba(46, 139, 87, 0.3);\n  margin-bottom: 40rpx;\n  \n  .wechat-icon {\n    margin-right: 16rpx;\n  }\n}\n\n.tips {\n  display: flex;\n  align-items: flex-start;\n  padding: 20rpx;\n  background: #f8f9fa;\n  border-radius: 16rpx;\n  border-left: 6rpx solid #2E8B57;\n  \n  .tips-text {\n    flex: 1;\n    font-size: 24rpx;\n    color: #666;\n    line-height: 1.5;\n    margin-left: 12rpx;\n  }\n}\n\n.footer {\n  margin-top: 60rpx;\n  \n  .copyright {\n    font-size: 24rpx;\n    color: rgba(255, 255, 255, 0.6);\n    text-align: center;\n  }\n}\n\n.agreement-modal {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  background: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  \n  .modal-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 40rpx;\n    border-bottom: 2rpx solid #f0f0f0;\n    \n    .modal-title {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n    }\n  }\n  \n  .modal-content {\n    flex: 1;\n    padding: 40rpx;\n    \n    .agreement-content {\n      font-size: 28rpx;\n      color: #666;\n      line-height: 1.6;\n      white-space: pre-line;\n    }\n  }\n  \n  .modal-footer {\n    padding: 40rpx;\n    border-top: 2rpx solid #f0f0f0;\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/login/login.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useAppStore", "ref", "uni", "api", "PAGE_PATHS", "onMounted"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4GA,MAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAe7B,MAAM,uBAAuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AA1B7B,UAAM,YAAYA,gBAAAA;AAClB,UAAM,WAAWC,eAAAA;AAGX,UAAA,gBAAgBC,kBAAI,KAAK;AACzB,UAAA,YAAYA,kBAAI,KAAK;AACrB,UAAA,qBAAqBA,kBAAI,KAAK;AAC9B,UAAA,wBAAwBA,kBAAI,EAAE;AAC9B,UAAA,0BAA0BA,kBAAI,EAAE;AAkCtC,UAAM,oBAAoB,MAAM;AAC9B,4BAAsB,QAAQ;AAC9B,8BAAwB,QAAQ;AAChC,yBAAmB,QAAQ;AAAA,IAAA;AAI7B,UAAM,oBAAoB,MAAM;AAC9B,4BAAsB,QAAQ;AAC9B,8BAAwB,QAAQ;AAChC,yBAAmB,QAAQ;AAAA,IAAA;AAI7B,UAAM,oBAAoB,MAAY;AAChC,UAAA,CAAC,cAAc,OAAO;AACxB,iBAAS,UAAU,UAAU;AAC7B;AAAA,MACF;AAEA,gBAAU,QAAQ;AAEd,UAAA;AAEI,cAAA,cAAc,MAAMC,oBAAI;AAE1B,YAAA,YAAY,CAAC,EAAE,MAAM;AAEjB,gBAAA,cAAc,MAAMA,cAAA,MAAI,eAAe;AAAA,YAC3C,MAAM;AAAA,UAAA,CACP;AAGD,gBAAM,WAAW,MAAMC,kBAAI,KAAK,QAAQ;AAAA,YACtC,MAAM,YAAY,CAAC,EAAE;AAAA,YACrB,UAAU,YAAY,CAAC,EAAE;AAAA,UAAA,CAC1B;AAGD,oBAAU,MAAM,SAAS,KAAK,OAAO,SAAS,KAAK,IAAI;AAGvD,cAAI,SAAS,KAAK,KAAK,WAAW,iBAAiB;AAExC,qBAAA,WAAWC,+BAAW,OAAO;AAAA,UAAA,OACjC;AAEI,qBAAA,UAAUA,+BAAW,IAAI;AAAA,UACpC;AAAA,QACF;AAAA,eACO,OAAY;AACnBF,sBAAA,MAAI,MAAM,SAAQ,gCAA+B,SAAS,KAAK;AAE/D,YAAI,MAAM,UAAU,MAAM,OAAO,SAAS,qBAAqB,GAAG;AAChE,mBAAS,UAAU,WAAW;AAAA,QAAA,OACzB;AACI,mBAAA,UAAU,MAAM,WAAW,UAAU;AAAA,QAChD;AAAA,MAAA,UACA;AACA,kBAAU,QAAQ;AAAA,MACpB;AAAA,IAAA;AAIFG,kBAAAA,UAAU,MAAM;AAEd,UAAI,UAAU,YAAY;AACxB,YAAI,UAAU,iBAAiB;AACpB,mBAAA,UAAUD,+BAAW,IAAI;AAAA,QAAA,OAC7B;AACI,mBAAA,WAAWA,+BAAW,OAAO;AAAA,QACxC;AAAA,MACF;AAAA,IAAA,CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnND,GAAG,WAAW,eAAe;"}