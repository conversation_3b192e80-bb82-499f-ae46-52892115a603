<view class="exam-center-container data-v-970fed46"><view class="status-bar data-v-970fed46" style="{{'height:' + a}}"></view><view class="header data-v-970fed46"><view class="header-content data-v-970fed46"><text class="page-title data-v-970fed46">考试中心</text><view class="header-actions data-v-970fed46"><u-icon wx:if="{{c}}" class="data-v-970fed46" bindclick="{{b}}" u-i="970fed46-0" bind:__l="__l" u-p="{{c}}"/></view></view></view><permission-wrapper wx:if="{{t}}" class="data-v-970fed46" u-s="{{['fallback','d']}}" u-i="970fed46-1" bind:__l="__l" u-p="{{t}}"><view class="auth-prompt data-v-970fed46" slot="fallback"><view class="prompt-card data-v-970fed46"><u-icon wx:if="{{d}}" class="data-v-970fed46" u-i="970fed46-2,970fed46-1" bind:__l="__l" u-p="{{d}}"/><text class="prompt-title data-v-970fed46">未认证，无法考试</text><text class="prompt-desc data-v-970fed46">请先完善个人资料并通过机构审核，才能参加考试</text><u-button wx:if="{{f}}" u-s="{{['d']}}" class="auth-btn data-v-970fed46" bindclick="{{e}}" u-i="970fed46-3,970fed46-1" bind:__l="__l" u-p="{{f}}"> 去完善资料 </u-button></view></view><view class="main-content data-v-970fed46"><view class="stats-card data-v-970fed46"><view class="stats-item data-v-970fed46"><text class="stats-number data-v-970fed46">{{g}}</text><text class="stats-label data-v-970fed46">参加考试</text></view><view class="stats-divider data-v-970fed46"></view><view class="stats-item data-v-970fed46"><text class="stats-number data-v-970fed46">{{h}}</text><text class="stats-label data-v-970fed46">通过考试</text></view><view class="stats-divider data-v-970fed46"></view><view class="stats-item data-v-970fed46"><text class="stats-number data-v-970fed46">{{i}}</text><text class="stats-label data-v-970fed46">待参加</text></view></view><view class="current-exams-section data-v-970fed46"><view class="section-header data-v-970fed46"><text class="section-title data-v-970fed46">本期考试</text><text class="section-desc data-v-970fed46">请及时参加考试，不要错过考试时间</text></view><loading-spinner wx:if="{{j}}" class="data-v-970fed46" u-i="970fed46-4,970fed46-1" bind:__l="__l" u-p="{{k}}"/><empty-state wx:elif="{{l}}" class="data-v-970fed46" u-i="970fed46-5,970fed46-1" bind:__l="__l" u-p="{{m}}"/><view wx:else class="exam-list data-v-970fed46"><view wx:for="{{n}}" wx:for-item="exam" wx:key="s" class="exam-card data-v-970fed46" bindtap="{{exam.t}}"><view class="{{['exam-type-tag', 'data-v-970fed46', exam.b]}}"><text class="data-v-970fed46">{{exam.a}}</text></view><view class="exam-info data-v-970fed46"><text class="exam-title data-v-970fed46">{{exam.c}}</text><text class="exam-desc data-v-970fed46">{{exam.d}}</text><view class="exam-meta data-v-970fed46"><view class="meta-item data-v-970fed46"><u-icon wx:if="{{o}}" class="data-v-970fed46" u-i="{{exam.e}}" bind:__l="__l" u-p="{{o}}"/><text class="data-v-970fed46">{{exam.f}}</text></view><view class="meta-item data-v-970fed46"><u-icon wx:if="{{p}}" class="data-v-970fed46" u-i="{{exam.g}}" bind:__l="__l" u-p="{{p}}"/><text class="data-v-970fed46">{{exam.h}}分钟</text></view><view wx:if="{{exam.i}}" class="meta-item data-v-970fed46"><u-icon wx:if="{{exam.k}}" class="data-v-970fed46" u-i="{{exam.j}}" bind:__l="__l" u-p="{{exam.k}}"/><text class="data-v-970fed46">{{exam.l}}</text></view></view></view><view class="exam-actions data-v-970fed46"><status-tag wx:if="{{exam.n}}" class="data-v-970fed46" u-i="{{exam.m}}" bind:__l="__l" u-p="{{exam.n}}"/><u-button wx:if="{{exam.r}}" u-s="{{['d']}}" class="action-btn data-v-970fed46" catchclick="{{exam.p}}" u-i="{{exam.q}}" bind:__l="__l" u-p="{{exam.r}}">{{exam.o}}</u-button></view></view></view></view><view class="history-section data-v-970fed46"><view class="history-card data-v-970fed46" bindtap="{{s}}"><view class="history-info data-v-970fed46"><u-icon wx:if="{{q}}" class="data-v-970fed46" u-i="970fed46-11,970fed46-1" bind:__l="__l" u-p="{{q}}"/><view class="history-content data-v-970fed46"><text class="history-title data-v-970fed46">历史考试记录</text><text class="history-desc data-v-970fed46">查看所有考试记录和成绩</text></view></view><u-icon wx:if="{{r}}" class="data-v-970fed46" u-i="970fed46-12,970fed46-1" bind:__l="__l" u-p="{{r}}"/></view></view></view></permission-wrapper></view>