{"version": 3, "file": "upload.js", "sources": ["uni_modules/uview-plus/components/u-upload/upload.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:09:50\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/upload.js\n */\nexport default {\n\t// upload组件\n\tupload: {\n\t\taccept: 'image',\n\t\textension: [],\n\t\tcapture: ['album', 'camera'],\n\t\tcompressed: true,\n\t\tcamera: 'back',\n\t\tmaxDuration: 60,\n\t\tuploadIcon: 'camera-fill',\n\t\tuploadIconColor: '#D3D4D6',\n\t\tuseBeforeRead: false,\n\t\tpreviewFullImage: true,\n\t\tmaxCount: 52,\n\t\tdisabled: false,\n\t\timageMode: 'aspectFill',\n\t\tname: '',\n\t\tsizeType: ['original', 'compressed'],\n\t\tmultiple: false,\n\t\tdeletable: true,\n\t\tmaxSize: Number.MAX_VALUE,\n\t\tfileList: [],\n\t\tuploadText: '',\n\t\twidth: 80,\n\t\theight: 80,\n\t\tpreviewImage: true,\n\t\tautoDelete: false,\n\t\tautoUpload: false,\n\t\tautoUploadApi: '',\n\t\tautoUploadAuthUrl: '',\n\t\tautoUploadDriver: '',\n\t\tautoUploadHeader: {},\n\t\tgetVideoThumb: false,\n\t\tcustomAfterAutoUpload: false\n\t}\n}\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEd,QAAQ;AAAA,IACP,QAAQ;AAAA,IACR,WAAW,CAAE;AAAA,IACb,SAAS,CAAC,SAAS,QAAQ;AAAA,IAC3B,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,aAAa;AAAA,IACb,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,eAAe;AAAA,IACf,kBAAkB;AAAA,IAClB,UAAU;AAAA,IACV,UAAU;AAAA,IACV,WAAW;AAAA,IACX,MAAM;AAAA,IACN,UAAU,CAAC,YAAY,YAAY;AAAA,IACnC,UAAU;AAAA,IACV,WAAW;AAAA,IACX,SAAS,OAAO;AAAA,IAChB,UAAU,CAAE;AAAA,IACZ,YAAY;AAAA,IACZ,OAAO;AAAA,IACP,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,mBAAmB;AAAA,IACnB,kBAAkB;AAAA,IAClB,kBAAkB,CAAE;AAAA,IACpB,eAAe;AAAA,IACf,uBAAuB;AAAA,EACvB;AACF;;"}