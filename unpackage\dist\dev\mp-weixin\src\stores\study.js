"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const common_vendor = require("../../common/vendor.js");
const src_constants_index = require("../constants/index.js");
const useStudyStore = common_vendor.defineStore("study", () => {
  const categories = common_vendor.ref([]);
  const currentSession = common_vendor.ref(null);
  const practiceHistory = common_vendor.ref([]);
  const dailyPracticeCount = common_vendor.ref(0);
  const lastPracticeDate = common_vendor.ref("");
  const canPracticeToday = common_vendor.computed(() => {
    const today = (/* @__PURE__ */ new Date()).toDateString();
    if (lastPracticeDate.value !== today) {
      return true;
    }
    return dailyPracticeCount.value < src_constants_index.PRACTICE_CONFIG.FREE_SESSIONS_PER_DAY;
  });
  const remainingPracticeCount = common_vendor.computed(() => {
    const today = (/* @__PURE__ */ new Date()).toDateString();
    if (lastPracticeDate.value !== today) {
      return src_constants_index.PRACTICE_CONFIG.FREE_SESSIONS_PER_DAY;
    }
    return Math.max(0, src_constants_index.PRACTICE_CONFIG.FREE_SESSIONS_PER_DAY - dailyPracticeCount.value);
  });
  const currentQuestionIndex = common_vendor.computed(() => {
    if (!currentSession.value)
      return 0;
    return Object.keys(currentSession.value.answers).length;
  });
  const currentQuestion = common_vendor.computed(() => {
    if (!currentSession.value || currentQuestionIndex.value >= currentSession.value.questions.length) {
      return null;
    }
    return currentSession.value.questions[currentQuestionIndex.value];
  });
  const isSessionCompleted = common_vendor.computed(() => {
    if (!currentSession.value)
      return false;
    return currentQuestionIndex.value >= currentSession.value.questions.length;
  });
  const setCategories = (newCategories) => {
    categories.value = newCategories;
  };
  const startPracticeSession = (categoryId, questions) => {
    const session = {
      id: Date.now().toString(),
      categoryId,
      questions,
      answers: {},
      totalCount: questions.length,
      startTime: (/* @__PURE__ */ new Date()).toISOString()
    };
    currentSession.value = session;
    saveCurrentSession();
    return session;
  };
  const answerQuestion = (questionId, answer) => {
    if (!currentSession.value)
      return;
    currentSession.value.answers[questionId] = answer;
    saveCurrentSession();
  };
  const completeSession = () => {
    if (!currentSession.value || !isSessionCompleted.value)
      return;
    let correctCount = 0;
    currentSession.value.questions.forEach((question) => {
      const userAnswer = currentSession.value.answers[question.id];
      if (isAnswerCorrect(question, userAnswer)) {
        correctCount++;
      }
    });
    currentSession.value.correctCount = correctCount;
    currentSession.value.score = Math.round(correctCount / currentSession.value.totalCount * 100);
    currentSession.value.endTime = (/* @__PURE__ */ new Date()).toISOString();
    practiceHistory.value.unshift(__spreadValues({}, currentSession.value));
    updateDailyPracticeCount();
    savePracticeHistory();
    saveCurrentSession();
    return currentSession.value;
  };
  const isAnswerCorrect = (question, userAnswer) => {
    if (!userAnswer)
      return false;
    switch (question.type) {
      case "single":
      case "judge":
        return userAnswer === question.answer;
      case "multiple":
        if (!Array.isArray(userAnswer) || !Array.isArray(question.answer))
          return false;
        return userAnswer.length === question.answer.length && userAnswer.every((ans) => question.answer.includes(ans));
      case "essay":
        return true;
      default:
        return false;
    }
  };
  const updateDailyPracticeCount = () => {
    const today = (/* @__PURE__ */ new Date()).toDateString();
    if (lastPracticeDate.value !== today) {
      dailyPracticeCount.value = 1;
      lastPracticeDate.value = today;
    } else {
      dailyPracticeCount.value++;
    }
    saveDailyPracticeData();
  };
  const clearCurrentSession = () => {
    currentSession.value = null;
    common_vendor.index.removeStorageSync(src_constants_index.STORAGE_KEYS.PRACTICE_CACHE);
  };
  const saveCurrentSession = () => {
    if (currentSession.value) {
      try {
        common_vendor.index.setStorageSync(src_constants_index.STORAGE_KEYS.PRACTICE_CACHE, JSON.stringify(currentSession.value));
      } catch (error) {
        common_vendor.index.__f__("error", "at src/stores/study.ts:148", "Failed to save current session:", error);
      }
    }
  };
  const loadCurrentSession = () => {
    try {
      const cached = common_vendor.index.getStorageSync(src_constants_index.STORAGE_KEYS.PRACTICE_CACHE);
      if (cached) {
        currentSession.value = JSON.parse(cached);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at src/stores/study.ts:160", "Failed to load current session:", error);
    }
  };
  const savePracticeHistory = () => {
    try {
      const historyToSave = practiceHistory.value.slice(0, 50);
      common_vendor.index.setStorageSync("practice_history", JSON.stringify(historyToSave));
    } catch (error) {
      common_vendor.index.__f__("error", "at src/stores/study.ts:170", "Failed to save practice history:", error);
    }
  };
  const loadPracticeHistory = () => {
    try {
      const history = common_vendor.index.getStorageSync("practice_history");
      if (history) {
        practiceHistory.value = JSON.parse(history);
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at src/stores/study.ts:181", "Failed to load practice history:", error);
    }
  };
  const saveDailyPracticeData = () => {
    try {
      const data = {
        count: dailyPracticeCount.value,
        date: lastPracticeDate.value
      };
      common_vendor.index.setStorageSync("daily_practice", JSON.stringify(data));
    } catch (error) {
      common_vendor.index.__f__("error", "at src/stores/study.ts:193", "Failed to save daily practice data:", error);
    }
  };
  const loadDailyPracticeData = () => {
    try {
      const data = common_vendor.index.getStorageSync("daily_practice");
      if (data) {
        const parsed = JSON.parse(data);
        dailyPracticeCount.value = parsed.count || 0;
        lastPracticeDate.value = parsed.date || "";
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at src/stores/study.ts:206", "Failed to load daily practice data:", error);
    }
  };
  const initStudyStore = () => {
    loadCurrentSession();
    loadPracticeHistory();
    loadDailyPracticeData();
  };
  const getSessionStats = () => {
    const totalSessions = practiceHistory.value.length;
    const totalQuestions = practiceHistory.value.reduce((sum, session) => sum + session.totalCount, 0);
    const totalCorrect = practiceHistory.value.reduce((sum, session) => sum + (session.correctCount || 0), 0);
    const averageScore = totalSessions > 0 ? practiceHistory.value.reduce((sum, session) => sum + (session.score || 0), 0) / totalSessions : 0;
    return {
      totalSessions,
      totalQuestions,
      totalCorrect,
      averageScore: Math.round(averageScore),
      accuracy: totalQuestions > 0 ? Math.round(totalCorrect / totalQuestions * 100) : 0
    };
  };
  return {
    // 状态
    categories,
    currentSession,
    practiceHistory,
    dailyPracticeCount,
    lastPracticeDate,
    // 计算属性
    canPracticeToday,
    remainingPracticeCount,
    currentQuestionIndex,
    currentQuestion,
    isSessionCompleted,
    // 方法
    setCategories,
    startPracticeSession,
    answerQuestion,
    completeSession,
    isAnswerCorrect,
    updateDailyPracticeCount,
    clearCurrentSession,
    saveCurrentSession,
    loadCurrentSession,
    savePracticeHistory,
    loadPracticeHistory,
    saveDailyPracticeData,
    loadDailyPracticeData,
    initStudyStore,
    getSessionStats
  };
});
exports.useStudyStore = useStudyStore;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/src/stores/study.js.map
