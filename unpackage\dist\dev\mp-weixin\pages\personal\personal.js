"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_user = require("../../src/stores/user.js");
const src_stores_app = require("../../src/stores/app.js");
const src_stores_study = require("../../src/stores/study.js");
const src_utils_index = require("../../src/utils/index.js");
const src_constants_index = require("../../src/constants/index.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  _easycom_u_icon2();
}
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
if (!Math) {
  (_easycom_u_icon + UserStatusBanner)();
}
const UserStatusBanner = () => "../../src/components/common/UserStatusBanner.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "personal",
  setup(__props) {
    const userStore = src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const studyStore = src_stores_study.useStudyStore();
    const statusBarHeight = common_vendor.ref(0);
    const studyStats = common_vendor.ref({
      totalSessions: 0,
      totalQuestions: 0,
      accuracy: 0
    });
    const examStats = common_vendor.ref({
      total: 0,
      passed: 0,
      pending: 0
    });
    const certificateStats = common_vendor.ref({
      total: 0,
      pending: 0,
      active: 0
    });
    const userInfo = common_vendor.computed(() => userStore.userInfo);
    common_vendor.onMounted(() => {
      try {
        if (common_vendor.index.canIUse("getWindowInfo")) {
          const windowInfo = common_vendor.index.getWindowInfo();
          statusBarHeight.value = windowInfo.statusBarHeight || 0;
        } else {
          const systemInfo = common_vendor.index.getSystemInfoSync();
          statusBarHeight.value = systemInfo.statusBarHeight || 0;
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/personal/personal.vue:223", "获取系统信息失败:", error);
        statusBarHeight.value = 0;
      }
      loadStats();
    });
    common_vendor.onShow(() => {
      loadStats();
    });
    const loadStats = () => {
      studyStats.value = studyStore.getSessionStats();
      examStats.value = {
        total: 12,
        passed: 10,
        pending: 2
      };
      certificateStats.value = {
        total: 8,
        pending: 1,
        active: 7
      };
    };
    const getUserStatusText = () => {
      var _a;
      if (!((_a = userInfo.value) == null ? void 0 : _a.status))
        return "";
      return src_constants_index.USER_STATUS_TEXT[userInfo.value.status] || userInfo.value.status;
    };
    const viewAvatar = () => {
      var _a;
      if ((_a = userInfo.value) == null ? void 0 : _a.photo) {
        common_vendor.index.previewImage({
          urls: [userInfo.value.photo],
          current: userInfo.value.photo
        });
      }
    };
    const editProfile = () => {
      var _a, _b;
      if (((_a = userInfo.value) == null ? void 0 : _a.status) === "not_submitted") {
        appStore.redirectTo(src_constants_index.PAGE_PATHS.PROFILE);
      } else if (((_b = userInfo.value) == null ? void 0 : _b.status) === "rejected") {
        appStore.redirectTo(src_constants_index.PAGE_PATHS.PROFILE);
      } else {
        appStore.navigateTo(src_constants_index.PAGE_PATHS.PERSONAL_INFO);
      }
    };
    const viewPersonalInfo = () => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.PERSONAL_INFO);
    };
    const viewCertificates = () => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.PERSONAL_CERTIFICATE);
    };
    const viewStudyStats = () => {
      appStore.showToast("学习统计功能开发中");
    };
    const viewExamStats = () => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.EXAM_HISTORY);
    };
    const viewStudyHistory = () => {
      appStore.showToast("学习历史功能开发中");
    };
    const viewExamHistory = () => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.EXAM_HISTORY);
    };
    const submitFeedback = () => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.PERSONAL_FEEDBACK);
    };
    const viewAbout = () => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.PERSONAL_ABOUT);
    };
    const logout = () => {
      appStore.showModal({
        title: "确认退出",
        content: "确定要退出登录吗？",
        confirmText: "确认退出",
        cancelText: "取消"
      }).then((confirmed) => {
        if (confirmed) {
          userStore.logout();
          appStore.reLaunch(src_constants_index.PAGE_PATHS.LOGIN);
        }
      });
    };
    return (_ctx, _cache) => {
      var _a, _b, _c, _d, _e, _f;
      return common_vendor.e({
        a: statusBarHeight.value + "px",
        b: ((_a = userInfo.value) == null ? void 0 : _a.photo) || "/static/images/default-avatar.png",
        c: common_vendor.o(viewAvatar),
        d: (_b = userInfo.value) == null ? void 0 : _b.status
      }, ((_c = userInfo.value) == null ? void 0 : _c.status) ? {
        e: common_vendor.t(getUserStatusText()),
        f: common_vendor.n(userInfo.value.status)
      } : {}, {
        g: common_vendor.t(((_d = userInfo.value) == null ? void 0 : _d.realName) || "未设置"),
        h: common_vendor.t(((_e = userInfo.value) == null ? void 0 : _e.organization) || "未设置机构"),
        i: common_vendor.t(common_vendor.unref(src_utils_index.formatDate)((_f = userInfo.value) == null ? void 0 : _f.createdAt, "YYYY-MM-DD")),
        j: common_vendor.o(editProfile),
        k: common_vendor.p({
          name: "edit-pen",
          color: "#fff",
          size: "32"
        }),
        l: common_vendor.p({
          showAction: true
        }),
        m: common_vendor.t(studyStats.value.totalSessions),
        n: common_vendor.o(viewStudyStats),
        o: common_vendor.t(examStats.value.total),
        p: common_vendor.o(viewExamStats),
        q: common_vendor.t(certificateStats.value.total),
        r: common_vendor.o(viewCertificates),
        s: common_vendor.p({
          name: "account",
          color: "#4A90E2",
          size: "40"
        }),
        t: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "32"
        }),
        v: common_vendor.o(viewPersonalInfo),
        w: common_vendor.p({
          name: "medal",
          color: "#FFD700",
          size: "40"
        }),
        x: certificateStats.value.pending > 0
      }, certificateStats.value.pending > 0 ? {
        y: common_vendor.t(certificateStats.value.pending)
      } : {}, {
        z: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "32"
        }),
        A: common_vendor.o(viewCertificates),
        B: common_vendor.p({
          name: "book",
          color: "#4CAF50",
          size: "40"
        }),
        C: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "32"
        }),
        D: common_vendor.o(viewStudyHistory),
        E: common_vendor.p({
          name: "file-text",
          color: "#FF9500",
          size: "40"
        }),
        F: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "32"
        }),
        G: common_vendor.o(viewExamHistory),
        H: common_vendor.p({
          name: "chat",
          color: "#9C27B0",
          size: "40"
        }),
        I: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "32"
        }),
        J: common_vendor.o(submitFeedback),
        K: common_vendor.p({
          name: "info-circle",
          color: "#607D8B",
          size: "40"
        }),
        L: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "32"
        }),
        M: common_vendor.o(viewAbout),
        N: common_vendor.p({
          name: "logout",
          color: "#f56c6c",
          size: "40"
        }),
        O: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "32"
        }),
        P: common_vendor.o(logout)
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6ae23533"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/personal/personal.js.map
