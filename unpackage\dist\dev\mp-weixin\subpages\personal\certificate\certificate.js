"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
const src_stores_user = require("../../../src/stores/user.js");
const src_stores_app = require("../../../src/stores/app.js");
const src_utils_index = require("../../../src/utils/index.js");
const src_api_index = require("../../../src/api/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  (_easycom_u_navbar2 + _easycom_u_icon2 + _easycom_u_button2 + _easycom_u_popup2)();
}
const _easycom_u_navbar = () => "../../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_u_popup = () => "../../../uni_modules/uview-plus/components/u-popup/u-popup.js";
if (!Math) {
  (_easycom_u_navbar + LoadingSpinner + EmptyState + _easycom_u_icon + StatusTag + _easycom_u_button + _easycom_u_popup)();
}
const LoadingSpinner = () => "../../../src/components/common/LoadingSpinner.js";
const EmptyState = () => "../../../src/components/common/EmptyState.js";
const StatusTag = () => "../../../src/components/common/StatusTag.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "certificate",
  setup(__props) {
    const userStore = src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const isLoading = common_vendor.ref(true);
    const certificates = common_vendor.ref([]);
    const currentFilter = common_vendor.ref("all");
    const showCertificateDetail = common_vendor.ref(false);
    const selectedCertificate = common_vendor.ref(null);
    const filterOptions = [
      { key: "all", label: "全部" },
      { key: "active", label: "有效" },
      { key: "pending", label: "待发放" },
      { key: "expired", label: "已过期" }
    ];
    const certificateStats = common_vendor.ref({
      total: 0,
      active: 0,
      pending: 0,
      expired: 0
    });
    const userInfo = common_vendor.computed(() => userStore.userInfo);
    const filteredCertificates = common_vendor.computed(() => {
      let filtered = certificates.value;
      switch (currentFilter.value) {
        case "active":
          filtered = filtered.filter((cert) => cert.status === "active");
          break;
        case "pending":
          filtered = filtered.filter((cert) => cert.status === "pending");
          break;
        case "expired":
          filtered = filtered.filter((cert) => cert.status === "expired");
          break;
      }
      return filtered;
    });
    common_vendor.onMounted(() => {
      loadCertificates();
    });
    const loadCertificates = () => __async(this, null, function* () {
      isLoading.value = true;
      try {
        const response = yield src_api_index.api.certificate.getCertificates();
        certificates.value = response.data;
        certificateStats.value = {
          total: certificates.value.length,
          active: certificates.value.filter((cert) => cert.status === "active").length,
          pending: certificates.value.filter((cert) => cert.status === "pending").length,
          expired: certificates.value.filter((cert) => cert.status === "expired").length
        };
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/personal/certificate/certificate.vue:294", "加载证书列表失败:", error);
        appStore.showToast(error.message || "加载失败");
      } finally {
        isLoading.value = false;
      }
    });
    const getExpiryClass = (expiresAt) => {
      const expiryDate = new Date(expiresAt);
      const now = /* @__PURE__ */ new Date();
      const daysUntilExpiry = Math.ceil((expiryDate.getTime() - now.getTime()) / (1e3 * 60 * 60 * 24));
      if (daysUntilExpiry < 0)
        return "expired";
      if (daysUntilExpiry <= 30)
        return "expiring-soon";
      return "normal";
    };
    const viewCertificate = (certificate) => {
      selectedCertificate.value = certificate;
      showCertificateDetail.value = true;
    };
    const downloadCertificate = (certificate) => __async(this, null, function* () {
      try {
        appStore.showLoading("准备下载...");
        const response = yield src_api_index.api.certificate.downloadCertificate(certificate.id);
        common_vendor.index.downloadFile({
          url: response.data.downloadUrl,
          success: (res) => {
            if (res.statusCode === 200) {
              common_vendor.index.saveFile({
                tempFilePath: res.tempFilePath,
                success: () => {
                  appStore.showToast("证书下载成功", "success");
                },
                fail: () => {
                  appStore.showToast("保存失败");
                }
              });
            }
          },
          fail: () => {
            appStore.showToast("下载失败");
          },
          complete: () => {
            appStore.hideLoading();
          }
        });
      } catch (error) {
        appStore.hideLoading();
        common_vendor.index.__f__("error", "at subpages/personal/certificate/certificate.vue:351", "下载证书失败:", error);
        appStore.showToast(error.message || "下载失败");
      }
    });
    const shareCertificate = (certificate) => {
      appStore.showToast("分享功能开发中");
    };
    return (_ctx, _cache) => {
      var _a;
      return common_vendor.e({
        a: common_vendor.p({
          title: "我的证书",
          autoBack: true,
          background: {
            background: "linear-gradient(135deg, #2E8B57 0%, #228B22 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: common_vendor.t(certificateStats.value.total),
        c: common_vendor.t(certificateStats.value.active),
        d: common_vendor.t(certificateStats.value.pending),
        e: common_vendor.f(filterOptions, (filter, k0, i0) => {
          return {
            a: common_vendor.t(filter.label),
            b: filter.key,
            c: currentFilter.value === filter.key ? 1 : "",
            d: common_vendor.o(($event) => currentFilter.value = filter.key, filter.key)
          };
        }),
        f: isLoading.value
      }, isLoading.value ? {
        g: common_vendor.p({
          text: "加载证书信息..."
        })
      } : filteredCertificates.value.length === 0 ? {
        i: common_vendor.p({
          type: "no-data",
          title: "暂无证书",
          description: "完成考试后可获得相应证书",
          showButton: false
        })
      } : {
        j: common_vendor.f(filteredCertificates.value, (certificate, k0, i0) => {
          return common_vendor.e({
            a: "8bbadf6f-3-" + i0,
            b: common_vendor.t(certificate.name),
            c: common_vendor.t(certificate.type),
            d: "8bbadf6f-4-" + i0,
            e: common_vendor.p({
              type: "certificate",
              status: certificate.status
            }),
            f: "8bbadf6f-5-" + i0,
            g: common_vendor.t(common_vendor.unref(src_utils_index.formatDate)(certificate.issuedAt, "YYYY-MM-DD")),
            h: certificate.expiresAt
          }, certificate.expiresAt ? {
            i: "8bbadf6f-6-" + i0,
            j: common_vendor.p({
              name: "clock",
              color: "#666",
              size: "24"
            }),
            k: common_vendor.t(common_vendor.unref(src_utils_index.formatDate)(certificate.expiresAt, "YYYY-MM-DD")),
            l: common_vendor.n(getExpiryClass(certificate.expiresAt))
          } : {}, {
            m: "8bbadf6f-7-" + i0,
            n: common_vendor.t(certificate.examScore),
            o: common_vendor.o(($event) => viewCertificate(certificate), certificate.id),
            p: "8bbadf6f-8-" + i0,
            q: certificate.status === "active"
          }, certificate.status === "active" ? {
            r: common_vendor.o(($event) => downloadCertificate(certificate), certificate.id),
            s: "8bbadf6f-9-" + i0,
            t: common_vendor.p({
              type: "primary",
              size: "small"
            })
          } : {}, {
            v: certificate.status === "active"
          }, certificate.status === "active" ? {
            w: common_vendor.o(($event) => shareCertificate(), certificate.id),
            x: "8bbadf6f-10-" + i0,
            y: common_vendor.p({
              type: "success",
              size: "small"
            })
          } : {}, {
            z: certificate.id,
            A: common_vendor.o(($event) => viewCertificate(certificate), certificate.id)
          });
        }),
        k: common_vendor.p({
          name: "medal",
          color: "#FFD700",
          size: "60"
        }),
        l: common_vendor.p({
          name: "calendar",
          color: "#666",
          size: "24"
        }),
        m: common_vendor.p({
          name: "checkmark-circle",
          color: "#666",
          size: "24"
        }),
        n: common_vendor.p({
          type: "info",
          size: "small",
          plain: true
        })
      }, {
        h: filteredCertificates.value.length === 0,
        o: selectedCertificate.value
      }, selectedCertificate.value ? {
        p: common_vendor.o(($event) => showCertificateDetail.value = false),
        q: common_vendor.p({
          name: "close",
          size: "32"
        }),
        r: common_vendor.t(selectedCertificate.value.name),
        s: common_vendor.t((_a = userInfo.value) == null ? void 0 : _a.realName),
        t: common_vendor.t(selectedCertificate.value.description),
        v: common_vendor.t(selectedCertificate.value.issuer),
        w: common_vendor.t(common_vendor.unref(src_utils_index.formatDate)(selectedCertificate.value.issuedAt, "YYYY年MM月DD日")),
        x: common_vendor.t(selectedCertificate.value.certificateNumber),
        y: common_vendor.t(selectedCertificate.value.type),
        z: common_vendor.t(selectedCertificate.value.examName),
        A: common_vendor.t(selectedCertificate.value.examScore),
        B: common_vendor.p({
          type: "certificate",
          status: selectedCertificate.value.status
        }),
        C: common_vendor.o(($event) => downloadCertificate(selectedCertificate.value)),
        D: common_vendor.p({
          type: "primary"
        })
      } : {}, {
        E: common_vendor.o(($event) => showCertificateDetail.value = $event),
        F: common_vendor.p({
          mode: "center",
          width: "90%",
          height: "80%",
          closeOnClickOverlay: true,
          modelValue: showCertificateDetail.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8bbadf6f"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpages/personal/certificate/certificate.js.map
