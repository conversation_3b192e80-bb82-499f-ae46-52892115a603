{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-form/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 当前form的需要验证字段的集合\n        model: {\n            type: Object,\n            default: () => defProps.form.model\n        },\n        // 验证规则\n        rules: {\n            type: [Object, Function, Array],\n            default: () => defProps.form.rules\n        },\n        // 有错误时的提示方式，message-提示信息，toast-进行toast提示\n        // border-bottom-下边框呈现红色，none-无提示\n        errorType: {\n            type: String,\n            default: () => defProps.form.errorType\n        },\n        // 是否显示表单域的下划线边框\n        borderBottom: {\n            type: Boolean,\n            default: () => defProps.form.borderBottom\n        },\n        // label的位置，left-左边，top-上边\n        labelPosition: {\n            type: String,\n            default: () => defProps.form.labelPosition\n        },\n        // label的宽度，单位px\n        labelWidth: {\n            type: [String, Number],\n            default: () => defProps.form.labelWidth\n        },\n        // lable字体的对齐方式\n        labelAlign: {\n            type: String,\n            default: () => defProps.form.labelAlign\n        },\n        // lable的样式，对象形式\n        labelStyle: {\n            type: Object,\n            default: () => defProps.form.labelStyle\n        }\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM,CAAC,QAAQ,UAAU,KAAK;AAAA,MAC9B,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA;AAAA,IAGD,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA;AAAA,IAED,YAAY;AAAA,MACR,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,KAAK;AAAA,IAChC;AAAA,EACJ;AACL,CAAC;;"}