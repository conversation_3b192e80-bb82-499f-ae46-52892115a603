"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const src_stores_user = require("../../src/stores/user.js");
const src_stores_app = require("../../src/stores/app.js");
const src_api_index = require("../../src/api/index.js");
const src_constants_index = require("../../src/constants/index.js");
if (!Array) {
  const _easycom_u_checkbox2 = common_vendor.resolveComponent("u-checkbox");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  (_easycom_u_checkbox2 + _easycom_u_icon2 + _easycom_u_button2 + _easycom_u_popup2)();
}
const _easycom_u_checkbox = () => "../../uni_modules/uview-plus/components/u-checkbox/u-checkbox.js";
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_u_popup = () => "../../uni_modules/uview-plus/components/u-popup/u-popup.js";
if (!Math) {
  (_easycom_u_checkbox + _easycom_u_icon + _easycom_u_button + _easycom_u_popup)();
}
const userAgreementContent = `1. 服务条款
本服务条款是您与疾控医护考试系统之间的协议，规定了您使用本平台服务的权利和义务。

2. 用户义务
用户承诺提供真实、准确的个人信息，不得冒用他人身份或提供虚假信息。

3. 隐私保护
我们重视您的隐私保护，严格按照相关法律法规保护您的个人信息安全。

4. 服务内容
本平台提供疾控医护人员任职资格考试相关的学习、练习、考试等服务。

5. 免责声明
在法律允许的范围内，本平台对服务中断、数据丢失等情况不承担责任。`;
const privacyPolicyContent = `1. 信息收集
我们会收集您在使用服务时主动提供的信息，包括但不限于姓名、手机号、身份证号等。

2. 信息使用
收集的信息将用于提供更好的服务，包括身份验证、考试管理、证书颁发等。

3. 信息保护
我们采用行业标准的安全措施保护您的信息，不会向第三方泄露您的个人信息。

4. 信息存储
您的个人信息将存储在安全的服务器中，并采用加密技术保护。

5. 权利保障
您有权查看、修改、删除您的个人信息，如有需要请联系客服。`;
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "login",
  setup(__props) {
    const userStore = src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const agreedToTerms = common_vendor.ref(false);
    const isLogging = common_vendor.ref(false);
    const showAgreementModal = common_vendor.ref(false);
    const currentAgreementTitle = common_vendor.ref("");
    const currentAgreementContent = common_vendor.ref("");
    const showUserAgreement = () => {
      currentAgreementTitle.value = "用户服务协议";
      currentAgreementContent.value = userAgreementContent;
      showAgreementModal.value = true;
    };
    const showPrivacyPolicy = () => {
      currentAgreementTitle.value = "隐私政策";
      currentAgreementContent.value = privacyPolicyContent;
      showAgreementModal.value = true;
    };
    const handleWeChatLogin = () => __async(this, null, function* () {
      if (!agreedToTerms.value) {
        appStore.showToast("请先同意用户协议");
        return;
      }
      isLogging.value = true;
      try {
        const loginResult = yield common_vendor.index.login();
        if (loginResult[1].code) {
          const userProfile = yield common_vendor.index.getUserProfile({
            desc: "用于完善用户资料"
          });
          const response = yield src_api_index.api.user.wxLogin({
            code: loginResult[1].code,
            userInfo: userProfile[1].userInfo
          });
          userStore.login(response.data.token, response.data.user);
          if (response.data.user.status === "not_submitted") {
            appStore.redirectTo(src_constants_index.PAGE_PATHS.PROFILE);
          } else {
            appStore.switchTab(src_constants_index.PAGE_PATHS.INFO);
          }
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/login/login.vue:191", "登录失败:", error);
        if (error.errMsg && error.errMsg.includes("getUserProfile:fail")) {
          appStore.showToast("需要授权后才能登录");
        } else {
          appStore.showToast(error.message || "登录失败，请重试");
        }
      } finally {
        isLogging.value = false;
      }
    });
    common_vendor.onMounted(() => {
      if (userStore.isLoggedIn) {
        if (userStore.isAuthenticated) {
          appStore.switchTab(src_constants_index.PAGE_PATHS.INFO);
        } else {
          appStore.redirectTo(src_constants_index.PAGE_PATHS.PROFILE);
        }
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0,
        b: common_vendor.o(($event) => agreedToTerms.value = $event),
        c: common_vendor.p({
          customStyle: {
            marginRight: "12rpx"
          },
          activeColor: "#2E8B57",
          modelValue: agreedToTerms.value
        }),
        d: common_vendor.o(showUserAgreement),
        e: common_vendor.o(showPrivacyPolicy),
        f: common_vendor.p({
          name: "weixin-fill",
          color: "#fff",
          size: "32"
        }),
        g: common_vendor.o(handleWeChatLogin),
        h: common_vendor.p({
          type: "primary",
          disabled: !agreedToTerms.value || isLogging.value,
          loading: isLogging.value,
          loadingText: "登录中..."
        }),
        i: common_vendor.p({
          name: "info-circle",
          color: "#999",
          size: "24"
        }),
        j: common_vendor.t(currentAgreementTitle.value),
        k: common_vendor.o(($event) => showAgreementModal.value = false),
        l: common_vendor.p({
          name: "close",
          size: "32"
        }),
        m: common_vendor.t(currentAgreementContent.value),
        n: common_vendor.o(($event) => showAgreementModal.value = false),
        o: common_vendor.p({
          type: "primary"
        }),
        p: common_vendor.o(($event) => showAgreementModal.value = $event),
        q: common_vendor.p({
          mode: "center",
          width: "640rpx",
          height: "800rpx",
          closeOnClickOverlay: true,
          modelValue: showAgreementModal.value
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-e4e4508d"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/login/login.js.map
