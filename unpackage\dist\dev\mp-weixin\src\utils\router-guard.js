"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const src_utils_permission = require("./permission.js");
const src_constants_index = require("../constants/index.js");
const routeGuardConfig = {
  authRequired: [
    src_constants_index.PAGE_PATHS.INFO,
    src_constants_index.PAGE_PATHS.STUDY,
    src_constants_index.PAGE_PATHS.EXAM,
    src_constants_index.PAGE_PATHS.PERSONAL,
    // 分包页面
    src_constants_index.PAGE_PATHS.EXAM_ONLINE_READING,
    src_constants_index.PAGE_PATHS.EXAM_ONLINE_FACE_VERIFY,
    src_constants_index.PAGE_PATHS.EXAM_ONLINE_ANSWER,
    src_constants_index.PAGE_PATHS.EXAM_OFFLINE_DETAIL,
    src_constants_index.PAGE_PATHS.EXAM_HISTORY,
    src_constants_index.PAGE_PATHS.PERSONAL_INFO,
    src_constants_index.PAGE_PATHS.PERSONAL_CERTIFICATE,
    src_constants_index.PAGE_PATHS.PERSONAL_FEEDBACK,
    src_constants_index.PAGE_PATHS.PERSONAL_ABOUT
  ],
  authenticationRequired: [
    src_constants_index.PAGE_PATHS.EXAM,
    src_constants_index.PAGE_PATHS.EXAM_ONLINE_READING,
    src_constants_index.PAGE_PATHS.EXAM_ONLINE_FACE_VERIFY,
    src_constants_index.PAGE_PATHS.EXAM_ONLINE_ANSWER,
    src_constants_index.PAGE_PATHS.EXAM_OFFLINE_DETAIL,
    src_constants_index.PAGE_PATHS.EXAM_HISTORY,
    src_constants_index.PAGE_PATHS.PERSONAL_CERTIFICATE
  ],
  publicPages: [
    src_constants_index.PAGE_PATHS.LOGIN,
    src_constants_index.PAGE_PATHS.PROFILE
  ],
  defaultRedirect: src_constants_index.PAGE_PATHS.INFO
};
class RouterGuard {
  constructor(config) {
    this.config = config;
  }
  /**
   * 检查页面访问权限
   */
  checkAccess(url) {
    return __async(this, null, function* () {
      const pagePath = url.split("?")[0];
      if (this.config.publicPages.includes(pagePath)) {
        return { allowed: true };
      }
      if (this.config.authRequired.includes(pagePath)) {
        const permissionManager = src_utils_permission.getPermissionManager();
        if (!permissionManager.isLoggedIn()) {
          return {
            allowed: false,
            redirectTo: src_constants_index.PAGE_PATHS.LOGIN,
            message: "请先登录"
          };
        }
      }
      if (this.config.authenticationRequired.includes(pagePath)) {
        const permissionManager = src_utils_permission.getPermissionManager();
        if (!permissionManager.isAuthenticated()) {
          const userStatus = permissionManager.getUserStatus();
          if (userStatus === "not_submitted") {
            return {
              allowed: false,
              redirectTo: src_constants_index.PAGE_PATHS.PROFILE,
              message: "请先完善个人资料"
            };
          } else if (userStatus === "pending") {
            return {
              allowed: false,
              redirectTo: src_constants_index.PAGE_PATHS.PERSONAL,
              message: "个人资料审核中，暂时无法使用此功能"
            };
          } else if (userStatus === "rejected") {
            return {
              allowed: false,
              redirectTo: src_constants_index.PAGE_PATHS.PROFILE,
              message: "资料审核未通过，请重新提交"
            };
          } else {
            return {
              allowed: false,
              redirectTo: src_constants_index.PAGE_PATHS.PERSONAL,
              message: "请先完善个人资料并通过审核"
            };
          }
        }
      }
      return { allowed: true };
    });
  }
  /**
   * 执行路由守卫
   */
  guard(url) {
    return __async(this, null, function* () {
      const result = yield this.checkAccess(url);
      if (!result.allowed) {
        if (result.message) {
          common_vendor.index.showToast({
            title: result.message,
            icon: "none",
            duration: 2e3
          });
        }
        if (result.redirectTo) {
          setTimeout(() => {
            if (result.redirectTo === src_constants_index.PAGE_PATHS.LOGIN) {
              common_vendor.index.reLaunch({
                url: result.redirectTo
              });
            } else if (this.config.authRequired.includes(result.redirectTo)) {
              common_vendor.index.switchTab({
                url: result.redirectTo
              });
            } else {
              common_vendor.index.redirectTo({
                url: result.redirectTo
              });
            }
          }, 1500);
        }
        return false;
      }
      return true;
    });
  }
  /**
   * 获取默认首页
   */
  getDefaultPage() {
    const permissionManager = src_utils_permission.getPermissionManager();
    if (!permissionManager.isLoggedIn()) {
      return src_constants_index.PAGE_PATHS.LOGIN;
    }
    const userStatus = permissionManager.getUserStatus();
    if (userStatus === "not_submitted") {
      return src_constants_index.PAGE_PATHS.PROFILE;
    }
    return this.config.defaultRedirect;
  }
}
const routerGuard = new RouterGuard(routeGuardConfig);
function interceptNavigation() {
  const originalNavigateTo = common_vendor.index.navigateTo;
  common_vendor.index.navigateTo = function(options) {
    routerGuard.guard(options.url).then((allowed) => {
      if (allowed) {
        originalNavigateTo.call(common_vendor.index, options);
      }
    });
  };
  const originalRedirectTo = common_vendor.index.redirectTo;
  common_vendor.index.redirectTo = function(options) {
    routerGuard.guard(options.url).then((allowed) => {
      if (allowed) {
        originalRedirectTo.call(common_vendor.index, options);
      }
    });
  };
  const originalSwitchTab = common_vendor.index.switchTab;
  common_vendor.index.switchTab = function(options) {
    routerGuard.guard(options.url).then((allowed) => {
      if (allowed) {
        originalSwitchTab.call(common_vendor.index, options);
      }
    });
  };
}
function initRouterGuard() {
  interceptNavigation();
  common_vendor.index.__f__("log", "at src/utils/router-guard.ts:249", "Router guard initialized");
}
exports.initRouterGuard = initRouterGuard;
//# sourceMappingURL=../../../.sourcemap/mp-weixin/src/utils/router-guard.js.map
