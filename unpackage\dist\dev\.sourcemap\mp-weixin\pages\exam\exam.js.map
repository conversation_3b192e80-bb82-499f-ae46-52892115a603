{"version": 3, "file": "exam.js", "sources": ["pages/exam/exam.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZXhhbS9leGFtLnZ1ZQ"], "sourcesContent": ["<template>\n  <view class=\"exam-center-container\">\n    <!-- 状态栏安全区域 -->\n    <view class=\"status-bar\" :style=\"{ height: statusBarHeight + 'px' }\"></view>\n    \n    <!-- 自定义头部 -->\n    <view class=\"header\">\n      <view class=\"header-content\">\n        <text class=\"page-title\">考试中心</text>\n        <view class=\"header-actions\">\n          <u-icon name=\"calendar\" color=\"#fff\" size=\"44\" @click=\"viewCalendar\" />\n        </view>\n      </view>\n    </view>\n    \n    <!-- 权限控制包装器 -->\n    <PermissionWrapper permission=\"authenticated\" :showFallback=\"true\">\n      <!-- 非认证用户提示 -->\n      <template #fallback>\n        <view class=\"auth-prompt\">\n          <view class=\"prompt-card\">\n            <u-icon name=\"lock\" color=\"#f56c6c\" size=\"80\" />\n            <text class=\"prompt-title\">未认证，无法考试</text>\n            <text class=\"prompt-desc\">请先完善个人资料并通过机构审核，才能参加考试</text>\n            <u-button \n              class=\"auth-btn\" \n              type=\"primary\" \n              size=\"medium\"\n              @click=\"goToAuth\"\n            >\n              去完善资料\n            </u-button>\n          </view>\n        </view>\n      </template>\n      \n      <!-- 认证用户考试内容 -->\n      <view class=\"main-content\">\n        <!-- 考试统计卡片 -->\n        <view class=\"stats-card\">\n          <view class=\"stats-item\">\n            <text class=\"stats-number\">{{ examStats.total }}</text>\n            <text class=\"stats-label\">参加考试</text>\n          </view>\n          <view class=\"stats-divider\"></view>\n          <view class=\"stats-item\">\n            <text class=\"stats-number\">{{ examStats.passed }}</text>\n            <text class=\"stats-label\">通过考试</text>\n          </view>\n          <view class=\"stats-divider\"></view>\n          <view class=\"stats-item\">\n            <text class=\"stats-number\">{{ examStats.pending }}</text>\n            <text class=\"stats-label\">待参加</text>\n          </view>\n        </view>\n        \n        <!-- 本期考试 -->\n        <view class=\"current-exams-section\">\n          <view class=\"section-header\">\n            <text class=\"section-title\">本期考试</text>\n            <text class=\"section-desc\">请及时参加考试，不要错过考试时间</text>\n          </view>\n          \n          <!-- 加载状态 -->\n          <LoadingSpinner v-if=\"isLoading\" text=\"加载考试信息...\" />\n          \n          <!-- 空状态 -->\n          <EmptyState \n            v-else-if=\"currentExams.length === 0\"\n            type=\"no-data\"\n            title=\"暂无待参加的考试\"\n            description=\"当前没有可参加的考试，请关注最新考试通知\"\n            :showButton=\"false\"\n          />\n          \n          <!-- 考试列表 -->\n          <view v-else class=\"exam-list\">\n            <view \n              v-for=\"exam in currentExams\" \n              :key=\"exam.id\"\n              class=\"exam-card\"\n              @click=\"handleExamClick(exam)\"\n            >\n              <!-- 考试类型标签 -->\n              <view class=\"exam-type-tag\" :class=\"exam.type\">\n                <text>{{ exam.type === 'online' ? '线上考试' : '线下考试' }}</text>\n              </view>\n              \n              <!-- 考试信息 -->\n              <view class=\"exam-info\">\n                <text class=\"exam-title\">{{ exam.name }}</text>\n                <text class=\"exam-desc\">{{ exam.description }}</text>\n                \n                <view class=\"exam-meta\">\n                  <view class=\"meta-item\">\n                    <u-icon name=\"clock\" color=\"#666\" size=\"24\" />\n                    <text>{{ formatExamTime(exam) }}</text>\n                  </view>\n                  <view class=\"meta-item\">\n                    <u-icon name=\"time\" color=\"#666\" size=\"24\" />\n                    <text>{{ exam.duration }}分钟</text>\n                  </view>\n                  <view v-if=\"exam.type === 'offline'\" class=\"meta-item\">\n                    <u-icon name=\"location\" color=\"#666\" size=\"24\" />\n                    <text>{{ getVenueText(exam) }}</text>\n                  </view>\n                </view>\n              </view>\n              \n              <!-- 考试状态和操作 -->\n              <view class=\"exam-actions\">\n                <StatusTag :type=\"'exam'\" :status=\"exam.status\" />\n                \n                <u-button \n                  class=\"action-btn\"\n                  :type=\"getButtonType(exam.status)\"\n                  size=\"small\"\n                  :disabled=\"!canAction(exam)\"\n                  @click.stop=\"handleAction(exam)\"\n                >\n                  {{ getActionText(exam) }}\n                </u-button>\n              </view>\n            </view>\n          </view>\n        </view>\n        \n        <!-- 历史考试记录入口 -->\n        <view class=\"history-section\">\n          <view class=\"history-card\" @click=\"viewHistory\">\n            <view class=\"history-info\">\n              <u-icon name=\"file-text\" color=\"#4A90E2\" size=\"60\" />\n              <view class=\"history-content\">\n                <text class=\"history-title\">历史考试记录</text>\n                <text class=\"history-desc\">查看所有考试记录和成绩</text>\n              </view>\n            </view>\n            <u-icon name=\"arrow-right\" color=\"#c0c4cc\" size=\"32\" />\n          </view>\n        </view>\n      </view>\n    </PermissionWrapper>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport { onShow } from '@dcloudio/uni-app'\nimport { useUserStore } from '../../src/stores/user'\nimport { useAppStore } from '../../src/stores/app'\nimport { formatDate } from '../../src/utils'\nimport { PAGE_PATHS } from '../../src/constants'\nimport api from '../../src/api'\nimport type { Exam } from '../../src/types'\n\n// 导入组件\nimport PermissionWrapper from '../../src/components/common/PermissionWrapper.vue'\nimport LoadingSpinner from '../../src/components/common/LoadingSpinner.vue'\nimport EmptyState from '../../src/components/common/EmptyState.vue'\nimport StatusTag from '../../src/components/common/StatusTag.vue'\n\n// Store\nconst userStore = useUserStore()\nconst appStore = useAppStore()\n\n// 系统信息\nconst statusBarHeight = ref(0)\n\n// 数据状态\nconst isLoading = ref(true)\nconst currentExams = ref<Exam[]>([])\n\n// 考试统计数据\nconst examStats = ref({\n  total: 0,\n  passed: 0,\n  pending: 0\n})\n\nonMounted(() => {\n  // 获取系统信息\n  const systemInfo = uni.getSystemInfoSync()\n  statusBarHeight.value = systemInfo.statusBarHeight || 0\n  \n  // 初始化数据\n  if (userStore.isAuthenticated) {\n    loadExamData()\n  }\n})\n\nonShow(() => {\n  // 页面显示时刷新数据\n  if (userStore.isAuthenticated) {\n    loadExamData()\n  }\n})\n\n// 加载考试数据\nconst loadExamData = async () => {\n  isLoading.value = true\n  \n  try {\n    // 加载当前考试列表\n    const response = await api.exam.getCurrentExams()\n    currentExams.value = response.data\n    \n    // 加载考试统计（这里可以从考试记录API获取）\n    // const statsResponse = await api.exam.getExamStats()\n    // examStats.value = statsResponse.data\n    \n    // 临时模拟数据\n    examStats.value = {\n      total: 12,\n      passed: 10,\n      pending: currentExams.value.length\n    }\n  } catch (error: any) {\n    uni.__f__('error','at pages/exam/exam.vue:218','加载考试数据失败:', error)\n    appStore.showToast(error.message || '加载失败，请重试')\n  } finally {\n    isLoading.value = false\n  }\n}\n\n// 去认证\nconst goToAuth = () => {\n  if (userStore.userInfo?.status === 'not_submitted') {\n    appStore.redirectTo(PAGE_PATHS.PROFILE)\n  } else {\n    appStore.switchTab(PAGE_PATHS.PERSONAL)\n  }\n}\n\n// 查看日历\nconst viewCalendar = () => {\n  appStore.showToast('考试日历功能开发中')\n}\n\n// 格式化考试时间\nconst formatExamTime = (exam: Exam) => {\n  const start = new Date(exam.startTime)\n  const end = new Date(exam.endTime)\n  \n  if (exam.type === 'online') {\n    return `${formatDate(start, 'MM月DD日 HH:mm')}-${formatDate(end, 'HH:mm')}`\n  } else {\n    return `${formatDate(start, 'MM月DD日 HH:mm')}`\n  }\n}\n\n// 获取场地文本\nconst getVenueText = (exam: Exam) => {\n  // 这里应该从场地信息中获取\n  return '待安排'\n}\n\n// 获取按钮类型\nconst getButtonType = (status: string) => {\n  const typeMap: Record<string, string> = {\n    'not_started': 'primary',\n    'in_progress': 'warning',\n    'completed': 'info',\n    'expired': 'info'\n  }\n  return typeMap[status] || 'default'\n}\n\n// 是否可以操作\nconst canAction = (exam: Exam) => {\n  return !['completed', 'expired'].includes(exam.status)\n}\n\n// 获取操作文本\nconst getActionText = (exam: Exam) => {\n  const actionMap: Record<string, string> = {\n    'not_started': '准备考试',\n    'in_progress': '继续考试',\n    'completed': '查看成绩',\n    'expired': '已过期'\n  }\n  return actionMap[exam.status] || '查看详情'\n}\n\n// 处理考试点击\nconst handleExamClick = (exam: Exam) => {\n  // 点击卡片区域的处理逻辑\n  if (exam.type === 'online') {\n    handleOnlineExam(exam)\n  } else {\n    handleOfflineExam(exam)\n  }\n}\n\n// 处理操作按钮点击\nconst handleAction = (exam: Exam) => {\n  if (exam.type === 'online') {\n    handleOnlineExam(exam)\n  } else {\n    handleOfflineExam(exam)\n  }\n}\n\n// 处理线上考试\nconst handleOnlineExam = (exam: Exam) => {\n  if (exam.status === 'not_started') {\n    // 跳转到考前阅读\n    appStore.navigateTo(PAGE_PATHS.EXAM_ONLINE_READING, { examId: exam.id })\n  } else if (exam.status === 'in_progress') {\n    // 继续考试\n    appStore.showModal({\n      title: '继续考试',\n      content: '检测到您有未完成的考试，是否继续？',\n      confirmText: '继续考试',\n      cancelText: '取消'\n    }).then((confirmed) => {\n      if (confirmed) {\n        appStore.navigateTo(PAGE_PATHS.EXAM_ONLINE_ANSWER, { examId: exam.id })\n      }\n    })\n  } else if (exam.status === 'completed') {\n    // 查看成绩\n    appStore.navigateTo(PAGE_PATHS.EXAM_HISTORY, { examId: exam.id })\n  }\n}\n\n// 处理线下考试\nconst handleOfflineExam = (exam: Exam) => {\n  // 跳转到线下考试详情页面\n  appStore.navigateTo(PAGE_PATHS.EXAM_OFFLINE_DETAIL, { examId: exam.id })\n}\n\n// 查看历史记录\nconst viewHistory = () => {\n  appStore.navigateTo(PAGE_PATHS.EXAM_HISTORY)\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../src/styles/global.scss';\n\n.exam-center-container {\n  min-height: 100vh;\n  background: $acdc-bg-primary;\n}\n\n.status-bar {\n  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);\n}\n\n.header {\n  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);\n  padding: 20rpx 30rpx 40rpx;\n  \n  .header-content {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    \n    .page-title {\n      font-size: 36rpx;\n      font-weight: bold;\n      color: #fff;\n    }\n    \n    .header-actions {\n      display: flex;\n      align-items: center;\n    }\n  }\n}\n\n.auth-prompt {\n  padding: 60rpx 30rpx;\n  \n  .prompt-card {\n    background: #fff;\n    border-radius: 32rpx;\n    padding: 80rpx 40rpx;\n    text-align: center;\n    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);\n    \n    .prompt-title {\n      display: block;\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n      margin: 40rpx 0 20rpx;\n    }\n    \n    .prompt-desc {\n      display: block;\n      font-size: 28rpx;\n      color: #666;\n      line-height: 1.6;\n      margin-bottom: 60rpx;\n    }\n    \n    .auth-btn {\n      width: 300rpx;\n      height: 80rpx;\n      border-radius: 40rpx;\n    }\n  }\n}\n\n.main-content {\n  padding: 30rpx;\n  padding-bottom: 120rpx; // 为底部导航留空间\n}\n\n.stats-card {\n  background: #fff;\n  border-radius: 24rpx;\n  padding: 40rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);\n  display: flex;\n  align-items: center;\n  \n  .stats-item {\n    flex: 1;\n    text-align: center;\n    \n    .stats-number {\n      display: block;\n      font-size: 48rpx;\n      font-weight: bold;\n      color: #4A90E2;\n      margin-bottom: 8rpx;\n    }\n    \n    .stats-label {\n      font-size: 24rpx;\n      color: #666;\n    }\n  }\n  \n  .stats-divider {\n    width: 2rpx;\n    height: 60rpx;\n    background: #f0f0f0;\n  }\n}\n\n.current-exams-section {\n  margin-bottom: 40rpx;\n  \n  .section-header {\n    margin-bottom: 30rpx;\n    \n    .section-title {\n      display: block;\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 8rpx;\n    }\n    \n    .section-desc {\n      font-size: 26rpx;\n      color: #666;\n    }\n  }\n  \n  .exam-list {\n    .exam-card {\n      position: relative;\n      background: #fff;\n      border-radius: 24rpx;\n      padding: 40rpx;\n      margin-bottom: 24rpx;\n      box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n      \n      .exam-type-tag {\n        position: absolute;\n        top: 20rpx;\n        right: 20rpx;\n        padding: 8rpx 16rpx;\n        border-radius: 12rpx;\n        font-size: 20rpx;\n        color: #fff;\n        \n        &.online {\n          background: #4A90E2;\n        }\n        \n        &.offline {\n          background: #FF9500;\n        }\n      }\n      \n      .exam-info {\n        margin-bottom: 30rpx;\n        padding-right: 100rpx; // 为标签留空间\n        \n        .exam-title {\n          display: block;\n          font-size: 30rpx;\n          font-weight: bold;\n          color: #333;\n          margin-bottom: 12rpx;\n          line-height: 1.4;\n        }\n        \n        .exam-desc {\n          display: block;\n          font-size: 26rpx;\n          color: #666;\n          margin-bottom: 20rpx;\n          line-height: 1.4;\n        }\n        \n        .exam-meta {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 24rpx;\n          \n          .meta-item {\n            display: flex;\n            align-items: center;\n            \n            text {\n              margin-left: 8rpx;\n              font-size: 24rpx;\n              color: #666;\n            }\n          }\n        }\n      }\n      \n      .exam-actions {\n        display: flex;\n        align-items: center;\n        justify-content: space-between;\n        \n        .action-btn {\n          width: 160rpx;\n          height: 64rpx;\n          border-radius: 32rpx;\n        }\n      }\n    }\n  }\n}\n\n.history-section {\n  .history-card {\n    background: #fff;\n    border-radius: 24rpx;\n    padding: 40rpx;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    \n    .history-info {\n      display: flex;\n      align-items: center;\n      flex: 1;\n      \n      .history-content {\n        margin-left: 30rpx;\n        \n        .history-title {\n          display: block;\n          font-size: 30rpx;\n          font-weight: bold;\n          color: #333;\n          margin-bottom: 8rpx;\n        }\n        \n        .history-desc {\n          font-size: 26rpx;\n          color: #666;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/exam/exam.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useUserStore", "useAppStore", "ref", "onMounted", "uni", "onShow", "api", "PAGE_PATHS", "formatDate"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA,MAAA,oBAA8B,MAAA;AAC9B,MAAA,iBAA2B,MAAA;AAC3B,MAAA,aAAuB,MAAA;AACvB,MAAA,YAAsB,MAAA;;;;AAGtB,UAAM,YAAYA,gBAAAA;AAClB,UAAM,WAAWC,eAAAA;AAGX,UAAA,kBAAkBC,kBAAI,CAAC;AAGvB,UAAA,YAAYA,kBAAI,IAAI;AACpB,UAAA,eAAeA,kBAAY,CAAA,CAAE;AAGnC,UAAM,YAAYA,cAAAA,IAAI;AAAA,MACpB,OAAO;AAAA,MACP,QAAQ;AAAA,MACR,SAAS;AAAA,IAAA,CACV;AAEDC,kBAAAA,UAAU,MAAM;AAER,YAAA,aAAaC,oBAAI;AACP,sBAAA,QAAQ,WAAW,mBAAmB;AAGtD,UAAI,UAAU,iBAAiB;AAChB;MACf;AAAA,IAAA,CACD;AAEDC,kBAAAA,OAAO,MAAM;AAEX,UAAI,UAAU,iBAAiB;AAChB;MACf;AAAA,IAAA,CACD;AAGD,UAAM,eAAe,MAAY;AAC/B,gBAAU,QAAQ;AAEd,UAAA;AAEF,cAAM,WAAW,MAAMC,cAAAA,IAAI,KAAK,gBAAgB;AAChD,qBAAa,QAAQ,SAAS;AAO9B,kBAAU,QAAQ;AAAA,UAChB,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,SAAS,aAAa,MAAM;AAAA,QAAA;AAAA,eAEvB,OAAY;AACnBF,sBAAA,MAAI,MAAM,SAAQ,8BAA6B,aAAa,KAAK;AACxD,iBAAA,UAAU,MAAM,WAAW,UAAU;AAAA,MAAA,UAC9C;AACA,kBAAU,QAAQ;AAAA,MACpB;AAAA,IAAA;AAIF,UAAM,WAAW,MAAM;;AACjB,YAAA,eAAU,aAAV,mBAAoB,YAAW,iBAAiB;AACzC,iBAAA,WAAWG,+BAAW,OAAO;AAAA,MAAA,OACjC;AACI,iBAAA,UAAUA,+BAAW,QAAQ;AAAA,MACxC;AAAA,IAAA;AAIF,UAAM,eAAe,MAAM;AACzB,eAAS,UAAU,WAAW;AAAA,IAAA;AAI1B,UAAA,iBAAiB,CAAC,SAAe;AACrC,YAAM,QAAQ,IAAI,KAAK,KAAK,SAAS;AACrC,YAAM,MAAM,IAAI,KAAK,KAAK,OAAO;AAE7B,UAAA,KAAK,SAAS,UAAU;AACnB,eAAA,GAAGC,gBAAAA,WAAW,OAAO,cAAc,CAAC,IAAIA,gBAAAA,WAAW,KAAK,OAAO,CAAC;AAAA,MAAA,OAClE;AACL,eAAO,GAAGA,gBAAA,WAAW,OAAO,cAAc,CAAC;AAAA,MAC7C;AAAA,IAAA;AAII,UAAA,eAAe,CAAC,SAAe;AAE5B,aAAA;AAAA,IAAA;AAIH,UAAA,gBAAgB,CAAC,WAAmB;AACxC,YAAM,UAAkC;AAAA,QACtC,eAAe;AAAA,QACf,eAAe;AAAA,QACf,aAAa;AAAA,QACb,WAAW;AAAA,MAAA;AAEN,aAAA,QAAQ,MAAM,KAAK;AAAA,IAAA;AAItB,UAAA,YAAY,CAAC,SAAe;AAChC,aAAO,CAAC,CAAC,aAAa,SAAS,EAAE,SAAS,KAAK,MAAM;AAAA,IAAA;AAIjD,UAAA,gBAAgB,CAAC,SAAe;AACpC,YAAM,YAAoC;AAAA,QACxC,eAAe;AAAA,QACf,eAAe;AAAA,QACf,aAAa;AAAA,QACb,WAAW;AAAA,MAAA;AAEN,aAAA,UAAU,KAAK,MAAM,KAAK;AAAA,IAAA;AAI7B,UAAA,kBAAkB,CAAC,SAAe;AAElC,UAAA,KAAK,SAAS,UAAU;AAC1B,yBAAiB,IAAI;AAAA,MAAA,OAChB;AACL,0BAAkB,IAAI;AAAA,MACxB;AAAA,IAAA;AAII,UAAA,eAAe,CAAC,SAAe;AAC/B,UAAA,KAAK,SAAS,UAAU;AAC1B,yBAAiB,IAAI;AAAA,MAAA,OAChB;AACL,0BAAkB,IAAI;AAAA,MACxB;AAAA,IAAA;AAII,UAAA,mBAAmB,CAAC,SAAe;AACnC,UAAA,KAAK,WAAW,eAAe;AAEjC,iBAAS,WAAWD,oBAAAA,WAAW,qBAAqB,EAAE,QAAQ,KAAK,IAAI;AAAA,MAAA,WAC9D,KAAK,WAAW,eAAe;AAExC,iBAAS,UAAU;AAAA,UACjB,OAAO;AAAA,UACP,SAAS;AAAA,UACT,aAAa;AAAA,UACb,YAAY;AAAA,QAAA,CACb,EAAE,KAAK,CAAC,cAAc;AACrB,cAAI,WAAW;AACb,qBAAS,WAAWA,oBAAAA,WAAW,oBAAoB,EAAE,QAAQ,KAAK,IAAI;AAAA,UACxE;AAAA,QAAA,CACD;AAAA,MAAA,WACQ,KAAK,WAAW,aAAa;AAEtC,iBAAS,WAAWA,oBAAAA,WAAW,cAAc,EAAE,QAAQ,KAAK,IAAI;AAAA,MAClE;AAAA,IAAA;AAII,UAAA,oBAAoB,CAAC,SAAe;AAExC,eAAS,WAAWA,oBAAAA,WAAW,qBAAqB,EAAE,QAAQ,KAAK,IAAI;AAAA,IAAA;AAIzE,UAAM,cAAc,MAAM;AACf,eAAA,WAAWA,+BAAW,YAAY;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5U7C,GAAG,WAAW,eAAe;"}