"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
const src_stores_app = require("../../../src/stores/app.js");
const src_utils_index = require("../../../src/utils/index.js");
const src_constants_index = require("../../../src/constants/index.js");
const src_api_index = require("../../../src/api/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_navbar2 + _easycom_u_icon2 + _easycom_u_button2)();
}
const _easycom_u_navbar = () => "../../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_navbar + LoadingSpinner + EmptyState + StatusTag + _easycom_u_icon + _easycom_u_button)();
}
const LoadingSpinner = () => "../../../src/components/common/LoadingSpinner.js";
const EmptyState = () => "../../../src/components/common/EmptyState.js";
const StatusTag = () => "../../../src/components/common/StatusTag.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "detail",
  props: {
    examId: {}
  },
  setup(__props) {
    const appStore = src_stores_app.useAppStore();
    const props = __props;
    const isLoading = common_vendor.ref(true);
    const error = common_vendor.ref("");
    const examDetail = common_vendor.ref(null);
    const registrationInfo = common_vendor.ref(null);
    const isRegistering = common_vendor.ref(false);
    const canRegister = common_vendor.computed(() => {
      var _a;
      return ((_a = examDetail.value) == null ? void 0 : _a.status) === "not_started" && !registrationInfo.value;
    });
    const canCancelRegister = common_vendor.computed(() => {
      var _a, _b;
      return ((_a = registrationInfo.value) == null ? void 0 : _a.status) === "registered" && ((_b = examDetail.value) == null ? void 0 : _b.status) === "not_started";
    });
    const canViewResult = common_vendor.computed(() => {
      var _a, _b;
      return ((_a = examDetail.value) == null ? void 0 : _a.status) === "completed" && ((_b = registrationInfo.value) == null ? void 0 : _b.status) === "completed";
    });
    common_vendor.onMounted(() => {
      loadExamDetail();
    });
    const loadExamDetail = () => __async(this, null, function* () {
      isLoading.value = true;
      error.value = "";
      try {
        const examResponse = yield src_api_index.api.exam.getExamDetail(props.examId);
        examDetail.value = examResponse.data;
        try {
          const registrationResponse = yield src_api_index.api.exam.getRegistrationInfo(props.examId);
          registrationInfo.value = registrationResponse.data;
        } catch (regError) {
          registrationInfo.value = null;
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at subpages/exam/offline/detail.vue:247", "加载考试详情失败:", err);
        error.value = err.message || "加载失败";
      } finally {
        isLoading.value = false;
      }
    });
    const formatExamTime = () => {
      if (!examDetail.value)
        return "";
      const startTime = new Date(examDetail.value.startTime);
      const endTime = new Date(examDetail.value.endTime);
      return `${src_utils_index.formatDate(startTime, "YYYY年MM月DD日 HH:mm")} - ${src_utils_index.formatDate(endTime, "HH:mm")}`;
    };
    const getStatusTip = () => {
      if (!examDetail.value)
        return "";
      switch (examDetail.value.status) {
        case "not_started":
          return "考试尚未开始";
        case "in_progress":
          return "考试正在进行中";
        case "completed":
          return "考试已结束";
        case "expired":
          return "考试已过期";
        default:
          return "";
      }
    };
    const handleRegister = () => __async(this, null, function* () {
      isRegistering.value = true;
      try {
        yield src_api_index.api.exam.registerExam(props.examId);
        appStore.showToast("报名成功", "success");
        yield loadExamDetail();
      } catch (error2) {
        common_vendor.index.__f__("error", "at subpages/exam/offline/detail.vue:293", "报名失败:", error2);
        appStore.showToast(error2.message || "报名失败，请重试");
      } finally {
        isRegistering.value = false;
      }
    });
    const handleCancelRegister = () => {
      appStore.showModal({
        title: "确认取消",
        content: "确定要取消报名吗？取消后需要重新报名。",
        confirmText: "确认取消",
        cancelText: "保留报名"
      }).then((confirmed) => __async(this, null, function* () {
        if (confirmed) {
          try {
            yield src_api_index.api.exam.cancelRegistration(props.examId);
            appStore.showToast("取消报名成功", "success");
            yield loadExamDetail();
          } catch (error2) {
            common_vendor.index.__f__("error", "at subpages/exam/offline/detail.vue:316", "取消报名失败:", error2);
            appStore.showToast(error2.message || "取消报名失败，请重试");
          }
        }
      }));
    };
    const viewExamResult = () => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.EXAM_HISTORY, { examId: props.examId });
    };
    const makePhoneCall = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: "************"
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "线下考试详情",
          autoBack: true,
          background: {
            background: "linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: isLoading.value
      }, isLoading.value ? {
        c: common_vendor.p({
          text: "加载考试信息..."
        })
      } : error.value ? {
        e: common_vendor.o(loadExamDetail),
        f: common_vendor.p({
          type: "no-data",
          title: error.value,
          description: "无法加载考试详情",
          showButton: true,
          buttonText: "重新加载"
        })
      } : examDetail.value ? common_vendor.e({
        h: common_vendor.t(examDetail.value.name),
        i: common_vendor.p({
          type: "exam",
          status: examDetail.value.status
        }),
        j: common_vendor.t(examDetail.value.description),
        k: common_vendor.p({
          name: "calendar",
          color: "#4A90E2",
          size: "32"
        }),
        l: common_vendor.t(formatExamTime()),
        m: common_vendor.p({
          name: "clock",
          color: "#4A90E2",
          size: "32"
        }),
        n: common_vendor.t(examDetail.value.duration),
        o: common_vendor.p({
          name: "location",
          color: "#4A90E2",
          size: "32"
        }),
        p: common_vendor.t(examDetail.value.venue || "待安排"),
        q: common_vendor.p({
          name: "file-text",
          color: "#4A90E2",
          size: "32"
        }),
        r: common_vendor.t(examDetail.value.totalScore),
        s: common_vendor.p({
          name: "checkmark-circle",
          color: "#4A90E2",
          size: "32"
        }),
        t: common_vendor.t(examDetail.value.passScore),
        v: common_vendor.p({
          name: "account",
          color: "#4A90E2",
          size: "32"
        }),
        w: registrationInfo.value
      }, registrationInfo.value ? common_vendor.e({
        x: common_vendor.p({
          type: "registration",
          status: registrationInfo.value.status
        }),
        y: common_vendor.t(common_vendor.unref(src_utils_index.formatDate)(registrationInfo.value.registeredAt, "YYYY-MM-DD HH:mm")),
        z: registrationInfo.value.seatNumber
      }, registrationInfo.value.seatNumber ? {
        A: common_vendor.t(registrationInfo.value.seatNumber)
      } : {}) : {
        B: common_vendor.p({
          name: "info-circle",
          color: "#FF9500",
          size: "40"
        })
      }, {
        C: common_vendor.p({
          name: "warning",
          color: "#FF9500",
          size: "32"
        }),
        D: common_vendor.p({
          name: "phone",
          color: "#4A90E2",
          size: "32"
        }),
        E: common_vendor.p({
          name: "phone",
          color: "#4CAF50",
          size: "28"
        }),
        F: common_vendor.o(makePhoneCall),
        G: common_vendor.p({
          name: "email",
          color: "#4A90E2",
          size: "28"
        })
      }) : {}, {
        d: error.value,
        g: examDetail.value,
        H: examDetail.value
      }, examDetail.value ? common_vendor.e({
        I: canRegister.value
      }, canRegister.value ? {
        J: common_vendor.o(handleRegister),
        K: common_vendor.p({
          type: "primary",
          loading: isRegistering.value,
          loadingText: "报名中..."
        })
      } : canCancelRegister.value ? {
        M: common_vendor.o(handleCancelRegister),
        N: common_vendor.p({
          type: "error",
          plain: true
        })
      } : canViewResult.value ? {
        P: common_vendor.o(viewExamResult),
        Q: common_vendor.p({
          type: "success"
        })
      } : {
        R: common_vendor.t(getStatusTip())
      }, {
        L: canCancelRegister.value,
        O: canViewResult.value
      }) : {});
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5d3ff4e0"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpages/exam/offline/detail.js.map
