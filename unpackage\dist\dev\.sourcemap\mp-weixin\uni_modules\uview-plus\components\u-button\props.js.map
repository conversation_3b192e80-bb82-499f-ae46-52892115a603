{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-button/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        // 是否细边框\n        hairline: {\n            type: <PERSON><PERSON>an,\n            default: () => defProps.button.hairline\n        },\n        // 按钮的预置样式，info，primary，error，warning，success\n        type: {\n            type: String,\n            default: () => defProps.button.type\n        },\n        // 按钮尺寸，large，normal，small，mini\n        size: {\n            type: String,\n            default: () => defProps.button.size\n        },\n        // 按钮形状，circle（两边为半圆），square（带圆角）\n        shape: {\n            type: String,\n            default: () => defProps.button.shape\n        },\n        // 按钮是否镂空\n        plain: {\n            type: <PERSON>olean,\n            default: () => defProps.button.plain\n        },\n        // 是否禁止状态\n        disabled: {\n            type: Boolean,\n            default: () => defProps.button.disabled\n        },\n        // 是否加载中\n        loading: {\n            type: Boolean,\n            default: () => defProps.button.loading\n        },\n        // 加载中提示文字\n        loadingText: {\n            type: [String, Number],\n            default: () => defProps.button.loadingText\n        },\n        // 加载状态图标类型\n        loadingMode: {\n            type: String,\n            default: () => defProps.button.loadingMode\n        },\n        // 加载图标大小\n        loadingSize: {\n            type: [String, Number],\n            default: () => defProps.button.loadingSize\n        },\n        // 开放能力，具体请看uniapp稳定关于button组件部分说明\n        // https://uniapp.dcloud.io/component/button\n        openType: {\n            type: String,\n            default: () => defProps.button.openType\n        },\n        // 用于 <form> 组件，点击分别会触发 <form> 组件的 submit/reset 事件\n        // 取值为submit（提交表单），reset（重置表单）\n        formType: {\n            type: String,\n            default: () => defProps.button.formType\n        },\n        // 打开 APP 时，向 APP 传递的参数，open-type=launchApp时有效\n        // 只微信小程序、QQ小程序有效\n        appParameter: {\n            type: String,\n            default: () => defProps.button.appParameter\n        },\n        // 指定是否阻止本节点的祖先节点出现点击态，微信小程序有效\n        hoverStopPropagation: {\n            type: Boolean,\n            default: () => defProps.button.hoverStopPropagation\n        },\n        // 指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文。只微信小程序有效\n        lang: {\n            type: String,\n            default: () => defProps.button.lang\n        },\n        // 会话来源，open-type=\"contact\"时有效。只微信小程序有效\n        sessionFrom: {\n            type: String,\n            default: () => defProps.button.sessionFrom\n        },\n        // 会话内消息卡片标题，open-type=\"contact\"时有效\n        // 默认当前标题，只微信小程序有效\n        sendMessageTitle: {\n            type: String,\n            default: () => defProps.button.sendMessageTitle\n        },\n        // 会话内消息卡片点击跳转小程序路径，open-type=\"contact\"时有效\n        // 默认当前分享路径，只微信小程序有效\n        sendMessagePath: {\n            type: String,\n            default: () => defProps.button.sendMessagePath\n        },\n        // 会话内消息卡片图片，open-type=\"contact\"时有效\n        // 默认当前页面截图，只微信小程序有效\n        sendMessageImg: {\n            type: String,\n            default: () => defProps.button.sendMessageImg\n        },\n        // 是否显示会话内消息卡片，设置此参数为 true，用户进入客服会话会在右下角显示\"可能要发送的小程序\"提示，\n        // 用户点击后可以快速发送小程序消息，open-type=\"contact\"时有效\n        showMessageCard: {\n            type: Boolean,\n            default: () => defProps.button.showMessageCard\n        },\n        // 额外传参参数，用于小程序的data-xxx属性，通过target.dataset.name获取\n        dataName: {\n            type: String,\n            default: () => defProps.button.dataName\n        },\n        // 节流，一定时间内只能触发一次\n        throttleTime: {\n            type: [String, Number],\n            default: () => defProps.button.throttleTime\n        },\n        // 按住后多久出现点击态，单位毫秒\n        hoverStartTime: {\n            type: [String, Number],\n            default: () => defProps.button.hoverStartTime\n        },\n        // 手指松开后点击态保留时间，单位毫秒\n        hoverStayTime: {\n            type: [String, Number],\n            default: () => defProps.button.hoverStayTime\n        },\n        // 按钮文字，之所以通过props传入，是因为slot传入的话\n        // nvue中无法控制文字的样式\n        text: {\n            type: [String, Number],\n            default: () => defProps.button.text\n        },\n        // 按钮图标\n        icon: {\n            type: String,\n            default: () => defProps.button.icon\n        },\n        // 按钮图标\n        iconColor: {\n            type: String,\n            default: () => defProps.button.icon\n        },\n        // 按钮颜色，支持传入linear-gradient渐变色\n        color: {\n            type: String,\n            default: () => defProps.button.color\n        },\n        // 停止冒泡\n        stop: {\n            type: Boolean,\n            default: () => defProps.button.stop\n        },\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA;AAAA,IAEH,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,cAAc;AAAA,MACV,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,sBAAsB;AAAA,MAClB,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,aAAa;AAAA,MACT,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,iBAAiB;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,gBAAgB;AAAA,MACZ,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,iBAAiB;AAAA,MACb,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,UAAU;AAAA,MACN,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,cAAc;AAAA,MACV,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,gBAAgB;AAAA,MACZ,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,eAAe;AAAA,MACX,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA;AAAA,IAGD,MAAM;AAAA,MACF,MAAM,CAAC,QAAQ,MAAM;AAAA,MACrB,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,WAAW;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,OAAO;AAAA,MACH,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA;AAAA,IAED,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,OAAO;AAAA,IAClC;AAAA,EACJ;AACL,CAAC;;"}