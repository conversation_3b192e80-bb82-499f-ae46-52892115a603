"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_study = require("../../src/stores/study.js");
const src_stores_app = require("../../src/stores/app.js");
const src_utils_index = require("../../src/utils/index.js");
const src_constants_index = require("../../src/constants/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  (_easycom_u_navbar2 + _easycom_u_icon2 + _easycom_u_button2 + _easycom_u_popup2)();
}
const _easycom_u_navbar = () => "../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_u_popup = () => "../../uni_modules/uview-plus/components/u-popup/u-popup.js";
if (!Math) {
  (_easycom_u_navbar + LoadingSpinner + EmptyState + _easycom_u_icon + StatusTag + _easycom_u_button + _easycom_u_popup)();
}
const LoadingSpinner = () => "../../src/components/common/LoadingSpinner.js";
const EmptyState = () => "../../src/components/common/EmptyState.js";
const StatusTag = () => "../../src/components/common/StatusTag.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "summary",
  props: {
    sessionId: {}
  },
  setup(__props) {
    const studyStore = src_stores_study.useStudyStore();
    const appStore = src_stores_app.useAppStore();
    const props = __props;
    const isLoading = common_vendor.ref(true);
    const error = common_vendor.ref("");
    const session = common_vendor.ref(null);
    const currentFilter = common_vendor.ref("all");
    const showQuestionDetail = common_vendor.ref(false);
    const selectedQuestion = common_vendor.ref(null);
    const selectedQuestionIndex = common_vendor.ref(0);
    const filterOptions = [
      { key: "all", label: "全部" },
      { key: "correct", label: "正确" },
      { key: "wrong", label: "错误" }
    ];
    const filteredQuestions = common_vendor.computed(() => {
      if (!session.value)
        return [];
      const questions = session.value.questions;
      switch (currentFilter.value) {
        case "correct":
          return questions.filter((q) => isQuestionCorrect(q));
        case "wrong":
          return questions.filter((q) => !isQuestionCorrect(q));
        default:
          return questions;
      }
    });
    common_vendor.onMounted(() => {
      loadSession();
    });
    const loadSession = () => {
      isLoading.value = true;
      error.value = "";
      try {
        const foundSession = studyStore.practiceHistory.find((s) => s.id === props.sessionId);
        if (!foundSession) {
          error.value = "练习记录不存在";
          return;
        }
        session.value = foundSession;
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/study/summary.vue:286", "加载练习总结失败:", err);
        error.value = "加载失败";
      } finally {
        isLoading.value = false;
      }
    };
    const getAccuracy = () => {
      if (!session.value || session.value.totalCount === 0)
        return 0;
      return Math.round((session.value.correctCount || 0) / session.value.totalCount * 100);
    };
    const getDuration = () => {
      if (!session.value || !session.value.endTime)
        return "未知";
      const start = new Date(session.value.startTime).getTime();
      const end = new Date(session.value.endTime).getTime();
      const duration = Math.floor((end - start) / 1e3);
      return src_utils_index.formatDuration(duration);
    };
    const getScoreLevel = (score) => {
      if (score >= 90)
        return "excellent";
      if (score >= 80)
        return "good";
      if (score >= 60)
        return "normal";
      return "poor";
    };
    const getScoreIcon = (score) => {
      if (score >= 90)
        return "trophy";
      if (score >= 80)
        return "thumbs-up";
      if (score >= 60)
        return "checkmark";
      return "close";
    };
    const getEvaluationText = () => {
      var _a;
      const score = ((_a = session.value) == null ? void 0 : _a.score) || 0;
      if (score >= 90)
        return "优秀！您的表现非常出色，继续保持！";
      if (score >= 80)
        return "良好！您掌握得不错，再接再厉！";
      if (score >= 60)
        return "及格！还有提升空间，建议多加练习。";
      return "需要加强！建议重点复习相关知识点。";
    };
    const isQuestionCorrect = (question) => {
      if (!session.value)
        return false;
      const userAnswer = session.value.answers[question.id];
      return studyStore.isAnswerCorrect(question, userAnswer);
    };
    const getQuestionNumber = (questionId) => {
      if (!session.value)
        return 0;
      const index = session.value.questions.findIndex((q) => q.id === questionId);
      return index + 1;
    };
    const getDifficultyText = (difficulty) => {
      const textMap = {
        easy: "基础",
        medium: "进阶",
        hard: "高级"
      };
      return textMap[difficulty] || "进阶";
    };
    const formatUserAnswer = (question) => {
      var _a;
      if (!session.value)
        return "未答";
      const answer = session.value.answers[question.id];
      if (answer === null || answer === void 0)
        return "未答";
      if (question.type === "judge") {
        return answer ? "正确" : "错误";
      }
      if (question.type === "single") {
        return ((_a = question.options) == null ? void 0 : _a[answer]) || "未知选项";
      }
      if (question.type === "multiple") {
        if (!Array.isArray(answer))
          return "未答";
        return answer.map((index) => {
          var _a2;
          return (_a2 = question.options) == null ? void 0 : _a2[index];
        }).join("、") || "未知选项";
      }
      if (question.type === "essay") {
        return answer.toString().substring(0, 50) + (answer.length > 50 ? "..." : "");
      }
      return "未知";
    };
    const formatCorrectAnswer = (question) => {
      var _a;
      const answer = question.answer;
      if (question.type === "judge") {
        return answer ? "正确" : "错误";
      }
      if (question.type === "single") {
        return ((_a = question.options) == null ? void 0 : _a[answer]) || "未知";
      }
      if (question.type === "multiple") {
        if (!Array.isArray(answer))
          return "未知";
        return answer.map((index) => {
          var _a2;
          return (_a2 = question.options) == null ? void 0 : _a2[index];
        }).join("、") || "未知";
      }
      return answer.toString();
    };
    const getOptionLabel = (index) => {
      return String.fromCharCode(65 + index);
    };
    const getDetailOptionClass = (question, index) => {
      var _a;
      const classes = ["detail-option"];
      const userAnswer = (_a = session.value) == null ? void 0 : _a.answers[question.id];
      const correctAnswer = question.answer;
      if (question.type === "single" && userAnswer === index) {
        classes.push(userAnswer === correctAnswer ? "user-correct" : "user-wrong");
      }
      if (question.type === "multiple" && Array.isArray(userAnswer) && userAnswer.includes(index)) {
        classes.push("user-selected");
      }
      if (question.type === "single" && correctAnswer === index) {
        classes.push("correct-answer");
      }
      if (question.type === "multiple" && Array.isArray(correctAnswer) && correctAnswer.includes(index)) {
        classes.push("correct-answer");
      }
      return classes.join(" ");
    };
    const getSuggestions = () => {
      var _a;
      const suggestions = [];
      const score = ((_a = session.value) == null ? void 0 : _a.score) || 0;
      const accuracy = getAccuracy();
      if (score < 60) {
        suggestions.push({
          type: "review",
          icon: "book",
          color: "#f56c6c",
          title: "加强基础学习",
          description: "建议重新学习相关知识点，打好基础"
        });
      }
      if (accuracy < 70) {
        suggestions.push({
          type: "practice",
          icon: "edit-pen",
          color: "#FF9500",
          title: "增加练习频次",
          description: "多做练习题，提高答题准确率"
        });
      }
      if (score >= 80) {
        suggestions.push({
          type: "advance",
          icon: "arrow-up",
          color: "#4CAF50",
          title: "挑战更高难度",
          description: "可以尝试更高难度的题目"
        });
      }
      return suggestions;
    };
    const viewQuestionDetail = (question, index) => {
      selectedQuestion.value = question;
      selectedQuestionIndex.value = index;
      showQuestionDetail.value = true;
    };
    const reviewWrongQuestions = () => {
      currentFilter.value = "wrong";
      appStore.showToast("已切换到错题模式");
    };
    const continueStudy = () => {
      appStore.navigateTo(src_constants_index.PAGE_PATHS.STUDY_CATEGORY);
    };
    const goBack = () => {
      appStore.navigateBack();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "练习总结",
          autoBack: true,
          background: {
            background: "linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: isLoading.value
      }, isLoading.value ? {
        c: common_vendor.p({
          text: "生成总结中..."
        })
      } : error.value ? {
        e: common_vendor.o(goBack),
        f: common_vendor.p({
          type: "no-data",
          title: error.value,
          description: "无法加载练习总结",
          showButton: true,
          buttonText: "返回"
        })
      } : session.value ? {
        h: common_vendor.p({
          name: getScoreIcon(session.value.score || 0),
          color: "#fff",
          size: "80"
        }),
        i: common_vendor.n(getScoreLevel(session.value.score || 0)),
        j: common_vendor.t(session.value.score || 0),
        k: common_vendor.t(getAccuracy()),
        l: common_vendor.t(session.value.correctCount || 0),
        m: common_vendor.t(session.value.totalCount),
        n: common_vendor.t(getDuration()),
        o: common_vendor.t(getEvaluationText()),
        p: common_vendor.f(filterOptions, (filter, k0, i0) => {
          return {
            a: common_vendor.t(filter.label),
            b: filter.key,
            c: currentFilter.value === filter.key ? 1 : "",
            d: common_vendor.o(($event) => currentFilter.value = filter.key, filter.key)
          };
        }),
        q: common_vendor.f(filteredQuestions.value, (question, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(getQuestionNumber(question.id)),
            b: "5dc513bd-4-" + i0,
            c: common_vendor.p({
              name: isQuestionCorrect(question) ? "checkmark-circle-fill" : "close-circle-fill",
              color: isQuestionCorrect(question) ? "#4CAF50" : "#f56c6c",
              size: "32"
            }),
            d: common_vendor.t(question.title),
            e: "5dc513bd-5-" + i0,
            f: common_vendor.p({
              type: "question",
              status: question.type
            }),
            g: question.difficulty
          }, question.difficulty ? {
            h: common_vendor.t(getDifficultyText(question.difficulty))
          } : {}, {
            i: common_vendor.t(formatUserAnswer(question)),
            j: isQuestionCorrect(question) ? 1 : "",
            k: !isQuestionCorrect(question) ? 1 : "",
            l: !isQuestionCorrect(question)
          }, !isQuestionCorrect(question) ? {
            m: common_vendor.t(formatCorrectAnswer(question))
          } : {}, {
            n: question.id,
            o: common_vendor.o(($event) => viewQuestionDetail(question, index), question.id)
          });
        }),
        r: common_vendor.p({
          name: "lightbulb",
          color: "#FF9500",
          size: "32"
        }),
        s: common_vendor.f(getSuggestions(), (suggestion, k0, i0) => {
          return {
            a: "5dc513bd-7-" + i0,
            b: common_vendor.p({
              name: suggestion.icon,
              color: suggestion.color,
              size: "40"
            }),
            c: common_vendor.t(suggestion.title),
            d: common_vendor.t(suggestion.description),
            e: suggestion.type
          };
        })
      } : {}, {
        d: error.value,
        g: session.value,
        t: common_vendor.o(reviewWrongQuestions),
        v: common_vendor.p({
          type: "info",
          plain: true
        }),
        w: common_vendor.o(continueStudy),
        x: common_vendor.p({
          type: "primary"
        }),
        y: selectedQuestion.value
      }, selectedQuestion.value ? common_vendor.e({
        z: common_vendor.o(($event) => showQuestionDetail.value = false),
        A: common_vendor.p({
          name: "close",
          size: "32"
        }),
        B: common_vendor.p({
          type: "question",
          status: selectedQuestion.value.type
        }),
        C: common_vendor.t(selectedQuestionIndex.value + 1),
        D: common_vendor.t(selectedQuestion.value.title),
        E: selectedQuestion.value.options
      }, selectedQuestion.value.options ? {
        F: common_vendor.f(selectedQuestion.value.options, (option, index, i0) => {
          return {
            a: common_vendor.t(getOptionLabel(index)),
            b: common_vendor.t(option),
            c: index,
            d: common_vendor.n(getDetailOptionClass(selectedQuestion.value, index))
          };
        })
      } : {}, {
        G: selectedQuestion.value.explanation
      }, selectedQuestion.value.explanation ? {
        H: common_vendor.p({
          name: "book",
          color: "#4A90E2",
          size: "32"
        }),
        I: common_vendor.t(selectedQuestion.value.explanation)
      } : {}) : {}, {
        J: common_vendor.o(($event) => showQuestionDetail.value = $event),
        K: common_vendor.p({
          mode: "bottom",
          height: "80%",
          closeOnClickOverlay: true,
          modelValue: showQuestionDetail.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5dc513bd"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/study/summary.js.map
