{"version": 3, "file": "PermissionWrapper.js", "sources": ["src/components/common/PermissionWrapper.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovcHJvamVjdC9BQ0RDZXhhbS9zcmMvY29tcG9uZW50cy9jb21tb24vUGVybWlzc2lvbldyYXBwZXIudnVl"], "sourcesContent": ["<template>\n  <view class=\"permission-wrapper\">\n    <!-- 有权限时显示内容 -->\n    <slot v-if=\"hasPermission\" />\n    \n    <!-- 无权限时显示提示 -->\n    <view v-else-if=\"showFallback\" class=\"permission-fallback\">\n      <view class=\"fallback-content\">\n        <u-icon :name=\"fallbackIcon\" :color=\"fallbackIconColor\" size=\"80\" />\n        <text class=\"fallback-title\">{{ fallbackTitle }}</text>\n        <text class=\"fallback-message\">{{ fallbackMessage }}</text>\n        <u-button \n          v-if=\"showAction\"\n          type=\"primary\"\n          size=\"normal\"\n          @click=\"handleAction\"\n          class=\"fallback-action\"\n        >\n          {{ actionText }}\n        </u-button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed } from 'vue'\nimport { useUserStore } from '../../stores/user'\nimport { useAppStore } from '../../stores/app'\nimport { permissionManager } from '../../utils/permission'\nimport { PAGE_PATHS } from '../../constants'\n\ninterface Props {\n  // 权限类型\n  permission?: 'auth' | 'authenticated' | 'feature'\n  // 功能名称（当permission为feature时使用）\n  feature?: string\n  // 是否显示无权限提示\n  showFallback?: boolean\n  // 自定义提示信息\n  fallbackTitle?: string\n  fallbackMessage?: string\n  fallbackIcon?: string\n  fallbackIconColor?: string\n  // 是否显示操作按钮\n  showAction?: boolean\n  actionText?: string\n  actionType?: 'login' | 'profile' | 'custom'\n}\n\nconst props = withDefaults(defineProps<Props>(), {\n  permission: 'auth',\n  feature: '',\n  showFallback: true,\n  fallbackTitle: '',\n  fallbackMessage: '',\n  fallbackIcon: 'lock',\n  fallbackIconColor: '#BFBFBF',\n  showAction: true,\n  actionText: '',\n  actionType: 'login'\n})\n\nconst emit = defineEmits<{\n  action: []\n}>()\n\nconst userStore = useUserStore()\nconst appStore = useAppStore()\n\n// 计算是否有权限\nconst hasPermission = computed(() => {\n  switch (props.permission) {\n    case 'auth':\n      return permissionManager.isLoggedIn()\n    case 'authenticated':\n      return permissionManager.isAuthenticated()\n    case 'feature':\n      if (!props.feature) return true\n      return permissionManager.checkFeatureAccess(props.feature).allowed\n    default:\n      return true\n  }\n})\n\n// 计算提示标题\nconst fallbackTitle = computed(() => {\n  if (props.fallbackTitle) return props.fallbackTitle\n  \n  switch (props.permission) {\n    case 'auth':\n      return '需要登录'\n    case 'authenticated':\n      return '需要认证'\n    case 'feature':\n      return '功能受限'\n    default:\n      return '权限不足'\n  }\n})\n\n// 计算提示信息\nconst fallbackMessage = computed(() => {\n  if (props.fallbackMessage) return props.fallbackMessage\n  \n  switch (props.permission) {\n    case 'auth':\n      return '请先登录后使用此功能'\n    case 'authenticated':\n      return permissionManager.getUserStatusMessage()\n    case 'feature':\n      if (props.feature) {\n        const result = permissionManager.checkFeatureAccess(props.feature)\n        return result.message || '您暂时无法使用此功能'\n      }\n      return '您暂时无法使用此功能'\n    default:\n      return '您暂时无法使用此功能'\n  }\n})\n\n// 计算操作按钮文本\nconst actionText = computed(() => {\n  if (props.actionText) return props.actionText\n  \n  switch (props.actionType) {\n    case 'login':\n      return '立即登录'\n    case 'profile':\n      return '完善资料'\n    default:\n      return '了解详情'\n  }\n})\n\n// 处理操作按钮点击\nconst handleAction = () => {\n  switch (props.actionType) {\n    case 'login':\n      appStore.redirectTo(PAGE_PATHS.LOGIN)\n      break\n    case 'profile':\n      if (userStore.userInfo?.status === 'not_submitted') {\n        appStore.redirectTo(PAGE_PATHS.PROFILE)\n      } else {\n        appStore.switchTab(PAGE_PATHS.PERSONAL)\n      }\n      break\n    case 'custom':\n      emit('action')\n      break\n    default:\n      emit('action')\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.permission-wrapper {\n  width: 100%;\n}\n\n.permission-fallback {\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 80rpx 40rpx;\n  min-height: 400rpx;\n}\n\n.fallback-content {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  text-align: center;\n  max-width: 400rpx;\n}\n\n.fallback-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #8A8A8A;\n  margin: 32rpx 0 16rpx;\n}\n\n.fallback-message {\n  font-size: 28rpx;\n  color: #BFBFBF;\n  line-height: 1.5;\n  margin-bottom: 48rpx;\n}\n\n.fallback-action {\n  margin-top: 16rpx;\n}\n</style>\n", "import Component from 'E:/project/ACDCexam/src/components/common/PermissionWrapper.vue'\nwx.createComponent(Component)"], "names": ["useUserStore", "useAppStore", "computed", "permission<PERSON>anager", "PAGE_PATHS"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA,UAAM,QAAQ;AAad,UAAM,OAAO;AAIb,UAAM,YAAYA,gBAAAA;AAClB,UAAM,WAAWC,eAAAA;AAGX,UAAA,gBAAgBC,cAAAA,SAAS,MAAM;AACnC,cAAQ,MAAM,YAAY;AAAA,QACxB,KAAK;AACH,iBAAOC,qBAAAA,kBAAkB;QAC3B,KAAK;AACH,iBAAOA,qBAAAA,kBAAkB;QAC3B,KAAK;AACH,cAAI,CAAC,MAAM;AAAgB,mBAAA;AAC3B,iBAAOA,qBAAkB,kBAAA,mBAAmB,MAAM,OAAO,EAAE;AAAA,QAC7D;AACS,iBAAA;AAAA,MACX;AAAA,IAAA,CACD;AAGK,UAAA,gBAAgBD,cAAAA,SAAS,MAAM;AACnC,UAAI,MAAM;AAAe,eAAO,MAAM;AAEtC,cAAQ,MAAM,YAAY;AAAA,QACxB,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT;AACS,iBAAA;AAAA,MACX;AAAA,IAAA,CACD;AAGK,UAAA,kBAAkBA,cAAAA,SAAS,MAAM;AACrC,UAAI,MAAM;AAAiB,eAAO,MAAM;AAExC,cAAQ,MAAM,YAAY;AAAA,QACxB,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACH,iBAAOC,qBAAAA,kBAAkB;QAC3B,KAAK;AACH,cAAI,MAAM,SAAS;AACjB,kBAAM,SAASA,qBAAA,kBAAkB,mBAAmB,MAAM,OAAO;AACjE,mBAAO,OAAO,WAAW;AAAA,UAC3B;AACO,iBAAA;AAAA,QACT;AACS,iBAAA;AAAA,MACX;AAAA,IAAA,CACD;AAGK,UAAA,aAAaD,cAAAA,SAAS,MAAM;AAChC,UAAI,MAAM;AAAY,eAAO,MAAM;AAEnC,cAAQ,MAAM,YAAY;AAAA,QACxB,KAAK;AACI,iBAAA;AAAA,QACT,KAAK;AACI,iBAAA;AAAA,QACT;AACS,iBAAA;AAAA,MACX;AAAA,IAAA,CACD;AAGD,UAAM,eAAe,MAAM;;AACzB,cAAQ,MAAM,YAAY;AAAA,QACxB,KAAK;AACM,mBAAA,WAAWE,+BAAW,KAAK;AACpC;AAAA,QACF,KAAK;AACC,gBAAA,eAAU,aAAV,mBAAoB,YAAW,iBAAiB;AACzC,qBAAA,WAAWA,+BAAW,OAAO;AAAA,UAAA,OACjC;AACI,qBAAA,UAAUA,+BAAW,QAAQ;AAAA,UACxC;AACA;AAAA,QACF,KAAK;AACH,eAAK,QAAQ;AACb;AAAA,QACF;AACE,eAAK,QAAQ;AAAA,MACjB;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxJF,GAAG,gBAAgB,SAAS;"}