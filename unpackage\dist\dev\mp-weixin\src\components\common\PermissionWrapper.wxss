/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.permission-wrapper.data-v-ba7e4f2b {
  width: 100%;
}
.permission-fallback.data-v-ba7e4f2b {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  min-height: 400rpx;
}
.fallback-content.data-v-ba7e4f2b {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 400rpx;
}
.fallback-title.data-v-ba7e4f2b {
  font-size: 32rpx;
  font-weight: bold;
  color: #8A8A8A;
  margin: 32rpx 0 16rpx;
}
.fallback-message.data-v-ba7e4f2b {
  font-size: 28rpx;
  color: #BFBFBF;
  line-height: 1.5;
  margin-bottom: 48rpx;
}
.fallback-action.data-v-ba7e4f2b {
  margin-top: 16rpx;
}