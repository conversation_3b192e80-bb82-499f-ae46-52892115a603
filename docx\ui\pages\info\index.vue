<template>
  <view class="info-center-container">
    <!-- 状态栏安全区域 -->
    <view class="status-bar" :style="{ height: statusBarHeight + 'px' }"></view>
    
    <!-- 自定义头部 -->
    <view class="header">
      <view class="header-content">
        <text class="page-title">信息中心</text>
        <view class="header-actions">
          <u-icon name="search" color="#fff" size="44" @click="handleSearch" />
        </view>
      </view>
    </view>
    
    <!-- 非正式用户状态提示 -->
    <view v-if="!userStore.isVerified" class="auth-prompt">
      <view class="prompt-card">
        <u-icon name="account" color="#FF9500" size="80" />
        <text class="prompt-title">身份认证{{ getAuthStatusText() }}</text>
        <text class="prompt-desc">{{ getAuthDescription() }}</text>
        <u-button 
          v-if="userStore.authStatus !== 'pending'"
          class="auth-btn" 
          type="primary" 
          size="medium"
          @click="goToAuth"
        >
          {{ getAuthButtonText() }}
        </u-button>
        <view v-else class="pending-status">
          <u-icon name="clock" color="#999" size="32" />
          <text>审核中，请耐心等待</text>
        </view>
      </view>
    </view>
    
    <!-- 正式用户内容 -->
    <view v-else class="main-content">
      <!-- 轮播公告 -->
      <view v-if="bannerList.length > 0" class="banner-section">
        <swiper 
          class="banner-swiper" 
          :indicator-dots="bannerList.length > 1"
          :autoplay="true"
          :interval="5000"
          :duration="500"
          circular
        >
          <swiper-item v-for="banner in bannerList" :key="banner.id">
            <view class="banner-item" @click="viewDetail(banner)">
              <view class="banner-content">
                <text class="banner-title">{{ banner.title }}</text>
                <text class="banner-desc">{{ banner.summary }}</text>
              </view>
              <view class="banner-tag important">重要</view>
            </view>
          </swiper-item>
        </swiper>
      </view>
      
      <!-- 内容分类 -->
      <view class="content-categories">
        <u-tabs 
          v-model="currentTab" 
          :list="tabList" 
          @change="onTabChange"
          activeColor="#4A90E2"
          inactiveColor="#666"
          lineColor="#4A90E2"
          :lineWidth="60"
          :lineHeight="6"
        />
      </view>
      
      <!-- 内容列表 -->
      <view class="content-list">
        <scroll-view 
          class="list-scroll"
          scroll-y
          :refresher-enabled="true"
          :refresher-triggered="isRefreshing"
          @refresherrefresh="onRefresh"
          @scrolltolower="onLoadMore"
        >
          <view v-if="currentList.length === 0 && !isLoading" class="empty-state">
            <u-empty 
              text="暂无内容" 
              icon="https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=200&h=200&fit=crop&crop=center"
              textSize="28"
            />
          </view>
          
          <view v-else>
            <view 
              v-for="item in currentList" 
              :key="item.id" 
              class="content-item"
              @click="viewDetail(item)"
            >
              <view class="item-header">
                <text class="item-title">{{ item.title }}</text>
                <view v-if="item.isImportant" class="important-tag">
                  <text>重要</text>
                </view>
              </view>
              
              <text v-if="item.summary" class="item-summary">{{ item.summary }}</text>
              
              <view class="item-footer">
                <text class="item-date">{{ formatDate(item.publishTime) }}</text>
                <view class="item-actions">
                  <u-icon name="eye" color="#999" size="24" />
                  <text class="view-count">{{ item.viewCount || 0 }}</text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 加载更多提示 -->
          <view v-if="isLoading" class="loading-more">
            <u-loading mode="spinner" size="32" />
            <text class="loading-text">加载中...</text>
          </view>
          
          <view v-if="!hasMore && currentList.length > 0" class="no-more">
            <text>没有更多内容了</text>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/stores/user'

// Store
const userStore = useUserStore()

// 系统信息
const statusBarHeight = ref(0)

// 数据状态
const currentTab = ref(0)
const isLoading = ref(false)
const isRefreshing = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)

// Tab配置
const tabList = [
  { name: '全部', key: 'all' },
  { name: '考试公告', key: 'announcement' },
  { name: '政策法规', key: 'policy' },
  { name: '重要通知', key: 'notice' }
]

// 模拟数据
const bannerList = ref([
  {
    id: 1,
    title: '2024年第二季度任职资格考试通知',
    summary: '请各位医护人员及时关注考试安排，做好备考准备',
    type: 'announcement',
    isImportant: true,
    publishTime: '2024-03-15'
  }
])

const allList = ref([
  {
    id: 1,
    title: '2024年第二季度疾控医护任职资格考试通知',
    summary: '定于2024年4月15日举行第二季度疾控医护人员任职资格考试，请及时报名',
    type: 'announcement',
    isImportant: true,
    publishTime: '2024-03-15',
    viewCount: 1520
  },
  {
    id: 2,
    title: '新版《疫苗接种工作规范》解读',
    summary: '针对最新发布的疫苗接种工作规范进行详细解读，重点关注变化内容',
    type: 'policy',
    isImportant: false,
    publishTime: '2024-03-12',
    viewCount: 856
  },
  {
    id: 3,
    title: '关于调整考试报名时间的紧急通知',
    summary: '因特殊情况，原定的考试报名时间需要调整，请各位考生注意',
    type: 'notice',
    isImportant: true,
    publishTime: '2024-03-10',
    viewCount: 2340
  }
])

// 计算当前列表
const currentList = computed(() => {
  const currentTabKey = tabList[currentTab.value].key
  if (currentTabKey === 'all') {
    return allList.value
  }
  return allList.value.filter(item => item.type === currentTabKey)
})

onMounted(() => {
  // 获取系统信息 - 使用新的API
  try {
    // 优先使用新API
    if (uni.canIUse('getWindowInfo')) {
      const windowInfo = uni.getWindowInfo()
      statusBarHeight.value = windowInfo.statusBarHeight || 0
    } else {
      // 兼容旧版本
      const systemInfo = uni.getSystemInfoSync()
      statusBarHeight.value = systemInfo.statusBarHeight || 0
    }
  } catch (error) {
    console.error('获取系统信息失败:', error)
    statusBarHeight.value = 0
  }

  // 初始化数据
  loadData()
})

// 获取认证状态文本
const getAuthStatusText = () => {
  const statusMap = {
    'not_submitted': '未完成',
    'pending': '审核中',
    'rejected': '未通过'
  }
  return statusMap[userStore.authStatus] || '未完成'
}

// 获取认证描述
const getAuthDescription = () => {
  const descMap = {
    'not_submitted': '请完善个人资料，提交机构审核后可查看完整信息',
    'pending': '您的资料正在审核中，审核通过后可使用完整功能',
    'rejected': '您的资料审核未通过，请重新提交正确的个人信息'
  }
  return descMap[userStore.authStatus] || '请完善个人资料'
}

// 获取认证按钮文字
const getAuthButtonText = () => {
  const textMap = {
    'not_submitted': '去完善资料',
    'rejected': '重新提交资料'
  }
  return textMap[userStore.authStatus] || '去完善资料'
}

// 去认证
const goToAuth = () => {
  uni.navigateTo({
    url: '/pages/profile/submit'
  })
}

// 切换Tab
const onTabChange = (index: number) => {
  currentTab.value = index
  currentPage.value = 1
  hasMore.value = true
  loadData()
}

// 加载数据
const loadData = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 这里应该调用真实的API
    // const response = await getInfoList({
    //   type: tabList[currentTab.value].key,
    //   page: currentPage.value,
    //   pageSize: 10
    // })
    
  } catch (error) {
    console.error('加载数据失败:', error)
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    })
  } finally {
    isLoading.value = false
    isRefreshing.value = false
  }
}

// 下拉刷新
const onRefresh = () => {
  isRefreshing.value = true
  currentPage.value = 1
  hasMore.value = true
  loadData()
}

// 加载更多
const onLoadMore = () => {
  if (hasMore.value && !isLoading.value) {
    currentPage.value++
    loadData()
  }
}

// 查看详情
const viewDetail = (item: any) => {
  uni.navigateTo({
    url: `/pages/info/detail?id=${item.id}&type=${item.type}`
  })
}

// 搜索
const handleSearch = () => {
  uni.showToast({
    title: '搜索功能开发中',
    icon: 'none'
  })
}

// 格式化日期
const formatDate = (dateStr: string) => {
  const date = new Date(dateStr)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (days === 0) {
    return '今天'
  } else if (days === 1) {
    return '昨天'
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return `${date.getMonth() + 1}-${date.getDate()}`
  }
}
</script>

<style lang="scss" scoped>
.info-center-container {
  min-height: 100vh;
  background: #f8f9fa;
}

.status-bar {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
}

.header {
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  padding: 20rpx 30rpx 40rpx;
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    
    .page-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
    }
    
    .header-actions {
      display: flex;
      align-items: center;
    }
  }
}

.auth-prompt {
  padding: 60rpx 30rpx;
  
  .prompt-card {
    background: #fff;
    border-radius: 32rpx;
    padding: 80rpx 40rpx;
    text-align: center;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
    
    .prompt-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin: 40rpx 0 20rpx;
    }
    
    .prompt-desc {
      display: block;
      font-size: 28rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 60rpx;
    }
    
    .auth-btn {
      width: 300rpx;
      height: 80rpx;
      border-radius: 40rpx;
    }
    
    .pending-status {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #999;
      font-size: 28rpx;
      
      text {
        margin-left: 16rpx;
      }
    }
  }
}

.main-content {
  padding-bottom: 120rpx; // 为底部导航留空间
}

.banner-section {
  margin: 30rpx;
  
  .banner-swiper {
    height: 200rpx;
    border-radius: 24rpx;
    overflow: hidden;
    
    .banner-item {
      position: relative;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 40rpx;
      display: flex;
      align-items: center;
      
      .banner-content {
        flex: 1;
        
        .banner-title {
          display: block;
          font-size: 30rpx;
          font-weight: bold;
          color: #fff;
          margin-bottom: 16rpx;
          line-height: 1.4;
        }
        
        .banner-desc {
          font-size: 24rpx;
          color: rgba(255, 255, 255, 0.8);
          line-height: 1.4;
        }
      }
      
      .banner-tag {
        position: absolute;
        top: 20rpx;
        right: 20rpx;
        padding: 8rpx 16rpx;
        background: rgba(255, 149, 0, 0.9);
        border-radius: 12rpx;
        font-size: 20rpx;
        color: #fff;
        
        &.important {
          background: rgba(255, 59, 48, 0.9);
        }
      }
    }
  }
}

.content-categories {
  background: #fff;
  padding: 0 30rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.content-list {
  .list-scroll {
    height: calc(100vh - 400rpx);
  }
  
  .content-item {
    margin: 20rpx 30rpx;
    padding: 40rpx;
    background: #fff;
    border-radius: 24rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
    
    .item-header {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 20rpx;
      
      .item-title {
        flex: 1;
        font-size: 30rpx;
        font-weight: bold;
        color: #333;
        line-height: 1.4;
        margin-right: 20rpx;
      }
      
      .important-tag {
        padding: 8rpx 16rpx;
        background: #fff3e0;
        border-radius: 12rpx;
        border: 2rpx solid #FF9500;
        
        text {
          font-size: 20rpx;
          color: #FF9500;
          font-weight: bold;
        }
      }
    }
    
    .item-summary {
      display: block;
      font-size: 26rpx;
      color: #666;
      line-height: 1.5;
      margin-bottom: 24rpx;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    
    .item-footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      
      .item-date {
        font-size: 24rpx;
        color: #999;
      }
      
      .item-actions {
        display: flex;
        align-items: center;
        
        .view-count {
          font-size: 24rpx;
          color: #999;
          margin-left: 8rpx;
        }
      }
    }
  }
}

.empty-state {
  padding: 120rpx 0;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  
  .loading-text {
    margin-left: 16rpx;
    font-size: 26rpx;
    color: #999;
  }
}

.no-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 26rpx;
}
</style> 