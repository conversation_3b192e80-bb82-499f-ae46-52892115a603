"use strict";
const common_vendor = require("../../common/vendor.js");
const src_stores_study = require("../../src/stores/study.js");
const src_stores_app = require("../../src/stores/app.js");
const src_constants_index = require("../../src/constants/index.js");
const src_utils_index = require("../../src/utils/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_textarea2 = common_vendor.resolveComponent("u-textarea");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  const _easycom_u_popup2 = common_vendor.resolveComponent("u-popup");
  const _easycom_u_modal2 = common_vendor.resolveComponent("u-modal");
  (_easycom_u_navbar2 + _easycom_u_icon2 + _easycom_u_textarea2 + _easycom_u_button2 + _easycom_u_popup2 + _easycom_u_modal2)();
}
const _easycom_u_navbar = () => "../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_textarea = () => "../../uni_modules/uview-plus/components/u-textarea/u-textarea.js";
const _easycom_u_button = () => "../../uni_modules/uview-plus/components/u-button/u-button.js";
const _easycom_u_popup = () => "../../uni_modules/uview-plus/components/u-popup/u-popup.js";
const _easycom_u_modal = () => "../../uni_modules/uview-plus/components/u-modal/u-modal.js";
if (!Math) {
  (_easycom_u_navbar + _easycom_u_icon + StatusTag + _easycom_u_textarea + _easycom_u_button + _easycom_u_popup + _easycom_u_modal)();
}
const StatusTag = () => "../../src/components/common/StatusTag.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "practice",
  props: {
    sessionId: {},
    categoryName: {}
  },
  setup(__props) {
    const studyStore = src_stores_study.useStudyStore();
    const appStore = src_stores_app.useAppStore();
    const props = __props;
    const showAnswerSheet = common_vendor.ref(false);
    const showExitModal = common_vendor.ref(false);
    const elapsedTime = common_vendor.ref(0);
    let timer = null;
    const currentSession = common_vendor.computed(() => studyStore.currentSession);
    const questions = common_vendor.computed(() => {
      var _a;
      return ((_a = currentSession.value) == null ? void 0 : _a.questions) || [];
    });
    const currentQuestionIndex = common_vendor.computed(() => studyStore.currentQuestionIndex);
    const currentQuestion = common_vendor.computed(() => studyStore.currentQuestion);
    const totalQuestions = common_vendor.computed(() => questions.value.length);
    const isLastQuestion = common_vendor.computed(() => currentQuestionIndex.value === totalQuestions.value - 1);
    const progressPercentage = common_vendor.computed(() => {
      if (totalQuestions.value === 0)
        return 0;
      return (currentQuestionIndex.value + 1) / totalQuestions.value * 100;
    });
    const navTitle = common_vendor.computed(() => {
      return props.categoryName || "题库练习";
    });
    const currentAnswer = common_vendor.computed({
      get: () => {
        if (!currentQuestion.value || !currentSession.value)
          return null;
        return currentSession.value.answers[currentQuestion.value.id] || null;
      },
      set: (value) => {
        if (currentQuestion.value) {
          studyStore.answerQuestion(currentQuestion.value.id, value);
        }
      }
    });
    const hasAnswer = common_vendor.computed(() => {
      if (!currentQuestion.value)
        return false;
      const answer = currentAnswer.value;
      if (answer === null || answer === void 0)
        return false;
      if (currentQuestion.value.type === "multiple") {
        return Array.isArray(answer) && answer.length > 0;
      }
      if (currentQuestion.value.type === "essay") {
        return typeof answer === "string" && answer.trim().length > 0;
      }
      return true;
    });
    const isChoiceQuestion = common_vendor.computed(() => {
      var _a, _b;
      return ((_a = currentQuestion.value) == null ? void 0 : _a.type) === "single" || ((_b = currentQuestion.value) == null ? void 0 : _b.type) === "multiple";
    });
    common_vendor.onMounted(() => {
      if (!currentSession.value || currentSession.value.id !== props.sessionId) {
        appStore.showToast("练习会话不存在");
        appStore.navigateBack();
        return;
      }
      startTimer();
    });
    common_vendor.onUnmounted(() => {
      if (timer) {
        clearInterval(timer);
      }
    });
    const startTimer = () => {
      timer = setInterval(() => {
        elapsedTime.value++;
      }, 1e3);
    };
    const formatTime = (seconds) => {
      return src_utils_index.formatDuration(seconds);
    };
    const getOptionLabel = (index) => {
      return String.fromCharCode(65 + index);
    };
    const getOptionClass = (index) => {
      const classes = ["option"];
      if (isOptionSelected(index)) {
        classes.push("selected");
      }
      return classes.join(" ");
    };
    const isOptionSelected = (index) => {
      if (!currentQuestion.value)
        return false;
      const answer = currentAnswer.value;
      if (currentQuestion.value.type === "multiple") {
        return Array.isArray(answer) && answer.includes(index);
      } else {
        return answer === index;
      }
    };
    const selectOption = (index) => {
      if (!currentQuestion.value)
        return;
      if (currentQuestion.value.type === "multiple") {
        let answer = Array.isArray(currentAnswer.value) ? [...currentAnswer.value] : [];
        const selectedIndex = answer.indexOf(index);
        if (selectedIndex > -1) {
          answer.splice(selectedIndex, 1);
        } else {
          answer.push(index);
        }
        currentAnswer.value = answer;
      } else {
        currentAnswer.value = index;
      }
    };
    const selectJudge = (value) => {
      currentAnswer.value = value;
    };
    const getDifficultyClass = (difficulty) => {
      const classMap = {
        easy: "difficulty-easy",
        medium: "difficulty-medium",
        hard: "difficulty-hard"
      };
      return classMap[difficulty] || "difficulty-medium";
    };
    const getDifficultyText = (difficulty) => {
      const textMap = {
        easy: "基础",
        medium: "进阶",
        hard: "高级"
      };
      return textMap[difficulty] || "进阶";
    };
    const prevQuestion = () => {
      if (currentQuestionIndex.value > 0) {
        jumpToQuestion(currentQuestionIndex.value - 1);
      }
    };
    const nextQuestion = () => {
      if (isLastQuestion.value) {
        finishPractice();
      } else {
        jumpToQuestion(currentQuestionIndex.value + 1);
      }
    };
    const jumpToQuestion = (index) => {
      showAnswerSheet.value = false;
    };
    const getAnswerItemClass = (index) => {
      var _a, _b;
      const classes = ["answer-item"];
      if (index === currentQuestionIndex.value) {
        classes.push("current");
      } else if ((_b = currentSession.value) == null ? void 0 : _b.answers[(_a = questions.value[index]) == null ? void 0 : _a.id]) {
        classes.push("answered");
      } else {
        classes.push("unanswered");
      }
      return classes.join(" ");
    };
    const finishPractice = () => {
      if (!currentSession.value)
        return;
      if (timer) {
        clearInterval(timer);
        timer = null;
      }
      const completedSession = studyStore.completeSession();
      if (completedSession) {
        appStore.redirectTo(src_constants_index.PAGE_PATHS.STUDY_SUMMARY, {
          sessionId: completedSession.id
        });
      }
    };
    const handleBack = () => {
      showExitModal.value = true;
    };
    const confirmExit = () => {
      studyStore.clearCurrentSession();
      if (timer) {
        clearInterval(timer);
        timer = null;
      }
      appStore.navigateBack();
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(handleBack),
        b: common_vendor.p({
          title: navTitle.value,
          autoBack: true,
          background: {
            background: "linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        c: common_vendor.t(currentQuestionIndex.value + 1),
        d: common_vendor.t(totalQuestions.value),
        e: progressPercentage.value + "%",
        f: common_vendor.p({
          name: "clock",
          color: "#666",
          size: "24"
        }),
        g: common_vendor.t(formatTime(elapsedTime.value)),
        h: currentQuestion.value
      }, currentQuestion.value ? common_vendor.e({
        i: common_vendor.p({
          type: "question",
          status: currentQuestion.value.type
        }),
        j: currentQuestion.value.difficulty
      }, currentQuestion.value.difficulty ? {
        k: common_vendor.t(getDifficultyText(currentQuestion.value.difficulty)),
        l: common_vendor.n(getDifficultyClass(currentQuestion.value.difficulty))
      } : {}, {
        m: common_vendor.t(currentQuestion.value.title),
        n: isChoiceQuestion.value
      }, isChoiceQuestion.value ? {
        o: common_vendor.f(currentQuestion.value.options, (option, index, i0) => {
          return common_vendor.e({
            a: common_vendor.t(getOptionLabel(index)),
            b: isOptionSelected(index)
          }, isOptionSelected(index) ? {
            c: "17cd85ad-3-" + i0,
            d: common_vendor.p({
              name: currentQuestion.value.type === "multiple" ? "checkbox-mark" : "checkmark-circle-fill",
              color: "#4A90E2",
              size: "32"
            })
          } : {}, {
            e: common_vendor.t(option),
            f: index,
            g: common_vendor.n(getOptionClass(index)),
            h: common_vendor.o(($event) => selectOption(index), index)
          });
        })
      } : currentQuestion.value.type === "judge" ? {
        q: common_vendor.p({
          name: currentAnswer.value === true ? "checkmark-circle-fill" : "checkmark-circle",
          color: currentAnswer.value === true ? "#4CAF50" : "#ccc",
          size: "48"
        }),
        r: currentAnswer.value === true ? 1 : "",
        s: common_vendor.o(($event) => selectJudge(true)),
        t: common_vendor.p({
          name: currentAnswer.value === false ? "close-circle-fill" : "close-circle",
          color: currentAnswer.value === false ? "#f56c6c" : "#ccc",
          size: "48"
        }),
        v: currentAnswer.value === false ? 1 : "",
        w: common_vendor.o(($event) => selectJudge(false))
      } : currentQuestion.value.type === "essay" ? {
        y: common_vendor.o(($event) => currentAnswer.value = $event),
        z: common_vendor.p({
          placeholder: "请输入您的答案...",
          maxlength: 500,
          showWordLimit: true,
          height: "300",
          autoHeight: true,
          modelValue: currentAnswer.value
        })
      } : {}, {
        p: currentQuestion.value.type === "judge",
        x: currentQuestion.value.type === "essay"
      }) : {}, {
        A: currentQuestionIndex.value > 0
      }, currentQuestionIndex.value > 0 ? {
        B: common_vendor.o(prevQuestion),
        C: common_vendor.p({
          type: "info",
          plain: true
        })
      } : {}, {
        D: common_vendor.t(isLastQuestion.value ? "完成练习" : "下一题"),
        E: common_vendor.o(nextQuestion),
        F: common_vendor.p({
          type: "primary",
          disabled: !hasAnswer.value
        }),
        G: common_vendor.p({
          name: "grid",
          color: "#4A90E2",
          size: "32"
        }),
        H: common_vendor.o(($event) => showAnswerSheet.value = true),
        I: common_vendor.o(($event) => showAnswerSheet.value = false),
        J: common_vendor.p({
          name: "close",
          size: "32"
        }),
        K: common_vendor.f(questions.value, (question, index, i0) => {
          return {
            a: common_vendor.t(index + 1),
            b: question.id,
            c: common_vendor.n(getAnswerItemClass(index)),
            d: common_vendor.o(($event) => jumpToQuestion(), question.id)
          };
        }),
        L: common_vendor.o(($event) => showAnswerSheet.value = $event),
        M: common_vendor.p({
          mode: "bottom",
          height: "60%",
          closeOnClickOverlay: true,
          modelValue: showAnswerSheet.value
        }),
        N: common_vendor.o(confirmExit),
        O: common_vendor.o(($event) => showExitModal.value = false),
        P: common_vendor.o(($event) => showExitModal.value = $event),
        Q: common_vendor.p({
          title: "确认退出",
          content: "退出后当前练习进度将丢失，确定要退出吗？",
          showCancelButton: true,
          modelValue: showExitModal.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-17cd85ad"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/study/practice.js.map
