{"version": 3, "file": "u-overlay.js", "sources": ["uni_modules/uview-plus/components/u-overlay/u-overlay.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniComponent:/RTovcHJvamVjdC9BQ0RDZXhhbS91bmlfbW9kdWxlcy91dmlldy1wbHVzL2NvbXBvbmVudHMvdS1vdmVybGF5L3Utb3ZlcmxheS52dWU"], "sourcesContent": ["<template>\n\t<u-transition\n\t    :show=\"show\"\n\t    custom-class=\"u-overlay\"\n\t    :duration=\"duration\"\n\t    :custom-style=\"overlayStyle\"\n\t    @click=\"clickHandler\"\n\t\************************=\"noop\"\n\t>\n\t\t<slot />\n\t</u-transition>\n</template>\n\n<script>\n\timport { props } from './props';\n\timport { mpMixin } from '../../libs/mixin/mpMixin';\n\timport { mixin } from '../../libs/mixin/mixin';\n\timport { addStyle, deepMerge } from '../../libs/function/index';\n\t/**\n\t * overlay 遮罩\n\t * @description 创建一个遮罩层，用于强调特定的页面元素，并阻止用户对遮罩下层的内容进行操作，一般用于弹窗场景\n\t * @tutorial https://ijry.github.io/uview-plus/components/overlay.html\n\t * @property {Boolean}\t\t\tshow\t\t是否显示遮罩（默认 false ）\n\t * @property {String | Number}\tzIndex\t\tzIndex 层级（默认 10070 ）\n\t * @property {String | Number}\tduration\t动画时长，单位毫秒（默认 300 ）\n\t * @property {String | Number}\topacity\t\t不透明度值，当做rgba的第四个参数 （默认 0.5 ）\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\n\t * @event {Function} click 点击遮罩发送事件\n\t * @example <u-overlay :show=\"show\" @click=\"show = false\"></u-overlay>\n\t */\n\texport default {\n\t\tname: \"u-overlay\",\n\t\tmixins: [mpMixin, mixin,props],\n\t\tcomputed: {\n\t\t\toverlayStyle() {\n\t\t\t\tconst style = {\n\t\t\t\t\tposition: 'fixed',\n\t\t\t\t\ttop: 0,\n\t\t\t\t\tleft: 0,\n\t\t\t\t\tright: 0,\n\t\t\t\t\tzIndex: this.zIndex,\n\t\t\t\t\tbottom: 0,\n\t\t\t\t\t'background-color': `rgba(0, 0, 0, ${this.opacity})`\n\t\t\t\t}\n\t\t\t\treturn deepMerge(style, addStyle(this.customStyle))\n\t\t\t}\n\t\t},\n\t\temits: [\"click\"],\n\t\tmethods: {\n\t\t\tclickHandler() {\n\t\t\t\tthis.$emit('click')\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n     $u-overlay-top:0 !default;\n     $u-overlay-left:0 !default;\n     $u-overlay-width:100% !default;\n     $u-overlay-height:100% !default;\n     $u-overlay-background-color:rgba(0, 0, 0, .7) !default;\n\t.u-overlay {\n\t\tposition: fixed;\n\t\ttop:$u-overlay-top;\n\t\tleft:$u-overlay-left;\n\t\twidth: $u-overlay-width;\n\t\theight:$u-overlay-height;\n\t\tbackground-color:$u-overlay-background-color;\n\t}\n</style>\n", "import Component from 'E:/project/ACDCexam/uni_modules/uview-plus/components/u-overlay/u-overlay.vue'\nwx.createComponent(Component)"], "names": ["mpMixin", "mixin", "props", "deepMerge", "addStyle"], "mappings": ";;;;;;AA8BC,MAAK,YAAU;AAAA,EACd,MAAM;AAAA,EACN,QAAQ,CAACA,yCAAAA,SAASC,uCAAK,OAACC,qDAAK;AAAA,EAC7B,UAAU;AAAA,IACT,eAAe;AACd,YAAM,QAAQ;AAAA,QACb,UAAU;AAAA,QACV,KAAK;AAAA,QACL,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ,KAAK;AAAA,QACb,QAAQ;AAAA,QACR,oBAAoB,iBAAiB,KAAK,OAAO;AAAA,MAClD;AACA,aAAOC,0CAAS,UAAC,OAAOC,0CAAQ,SAAC,KAAK,WAAW,CAAC;AAAA,IACnD;AAAA,EACA;AAAA,EACD,OAAO,CAAC,OAAO;AAAA,EACf,SAAS;AAAA,IACR,eAAe;AACd,WAAK,MAAM,OAAO;AAAA,IACnB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;ACpDD,GAAG,gBAAgB,SAAS;"}