{"version": 3, "file": "gridItem.js", "sources": ["uni_modules/uview-plus/components/u-grid-item/gridItem.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:06:13\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/gridItem.js\n */\nexport default {\n    // grid-item组件\n    gridItem: {\n        name: null,\n        bgColor: 'transparent'\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,WAAA;AAAA;AAAA,EAEX,UAAU;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,EACZ;AACL;;"}