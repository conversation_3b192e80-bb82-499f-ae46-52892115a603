{"version": 3, "file": "detail.js", "sources": ["pages/info/detail.vue", "C:/Users/<USER>/Downloads/HBuilderX.4.66.2025051912/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvaW5mby9kZXRhaWwudnVl"], "sourcesContent": ["<template>\n  <view class=\"detail-container\">\n    <!-- 自定义导航栏 -->\n    <u-navbar \n      title=\"详情\" \n      :autoBack=\"true\"\n      :background=\"{ background: 'linear-gradient(135deg, #2E8B57 0%, #228B22 100%)' }\"\n      titleStyle=\"color: #fff; font-weight: bold;\"\n    />\n    \n    <!-- 加载状态 -->\n    <LoadingSpinner v-if=\"isLoading\" overlay text=\"加载中...\" />\n    \n    <!-- 错误状态 -->\n    <EmptyState \n      v-else-if=\"error\"\n      type=\"no-network\"\n      :title=\"error\"\n      description=\"请检查网络连接后重试\"\n      :showButton=\"true\"\n      buttonText=\"重新加载\"\n      @buttonClick=\"loadDetail\"\n    />\n    \n    <!-- 详情内容 -->\n    <view v-else-if=\"detail\" class=\"detail-content\">\n      <!-- 文章头部 -->\n      <view class=\"article-header\">\n        <view class=\"header-tags\">\n          <StatusTag :type=\"'announcement'\" :status=\"detail.type\" />\n          <view v-if=\"detail.isImportant\" class=\"important-tag\">\n            <u-icon name=\"warning\" color=\"#FF9500\" size=\"24\" />\n            <text>重要</text>\n          </view>\n          <view v-if=\"detail.isTop\" class=\"top-tag\">\n            <u-icon name=\"arrow-up\" color=\"#52C41A\" size=\"24\" />\n            <text>置顶</text>\n          </view>\n        </view>\n        \n        <text class=\"article-title\">{{ detail.title }}</text>\n        \n        <view class=\"article-meta\">\n          <view class=\"meta-item\">\n            <u-icon name=\"calendar\" color=\"#999\" size=\"28\" />\n            <text>{{ formatDate(detail.publishTime, 'YYYY-MM-DD HH:mm') }}</text>\n          </view>\n          <view v-if=\"detail.source\" class=\"meta-item\">\n            <u-icon name=\"home\" color=\"#999\" size=\"28\" />\n            <text>{{ detail.source }}</text>\n          </view>\n        </view>\n      </view>\n      \n      <!-- 分割线 -->\n      <view class=\"divider\"></view>\n      \n      <!-- 文章内容 -->\n      <view class=\"article-body\">\n        <rich-text :nodes=\"formattedContent\" class=\"rich-content\" />\n      </view>\n      \n      <!-- 底部操作 -->\n      <view class=\"article-actions\">\n        <view class=\"action-item\" @click=\"handleShare\">\n          <u-icon name=\"share\" color=\"#666\" size=\"32\" />\n          <text>分享</text>\n        </view>\n        <view class=\"action-item\" @click=\"handleCollect\">\n          <u-icon :name=\"isCollected ? 'heart-fill' : 'heart'\" :color=\"isCollected ? '#F5222D' : '#666'\" size=\"32\" />\n          <text>{{ isCollected ? '已收藏' : '收藏' }}</text>\n        </view>\n        <view class=\"action-item\" @click=\"handleFeedback\">\n          <u-icon name=\"chat\" color=\"#666\" size=\"32\" />\n          <text>反馈</text>\n        </view>\n      </view>\n    </view>\n    \n    <!-- 相关推荐 -->\n    <view v-if=\"relatedList.length > 0\" class=\"related-section\">\n      <view class=\"section-title\">\n        <text>相关推荐</text>\n      </view>\n      \n      <view class=\"related-list\">\n        <view \n          v-for=\"item in relatedList\" \n          :key=\"item.id\"\n          class=\"related-item\"\n          @click=\"viewRelated(item)\"\n        >\n          <text class=\"related-title\">{{ item.title }}</text>\n          <text class=\"related-date\">{{ formatRelativeTime(item.publishTime) }}</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, computed, onMounted } from 'vue'\nimport { useAppStore } from '../../src/stores/app'\nimport api from '../../src/api'\nimport { formatDate, formatRelativeTime } from '../../src/utils'\nimport type { Announcement } from '../../src/types'\n\n// 导入组件\nimport LoadingSpinner from '../../src/components/common/LoadingSpinner.vue'\nimport EmptyState from '../../src/components/common/EmptyState.vue'\nimport StatusTag from '../../src/components/common/StatusTag.vue'\n\n// Store\nconst appStore = useAppStore()\n\n// 页面参数\nconst props = defineProps<{\n  id: string\n}>()\n\n// 响应式数据\nconst isLoading = ref(true)\nconst error = ref('')\nconst detail = ref<Announcement | null>(null)\nconst relatedList = ref<Announcement[]>([])\nconst isCollected = ref(false)\n\n// 格式化内容\nconst formattedContent = computed(() => {\n  if (!detail.value?.content) return ''\n  \n  // 简单的HTML内容处理\n  let content = detail.value.content\n  \n  // 替换换行符\n  content = content.replace(/\\n/g, '<br/>')\n  \n  // 添加段落样式\n  content = content.replace(/(<br\\/>){2,}/g, '</p><p>')\n  content = `<p>${content}</p>`\n  \n  return content\n})\n\nonMounted(() => {\n  loadDetail()\n  loadRelated()\n})\n\n// 加载详情\nconst loadDetail = async () => {\n  if (!props.id) {\n    error.value = '参数错误'\n    isLoading.value = false\n    return\n  }\n  \n  isLoading.value = true\n  error.value = ''\n  \n  try {\n    const response = await api.info.getAnnouncementDetail(props.id)\n    detail.value = response.data\n    \n    // 检查是否已收藏（这里可以调用收藏状态API）\n    // const collectResponse = await api.info.getCollectStatus(props.id)\n    // isCollected.value = collectResponse.data.isCollected\n  } catch (err: any) {\n    uni.__f__('error','at pages/info/detail.vue:169','加载详情失败:', err)\n    error.value = err.message || '加载失败'\n  } finally {\n    isLoading.value = false\n  }\n}\n\n// 加载相关推荐\nconst loadRelated = async () => {\n  try {\n    const response = await api.info.getAnnouncements({\n      page: 1,\n      pageSize: 5,\n      type: detail.value?.type\n    })\n    \n    // 过滤掉当前文章\n    relatedList.value = response.data.list.filter(item => item.id !== props.id)\n  } catch (error) {\n    uni.__f__('error','at pages/info/detail.vue:188','加载相关推荐失败:', error)\n  }\n}\n\n// 查看相关文章\nconst viewRelated = (item: Announcement) => {\n  // 替换当前页面\n  uni.redirectTo({\n    url: `/pages/info/detail?id=${item.id}`\n  })\n}\n\n// 分享\nconst handleShare = () => {\n  if (!detail.value) return\n  \n  uni.share({\n    provider: 'weixin',\n    scene: 'WXSceneSession',\n    type: 0,\n    href: '', // 这里应该是文章的分享链接\n    title: detail.value.title,\n    summary: detail.value.content.substring(0, 100),\n    imageUrl: '', // 分享图片\n    success: () => {\n      appStore.showToast('分享成功', 'success')\n    },\n    fail: (err) => {\n      uni.__f__('error','at pages/info/detail.vue:216','分享失败:', err)\n      appStore.showToast('分享失败')\n    }\n  })\n}\n\n// 收藏/取消收藏\nconst handleCollect = async () => {\n  if (!detail.value) return\n  \n  try {\n    if (isCollected.value) {\n      // 取消收藏\n      // await api.info.uncollect(detail.value.id)\n      isCollected.value = false\n      appStore.showToast('已取消收藏', 'success')\n    } else {\n      // 收藏\n      // await api.info.collect(detail.value.id)\n      isCollected.value = true\n      appStore.showToast('收藏成功', 'success')\n    }\n  } catch (error: any) {\n    appStore.showToast(error.message || '操作失败')\n  }\n}\n\n// 反馈\nconst handleFeedback = () => {\n  appStore.showModal({\n    title: '问题反馈',\n    content: '如果您发现内容有误或有其他问题，请联系我们进行反馈。',\n    confirmText: '联系客服',\n    cancelText: '取消'\n  }).then((confirmed) => {\n    if (confirmed) {\n      // 跳转到反馈页面或联系客服\n      appStore.showToast('功能开发中')\n    }\n  })\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '../../src/styles/global.scss';\n\n.detail-container {\n  min-height: 100vh;\n  background: $acdc-bg-primary;\n}\n\n.detail-content {\n  background: #fff;\n  margin: 24rpx;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n}\n\n.article-header {\n  padding: 40rpx;\n  \n  .header-tags {\n    display: flex;\n    align-items: center;\n    gap: 16rpx;\n    margin-bottom: 24rpx;\n    \n    .important-tag,\n    .top-tag {\n      display: flex;\n      align-items: center;\n      gap: 8rpx;\n      padding: 8rpx 16rpx;\n      border-radius: 20rpx;\n      font-size: 24rpx;\n      \n      text {\n        font-weight: bold;\n      }\n    }\n    \n    .important-tag {\n      background: #fff7e6;\n      color: $uni-color-warning;\n    }\n    \n    .top-tag {\n      background: #f6ffed;\n      color: $uni-color-success;\n    }\n  }\n  \n  .article-title {\n    display: block;\n    font-size: 36rpx;\n    font-weight: bold;\n    color: $acdc-text-primary;\n    line-height: 1.4;\n    margin-bottom: 32rpx;\n  }\n  \n  .article-meta {\n    display: flex;\n    flex-direction: column;\n    gap: 16rpx;\n    \n    .meta-item {\n      display: flex;\n      align-items: center;\n      gap: 12rpx;\n      \n      text {\n        font-size: 26rpx;\n        color: $acdc-text-secondary;\n      }\n    }\n  }\n}\n\n.divider {\n  height: 2rpx;\n  background: $acdc-divider-color;\n  margin: 0 40rpx;\n}\n\n.article-body {\n  padding: 40rpx;\n  \n  .rich-content {\n    font-size: 30rpx;\n    line-height: 1.8;\n    color: $acdc-text-primary;\n    \n    :deep(p) {\n      margin-bottom: 24rpx;\n      text-align: justify;\n    }\n    \n    :deep(img) {\n      max-width: 100%;\n      height: auto;\n      border-radius: 8rpx;\n      margin: 16rpx 0;\n    }\n    \n    :deep(strong) {\n      font-weight: bold;\n      color: $acdc-primary;\n    }\n  }\n}\n\n.article-actions {\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  padding: 32rpx 40rpx;\n  border-top: 2rpx solid $acdc-divider-color;\n  \n  .action-item {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    gap: 12rpx;\n    padding: 16rpx;\n    \n    text {\n      font-size: 24rpx;\n      color: $acdc-text-secondary;\n    }\n  }\n}\n\n.related-section {\n  margin: 24rpx;\n  background: #fff;\n  border-radius: 24rpx;\n  overflow: hidden;\n  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n  \n  .section-title {\n    padding: 32rpx 40rpx 24rpx;\n    border-bottom: 2rpx solid $acdc-divider-color;\n    \n    text {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: $acdc-text-primary;\n    }\n  }\n  \n  .related-list {\n    padding: 24rpx 40rpx 40rpx;\n    \n    .related-item {\n      display: flex;\n      justify-content: space-between;\n      align-items: flex-start;\n      padding: 24rpx 0;\n      border-bottom: 1rpx solid $acdc-divider-color;\n      \n      &:last-child {\n        border-bottom: none;\n      }\n      \n      .related-title {\n        flex: 1;\n        font-size: 28rpx;\n        color: $acdc-text-primary;\n        line-height: 1.4;\n        margin-right: 24rpx;\n        \n        overflow: hidden;\n        text-overflow: ellipsis;\n        display: -webkit-box;\n        -webkit-line-clamp: 2;\n        -webkit-box-orient: vertical;\n      }\n      \n      .related-date {\n        font-size: 24rpx;\n        color: $acdc-text-disabled;\n        white-space: nowrap;\n      }\n    }\n  }\n}\n</style>\n", "import MiniProgramPage from 'E:/project/ACDCexam/pages/info/detail.vue'\nwx.createPage(MiniProgramPage)"], "names": ["useAppStore", "ref", "computed", "onMounted", "api", "uni", "error"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4GA,MAAA,iBAA2B,MAAA;AAC3B,MAAA,aAAuB,MAAA;AACvB,MAAA,YAAsB,MAAA;;;;;;;AAGtB,UAAM,WAAWA,eAAAA;AAGjB,UAAM,QAAQ;AAKR,UAAA,YAAYC,kBAAI,IAAI;AACpB,UAAA,QAAQA,kBAAI,EAAE;AACd,UAAA,SAASA,kBAAyB,IAAI;AACtC,UAAA,cAAcA,kBAAoB,CAAA,CAAE;AACpC,UAAA,cAAcA,kBAAI,KAAK;AAGvB,UAAA,mBAAmBC,cAAAA,SAAS,MAAM;;AAClC,UAAA,GAAC,YAAO,UAAP,mBAAc;AAAgB,eAAA;AAG/B,UAAA,UAAU,OAAO,MAAM;AAGjB,gBAAA,QAAQ,QAAQ,OAAO,OAAO;AAG9B,gBAAA,QAAQ,QAAQ,iBAAiB,SAAS;AACpD,gBAAU,MAAM,OAAO;AAEhB,aAAA;AAAA,IAAA,CACR;AAEDC,kBAAAA,UAAU,MAAM;AACH;AACC;IAAA,CACb;AAGD,UAAM,aAAa,MAAY;AACzB,UAAA,CAAC,MAAM,IAAI;AACb,cAAM,QAAQ;AACd,kBAAU,QAAQ;AAClB;AAAA,MACF;AAEA,gBAAU,QAAQ;AAClB,YAAM,QAAQ;AAEV,UAAA;AACF,cAAM,WAAW,MAAMC,kBAAI,KAAK,sBAAsB,MAAM,EAAE;AAC9D,eAAO,QAAQ,SAAS;AAAA,eAKjB,KAAU;AACjBC,sBAAA,MAAI,MAAM,SAAQ,gCAA+B,WAAW,GAAG;AACzD,cAAA,QAAQ,IAAI,WAAW;AAAA,MAAA,UAC7B;AACA,kBAAU,QAAQ;AAAA,MACpB;AAAA,IAAA;AAIF,UAAM,cAAc,MAAY;;AAC1B,UAAA;AACF,cAAM,WAAW,MAAMD,kBAAI,KAAK,iBAAiB;AAAA,UAC/C,MAAM;AAAA,UACN,UAAU;AAAA,UACV,OAAM,YAAO,UAAP,mBAAc;AAAA,QAAA,CACrB;AAGW,oBAAA,QAAQ,SAAS,KAAK,KAAK,OAAO,CAAQ,SAAA,KAAK,OAAO,MAAM,EAAE;AAAA,eACnEE,QAAO;AACdD,sBAAA,MAAI,MAAM,SAAQ,gCAA+B,aAAaC,MAAK;AAAA,MACrE;AAAA,IAAA;AAII,UAAA,cAAc,CAAC,SAAuB;AAE1CD,oBAAAA,MAAI,WAAW;AAAA,QACb,KAAK,yBAAyB,KAAK,EAAE;AAAA,MAAA,CACtC;AAAA,IAAA;AAIH,UAAM,cAAc,MAAM;AACxB,UAAI,CAAC,OAAO;AAAO;AAEnBA,oBAAAA,MAAI,MAAM;AAAA,QACR,UAAU;AAAA,QACV,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA;AAAA,QACN,OAAO,OAAO,MAAM;AAAA,QACpB,SAAS,OAAO,MAAM,QAAQ,UAAU,GAAG,GAAG;AAAA,QAC9C,UAAU;AAAA;AAAA,QACV,SAAS,MAAM;AACJ,mBAAA,UAAU,QAAQ,SAAS;AAAA,QACtC;AAAA,QACA,MAAM,CAAC,QAAQ;AACbA,wBAAA,MAAI,MAAM,SAAQ,gCAA+B,SAAS,GAAG;AAC7D,mBAAS,UAAU,MAAM;AAAA,QAC3B;AAAA,MAAA,CACD;AAAA,IAAA;AAIH,UAAM,gBAAgB,MAAY;AAChC,UAAI,CAAC,OAAO;AAAO;AAEf,UAAA;AACF,YAAI,YAAY,OAAO;AAGrB,sBAAY,QAAQ;AACX,mBAAA,UAAU,SAAS,SAAS;AAAA,QAAA,OAChC;AAGL,sBAAY,QAAQ;AACX,mBAAA,UAAU,QAAQ,SAAS;AAAA,QACtC;AAAA,eACOC,QAAY;AACV,iBAAA,UAAUA,OAAM,WAAW,MAAM;AAAA,MAC5C;AAAA,IAAA;AAIF,UAAM,iBAAiB,MAAM;AAC3B,eAAS,UAAU;AAAA,QACjB,OAAO;AAAA,QACP,SAAS;AAAA,QACT,aAAa;AAAA,QACb,YAAY;AAAA,MAAA,CACb,EAAE,KAAK,CAAC,cAAc;AACrB,YAAI,WAAW;AAEb,mBAAS,UAAU,OAAO;AAAA,QAC5B;AAAA,MAAA,CACD;AAAA,IAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7PH,GAAG,WAAW,eAAe;"}