{"version": 3, "file": "code.js", "sources": ["uni_modules/uview-plus/components/u-code/code.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:55:27\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/code.js\n */\n\nexport default {\n    // code 组件\n    code: {\n        seconds: 60,\n        startText: '获取验证码',\n        changeText: 'X秒重新获取',\n        endText: '重新获取',\n        keepRunning: false,\n        uniqueKey: ''\n    }\n}\n"], "names": [], "mappings": ";AAUA,MAAe,OAAA;AAAA;AAAA,EAEX,MAAM;AAAA,IACF,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,SAAS;AAAA,IACT,aAAa;AAAA,IACb,WAAW;AAAA,EACd;AACL;;"}