"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../../common/vendor.js");
const src_stores_app = require("../../../src/stores/app.js");
const src_constants_index = require("../../../src/constants/index.js");
const src_api_index = require("../../../src/api/index.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_checkbox2 = common_vendor.resolveComponent("u-checkbox");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_navbar2 + _easycom_u_icon2 + _easycom_u_checkbox2 + _easycom_u_button2)();
}
const _easycom_u_navbar = () => "../../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_checkbox = () => "../../../uni_modules/uview-plus/components/u-checkbox/u-checkbox.js";
const _easycom_u_button = () => "../../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_navbar + _easycom_u_icon + _easycom_u_checkbox + _easycom_u_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "reading",
  props: {
    examId: {}
  },
  setup(__props) {
    const appStore = src_stores_app.useAppStore();
    const props = __props;
    const examInfo = common_vendor.ref(null);
    const hasRead = common_vendor.ref(false);
    const isStarting = common_vendor.ref(false);
    const networkStatus = common_vendor.ref(true);
    const batteryLevel = common_vendor.ref(100);
    const cameraPermission = common_vendor.ref(false);
    const canStartExam = common_vendor.computed(() => {
      return networkStatus.value && batteryLevel.value >= 20 && cameraPermission.value;
    });
    common_vendor.onMounted(() => {
      loadExamInfo();
      checkDeviceStatus();
    });
    const loadExamInfo = () => __async(this, null, function* () {
      try {
        const response = yield src_api_index.api.exam.getExamDetail(props.examId);
        examInfo.value = response.data;
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/exam/online/reading.vue:218", "加载考试信息失败:", error);
        appStore.showToast(error.message || "加载失败");
        appStore.navigateBack();
      }
    });
    const checkDeviceStatus = () => __async(this, null, function* () {
      try {
        const networkInfo = yield common_vendor.index.getNetworkType();
        networkStatus.value = networkInfo[1].networkType !== "none";
        const batteryInfo = yield common_vendor.index.getBatteryInfo();
        batteryLevel.value = batteryInfo[1].level;
        const authResult = yield common_vendor.index.authorize({
          scope: "scope.camera"
        });
        cameraPermission.value = true;
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/exam/online/reading.vue:241", "设备状态检查失败:", error);
        try {
          yield common_vendor.index.authorize({
            scope: "scope.camera"
          });
          cameraPermission.value = true;
        } catch (authError) {
          cameraPermission.value = false;
          appStore.showModal({
            title: "需要摄像头权限",
            content: "考试需要使用摄像头进行人脸识别验证，请在设置中开启摄像头权限。",
            confirmText: "去设置",
            cancelText: "取消"
          }).then((confirmed) => {
            if (confirmed) {
              common_vendor.index.openSetting();
            }
          });
        }
      }
    });
    const startExam = () => __async(this, null, function* () {
      if (!hasRead.value) {
        appStore.showToast("请先阅读并同意考试规则");
        return;
      }
      if (!canStartExam.value) {
        appStore.showToast("设备状态不满足考试要求");
        return;
      }
      isStarting.value = true;
      try {
        const examDetail = yield src_api_index.api.exam.getExamDetail(props.examId);
        const exam = examDetail.data;
        if (exam.status !== "not_started" && exam.status !== "in_progress") {
          appStore.showToast("考试已结束或不在考试时间内");
          appStore.navigateBack();
          return;
        }
        appStore.redirectTo(src_constants_index.PAGE_PATHS.EXAM_ONLINE_FACE_VERIFY, {
          examId: props.examId
        });
      } catch (error) {
        common_vendor.index.__f__("error", "at subpages/exam/online/reading.vue:294", "开始考试失败:", error);
        appStore.showToast(error.message || "开始考试失败，请重试");
      } finally {
        isStarting.value = false;
      }
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          title: "考前须知",
          autoBack: true,
          background: {
            background: "linear-gradient(135deg, #4A90E2 0%, #357ABD 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: examInfo.value
      }, examInfo.value ? {
        c: common_vendor.t(examInfo.value.name),
        d: common_vendor.p({
          name: "clock",
          color: "#4A90E2",
          size: "32"
        }),
        e: common_vendor.t(examInfo.value.duration),
        f: common_vendor.p({
          name: "file-text",
          color: "#4A90E2",
          size: "32"
        }),
        g: common_vendor.t(examInfo.value.totalScore),
        h: common_vendor.p({
          name: "checkmark-circle",
          color: "#4A90E2",
          size: "32"
        }),
        i: common_vendor.t(examInfo.value.passScore)
      } : {}, {
        j: common_vendor.p({
          name: "warning",
          color: "#FF9500",
          size: "32"
        }),
        k: common_vendor.p({
          name: "exclamation-circle",
          color: "#f56c6c",
          size: "24"
        }),
        l: common_vendor.p({
          name: "exclamation-circle",
          color: "#f56c6c",
          size: "24"
        }),
        m: common_vendor.p({
          name: "exclamation-circle",
          color: "#f56c6c",
          size: "24"
        }),
        n: common_vendor.p({
          name: "shield",
          color: "#4A90E2",
          size: "32"
        }),
        o: common_vendor.p({
          name: "camera",
          color: "#4A90E2",
          size: "40"
        }),
        p: common_vendor.p({
          name: "account",
          color: "#4A90E2",
          size: "40"
        }),
        q: common_vendor.p({
          name: "list",
          color: "#4A90E2",
          size: "32"
        }),
        r: common_vendor.p({
          name: "settings",
          color: "#4A90E2",
          size: "32"
        }),
        s: common_vendor.p({
          name: "wifi",
          color: "#4CAF50",
          size: "32"
        }),
        t: common_vendor.t(networkStatus.value ? "正常" : "异常"),
        v: networkStatus.value ? 1 : "",
        w: common_vendor.p({
          name: "battery-charging",
          color: "#4CAF50",
          size: "32"
        }),
        x: common_vendor.t(batteryLevel.value),
        y: batteryLevel.value >= 80 ? 1 : "",
        z: common_vendor.p({
          name: "camera",
          color: "#4CAF50",
          size: "32"
        }),
        A: common_vendor.t(cameraPermission.value ? "已授权" : "未授权"),
        B: cameraPermission.value ? 1 : "",
        C: common_vendor.o(($event) => hasRead.value = $event),
        D: common_vendor.p({
          customStyle: {
            marginRight: "16rpx"
          },
          activeColor: "#4A90E2",
          modelValue: hasRead.value
        }),
        E: common_vendor.o(startExam),
        F: common_vendor.p({
          type: "primary",
          disabled: !hasRead.value || !canStartExam.value,
          loading: isStarting.value,
          loadingText: "准备中..."
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ea8408ec"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpages/exam/online/reading.js.map
