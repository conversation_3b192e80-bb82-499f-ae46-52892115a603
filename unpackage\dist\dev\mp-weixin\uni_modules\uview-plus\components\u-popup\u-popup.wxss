/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-05c24e9b,
.u-empty__wrap.data-v-05c24e9b,
.u-tabs.data-v-05c24e9b,
.u-tabs__wrapper.data-v-05c24e9b,
.u-tabs__wrapper__scroll-view-wrapper.data-v-05c24e9b,
.u-tabs__wrapper__scroll-view.data-v-05c24e9b,
.u-tabs__wrapper__nav.data-v-05c24e9b,
.u-tabs__wrapper__nav__line.data-v-05c24e9b,
.up-empty.data-v-05c24e9b,
.up-empty__wrap.data-v-05c24e9b,
.up-tabs.data-v-05c24e9b,
.up-tabs__wrapper.data-v-05c24e9b,
.up-tabs__wrapper__scroll-view-wrapper.data-v-05c24e9b,
.up-tabs__wrapper__scroll-view.data-v-05c24e9b,
.up-tabs__wrapper__nav.data-v-05c24e9b,
.up-tabs__wrapper__nav__line.data-v-05c24e9b {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-popup.data-v-05c24e9b {
  flex: 1;
}
.u-popup__trigger.data-v-05c24e9b {
  position: relative;
}
.u-popup__trigger__cover.data-v-05c24e9b {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
.u-popup__content.data-v-05c24e9b {
  background-color: #fff;
  position: relative;
}
.u-popup__content--round-top.data-v-05c24e9b {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.u-popup__content--round-left.data-v-05c24e9b {
  border-top-left-radius: 0;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 10px;
}
.u-popup__content--round-right.data-v-05c24e9b {
  border-top-left-radius: 10px;
  border-top-right-radius: 0;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 0;
}
.u-popup__content--round-bottom.data-v-05c24e9b {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}
.u-popup__content--round-center.data-v-05c24e9b {
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}
.u-popup__content__close.data-v-05c24e9b {
  position: absolute;
}
.u-popup__content__close--hover.data-v-05c24e9b {
  opacity: 0.4;
}
.u-popup__content__close--top-left.data-v-05c24e9b {
  top: 15px;
  left: 15px;
}
.u-popup__content__close--top-right.data-v-05c24e9b {
  top: 15px;
  right: 15px;
}
.u-popup__content__close--bottom-left.data-v-05c24e9b {
  bottom: 15px;
  left: 15px;
}
.u-popup__content__close--bottom-right.data-v-05c24e9b {
  right: 15px;
  bottom: 15px;
}