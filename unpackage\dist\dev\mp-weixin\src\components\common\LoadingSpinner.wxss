/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.loading-spinner.data-v-edb94d20 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}
.loading-overlay.data-v-edb94d20 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 9999;
}
.spinner-container.data-v-edb94d20 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.spinner.data-v-edb94d20 {
  position: relative;
  animation: spin-edb94d20 1.2s linear infinite;
}
.spinner-small.data-v-edb94d20 {
  width: 40rpx;
  height: 40rpx;
}
.spinner-medium.data-v-edb94d20 {
  width: 60rpx;
  height: 60rpx;
}
.spinner-large.data-v-edb94d20 {
  width: 80rpx;
  height: 80rpx;
}
.spinner-dot.data-v-edb94d20 {
  position: absolute;
  top: 0;
  left: 50%;
  transform-origin: 0 50%;
  animation: fade-edb94d20 1.2s linear infinite;
}
.spinner-small .spinner-dot.data-v-edb94d20 {
  width: 6rpx;
  height: 20rpx;
  margin-left: -3rpx;
}
.spinner-medium .spinner-dot.data-v-edb94d20 {
  width: 8rpx;
  height: 30rpx;
  margin-left: -4rpx;
}
.spinner-large .spinner-dot.data-v-edb94d20 {
  width: 10rpx;
  height: 40rpx;
  margin-left: -5rpx;
}
.dot.data-v-edb94d20 {
  width: 100%;
  height: 25%;
  background-color: #2E8B57;
  border-radius: 50rpx;
}
.spinner-dot.data-v-edb94d20:nth-child(1) {
  animation-delay: 0s;
}
.spinner-dot.data-v-edb94d20:nth-child(2) {
  animation-delay: -0.1s;
}
.spinner-dot.data-v-edb94d20:nth-child(3) {
  animation-delay: -0.2s;
}
.spinner-dot.data-v-edb94d20:nth-child(4) {
  animation-delay: -0.3s;
}
.spinner-dot.data-v-edb94d20:nth-child(5) {
  animation-delay: -0.4s;
}
.spinner-dot.data-v-edb94d20:nth-child(6) {
  animation-delay: -0.5s;
}
.spinner-dot.data-v-edb94d20:nth-child(7) {
  animation-delay: -0.6s;
}
.spinner-dot.data-v-edb94d20:nth-child(8) {
  animation-delay: -0.7s;
}
.spinner-dot.data-v-edb94d20:nth-child(9) {
  animation-delay: -0.8s;
}
.spinner-dot.data-v-edb94d20:nth-child(10) {
  animation-delay: -0.9s;
}
.spinner-dot.data-v-edb94d20:nth-child(11) {
  animation-delay: -1s;
}
.spinner-dot.data-v-edb94d20:nth-child(12) {
  animation-delay: -1.1s;
}
.loading-text.data-v-edb94d20 {
  margin-top: 24rpx;
  font-size: 28rpx;
  color: #8A8A8A;
}
@keyframes spin-edb94d20 {
0% {
    transform: rotate(0deg);
}
100% {
    transform: rotate(360deg);
}
}
@keyframes fade-edb94d20 {
0%, 39%, 100% {
    opacity: 0;
}
40% {
    opacity: 1;
}
}