{"version": 3, "file": "avatar.js", "sources": ["uni_modules/uview-plus/components/u-avatar/avatar.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 16:49:22\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/avatar.js\n */\nexport default {\n    // avatar 组件\n    avatar: {\n        src: '',\n        shape: 'circle',\n        size: 40,\n        mode: 'scaleToFill',\n        text: '',\n        bgColor: '#c0c4cc',\n        color: '#ffffff',\n        fontSize: 18,\n        icon: '',\n        mpAvatar: false,\n        randomBgColor: false,\n        defaultUrl: '',\n        colorIndex: '',\n        name: ''\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,KAAK;AAAA,IACL,OAAO;AAAA,IACP,MAAM;AAAA,IACN,MAAM;AAAA,IACN,MAAM;AAAA,IACN,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,MAAM;AAAA,IACN,UAAU;AAAA,IACV,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,MAAM;AAAA,EACT;AACL;;"}