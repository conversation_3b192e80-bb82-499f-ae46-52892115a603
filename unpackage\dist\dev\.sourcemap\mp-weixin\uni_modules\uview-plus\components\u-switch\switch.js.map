{"version": 3, "file": "switch.js", "sources": ["uni_modules/uview-plus/components/u-switch/switch.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:22:24\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/switch.js\n */\nexport default {\n    // switch\n    switch: {\n        loading: false,\n        disabled: false,\n        size: 25,\n        activeColor: '#2979ff',\n        inactiveColor: '#ffffff',\n        value: false,\n        activeValue: true,\n        inactiveValue: false,\n        asyncChange: false,\n        space: 0\n    }\n}\n"], "names": [], "mappings": ";AASA,MAAe,SAAA;AAAA;AAAA,EAEX,QAAQ;AAAA,IACJ,SAAS;AAAA,IACT,UAAU;AAAA,IACV,MAAM;AAAA,IACN,aAAa;AAAA,IACb,eAAe;AAAA,IACf,OAAO;AAAA,IACP,aAAa;AAAA,IACb,eAAe;AAAA,IACf,aAAa;AAAA,IACb,OAAO;AAAA,EACV;AACL;;"}