"use strict";
const common_vendor = require("../../../common/vendor.js");
const common_assets = require("../../../common/assets.js");
const src_stores_app = require("../../../src/stores/app.js");
if (!Array) {
  const _easycom_u_navbar2 = common_vendor.resolveComponent("u-navbar");
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_navbar2 + _easycom_u_icon2 + _easycom_u_button2)();
}
const _easycom_u_navbar = () => "../../../uni_modules/uview-plus/components/u-navbar/u-navbar.js";
const _easycom_u_icon = () => "../../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_navbar + _easycom_u_icon + _easycom_u_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "about",
  setup(__props) {
    const appStore = src_stores_app.useAppStore();
    const appVersion = common_vendor.ref("1.0.0");
    const updateTime = common_vendor.ref("2024-12-01");
    const makePhoneCall = () => {
      common_vendor.index.makePhoneCall({
        phoneNumber: "************",
        fail: (error) => {
          common_vendor.index.__f__("error", "at subpages/personal/about/about.vue:212", "拨打电话失败:", error);
          appStore.showToast("拨打电话失败");
        }
      });
    };
    const copyEmail = () => {
      common_vendor.index.setClipboardData({
        data: "<EMAIL>",
        success: () => {
          appStore.showToast("邮箱地址已复制", "success");
        },
        fail: (error) => {
          common_vendor.index.__f__("error", "at subpages/personal/about/about.vue:226", "复制失败:", error);
          appStore.showToast("复制失败");
        }
      });
    };
    const viewPrivacyPolicy = () => {
      appStore.showModal({
        title: "隐私政策",
        content: "我们非常重视您的隐私保护。本应用仅收集必要的用户信息用于提供服务，不会泄露给第三方。",
        showCancelButton: false,
        confirmText: "我知道了"
      });
    };
    const viewUserAgreement = () => {
      appStore.showModal({
        title: "用户协议",
        content: "使用本应用即表示您同意遵守相关使用条款。请合理使用系统功能，不得进行违法违规操作。",
        showCancelButton: false,
        confirmText: "我知道了"
      });
    };
    const checkUpdate = () => {
      appStore.showLoading("检查中...");
      setTimeout(() => {
        appStore.hideLoading();
        appStore.showToast("当前已是最新版本", "success");
      }, 2e3);
    };
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          title: "关于我们",
          autoBack: true,
          background: {
            background: "linear-gradient(135deg, #2E8B57 0%, #228B22 100%)"
          },
          titleStyle: "color: #fff; font-weight: bold;"
        }),
        b: common_assets._imports_0,
        c: common_vendor.t(appVersion.value),
        d: common_vendor.p({
          name: "star",
          color: "#2E8B57",
          size: "32"
        }),
        e: common_vendor.p({
          name: "book",
          color: "#4A90E2",
          size: "40"
        }),
        f: common_vendor.p({
          name: "edit-pen",
          color: "#4CAF50",
          size: "40"
        }),
        g: common_vendor.p({
          name: "medal",
          color: "#FFD700",
          size: "40"
        }),
        h: common_vendor.p({
          name: "chart",
          color: "#FF9500",
          size: "40"
        }),
        i: common_vendor.p({
          name: "headphones",
          color: "#2E8B57",
          size: "32"
        }),
        j: common_vendor.p({
          name: "phone",
          color: "#4CAF50",
          size: "32"
        }),
        k: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "24"
        }),
        l: common_vendor.o(makePhoneCall),
        m: common_vendor.p({
          name: "email",
          color: "#4A90E2",
          size: "32"
        }),
        n: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "24"
        }),
        o: common_vendor.o(copyEmail),
        p: common_vendor.p({
          name: "clock",
          color: "#FF9500",
          size: "32"
        }),
        q: common_vendor.p({
          name: "account-circle",
          color: "#2E8B57",
          size: "32"
        }),
        r: common_vendor.p({
          name: "shield",
          color: "#2E8B57",
          size: "32"
        }),
        s: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "24"
        }),
        t: common_vendor.o(viewPrivacyPolicy),
        v: common_vendor.p({
          name: "arrow-right",
          color: "#c0c4cc",
          size: "24"
        }),
        w: common_vendor.o(viewUserAgreement),
        x: common_vendor.p({
          name: "refresh",
          color: "#2E8B57",
          size: "32"
        }),
        y: common_vendor.t(appVersion.value),
        z: common_vendor.t(updateTime.value),
        A: common_vendor.o(checkUpdate),
        B: common_vendor.p({
          type: "primary",
          plain: true,
          size: "medium"
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d86fbda3"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/subpages/personal/about/about.js.map
