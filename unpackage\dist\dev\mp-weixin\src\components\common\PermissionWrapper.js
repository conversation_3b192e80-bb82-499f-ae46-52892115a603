"use strict";
const common_vendor = require("../../../common/vendor.js");
const src_stores_user = require("../../stores/user.js");
const src_stores_app = require("../../stores/app.js");
const src_utils_permission = require("../../utils/permission.js");
const src_constants_index = require("../../constants/index.js");
if (!Array) {
  const _easycom_u_icon2 = common_vendor.resolveComponent("u-icon");
  const _easycom_u_button2 = common_vendor.resolveComponent("u-button");
  (_easycom_u_icon2 + _easycom_u_button2)();
}
const _easycom_u_icon = () => "../../../uni_modules/uview-plus/components/u-icon/u-icon.js";
const _easycom_u_button = () => "../../../uni_modules/uview-plus/components/u-button/u-button.js";
if (!Math) {
  (_easycom_u_icon + _easycom_u_button)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "PermissionWrapper",
  props: {
    permission: { default: "auth" },
    feature: { default: "" },
    showFallback: { type: Boolean, default: true },
    fallbackTitle: { default: "" },
    fallbackMessage: { default: "" },
    fallbackIcon: { default: "lock" },
    fallbackIconColor: { default: "#BFBFBF" },
    showAction: { type: Boolean, default: true },
    actionText: { default: "" },
    actionType: { default: "login" }
  },
  emits: ["action"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const userStore = src_stores_user.useUserStore();
    const appStore = src_stores_app.useAppStore();
    const hasPermission = common_vendor.computed(() => {
      switch (props.permission) {
        case "auth":
          return src_utils_permission.permissionManager.isLoggedIn();
        case "authenticated":
          return src_utils_permission.permissionManager.isAuthenticated();
        case "feature":
          if (!props.feature)
            return true;
          return src_utils_permission.permissionManager.checkFeatureAccess(props.feature).allowed;
        default:
          return true;
      }
    });
    const fallbackTitle = common_vendor.computed(() => {
      if (props.fallbackTitle)
        return props.fallbackTitle;
      switch (props.permission) {
        case "auth":
          return "需要登录";
        case "authenticated":
          return "需要认证";
        case "feature":
          return "功能受限";
        default:
          return "权限不足";
      }
    });
    const fallbackMessage = common_vendor.computed(() => {
      if (props.fallbackMessage)
        return props.fallbackMessage;
      switch (props.permission) {
        case "auth":
          return "请先登录后使用此功能";
        case "authenticated":
          return src_utils_permission.permissionManager.getUserStatusMessage();
        case "feature":
          if (props.feature) {
            const result = src_utils_permission.permissionManager.checkFeatureAccess(props.feature);
            return result.message || "您暂时无法使用此功能";
          }
          return "您暂时无法使用此功能";
        default:
          return "您暂时无法使用此功能";
      }
    });
    const actionText = common_vendor.computed(() => {
      if (props.actionText)
        return props.actionText;
      switch (props.actionType) {
        case "login":
          return "立即登录";
        case "profile":
          return "完善资料";
        default:
          return "了解详情";
      }
    });
    const handleAction = () => {
      var _a;
      switch (props.actionType) {
        case "login":
          appStore.redirectTo(src_constants_index.PAGE_PATHS.LOGIN);
          break;
        case "profile":
          if (((_a = userStore.userInfo) == null ? void 0 : _a.status) === "not_submitted") {
            appStore.redirectTo(src_constants_index.PAGE_PATHS.PROFILE);
          } else {
            appStore.switchTab(src_constants_index.PAGE_PATHS.PERSONAL);
          }
          break;
        case "custom":
          emit("action");
          break;
        default:
          emit("action");
      }
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: hasPermission.value
      }, hasPermission.value ? {} : _ctx.showFallback ? common_vendor.e({
        c: common_vendor.p({
          name: _ctx.fallbackIcon,
          color: _ctx.fallbackIconColor,
          size: "80"
        }),
        d: common_vendor.t(fallbackTitle.value),
        e: common_vendor.t(fallbackMessage.value),
        f: _ctx.showAction
      }, _ctx.showAction ? {
        g: common_vendor.t(actionText.value),
        h: common_vendor.o(handleAction),
        i: common_vendor.p({
          type: "primary",
          size: "normal"
        })
      } : {}) : {}, {
        b: _ctx.showFallback
      });
    };
  }
});
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-ba7e4f2b"]]);
wx.createComponent(Component);
//# sourceMappingURL=../../../../.sourcemap/mp-weixin/src/components/common/PermissionWrapper.js.map
