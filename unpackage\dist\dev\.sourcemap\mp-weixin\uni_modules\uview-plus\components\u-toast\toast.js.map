{"version": 3, "file": "toast.js", "sources": ["uni_modules/uview-plus/components/u-toast/toast.js"], "sourcesContent": ["/*\n * <AUTHOR> LQ\n * @Description  :\n * @version      : 1.0\n * @Date         : 2021-08-20 16:44:21\n * @LastAuthor   : LQ\n * @lastTime     : 2021-08-20 17:07:07\n * @FilePath     : /u-view2.0/uview-ui/libs/config/props/toast.js\n */\nexport default {\n    // toast组件\n    toast: {\n        zIndex: 10090,\n        loading: false,\n        message: '',\n        icon: '',\n        type: '',\n        loadingMode: '',\n        show: '',\n        overlay: false,\n        position: 'center',\n        params: {},\n        duration: 2000,\n        isTab: false,\n        url: '',\n        callback: null,\n        back: false\n    }\n\n}\n"], "names": [], "mappings": ";AASA,MAAe,QAAA;AAAA;AAAA,EAEX,OAAO;AAAA,IACH,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,SAAS;AAAA,IACT,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,IACb,MAAM;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,IACV,QAAQ,CAAE;AAAA,IACV,UAAU;AAAA,IACV,OAAO;AAAA,IACP,KAAK;AAAA,IACL,UAAU;AAAA,IACV,MAAM;AAAA,EACT;AAEL;;"}