<view class="exam-answer-container data-v-466c6991"><u-navbar wx:if="{{a}}" class="data-v-466c6991" u-i="466c6991-0" bind:__l="__l" u-p="{{a}}"/><view class="exam-status-bar data-v-466c6991"><view class="status-info data-v-466c6991"><text class="question-progress data-v-466c6991">{{b}}/{{c}}</text><text class="{{['time-remaining', 'data-v-466c6991', e && 'warning']}}">{{d}}</text></view><view class="progress-bar data-v-466c6991"><view class="progress-fill data-v-466c6991" style="{{'width:' + f}}"></view></view></view><view wx:if="{{g}}" class="question-container data-v-466c6991"><scroll-view class="question-scroll data-v-466c6991" scroll-y><view class="question-header data-v-466c6991"><view class="question-info data-v-466c6991"><status-tag wx:if="{{h}}" class="data-v-466c6991" u-i="466c6991-1" bind:__l="__l" u-p="{{h}}"/><text class="question-score data-v-466c6991">{{i}}分</text></view></view><view class="question-content data-v-466c6991"><text class="question-title data-v-466c6991">{{j}}</text></view><view wx:if="{{k}}" class="question-options data-v-466c6991"><view wx:for="{{l}}" wx:for-item="option" wx:key="f" class="{{['option-item', 'data-v-466c6991', option.g]}}" bindtap="{{option.h}}"><view class="option-indicator data-v-466c6991"><text class="option-label data-v-466c6991">{{option.a}}</text><u-icon wx:if="{{option.b}}" class="data-v-466c6991" u-i="{{option.c}}" bind:__l="__l" u-p="{{option.d}}"/></view><text class="option-text data-v-466c6991">{{option.e}}</text></view></view><view wx:elif="{{m}}" class="judge-options data-v-466c6991"><view class="{{['judge-option', 'data-v-466c6991', o && 'active']}}" bindtap="{{p}}"><u-icon wx:if="{{n}}" class="data-v-466c6991" u-i="466c6991-3" bind:__l="__l" u-p="{{n}}"/><text class="data-v-466c6991">正确</text></view><view class="{{['judge-option', 'data-v-466c6991', r && 'active']}}" bindtap="{{s}}"><u-icon wx:if="{{q}}" class="data-v-466c6991" u-i="466c6991-4" bind:__l="__l" u-p="{{q}}"/><text class="data-v-466c6991">错误</text></view></view><view wx:elif="{{t}}" class="essay-input data-v-466c6991"><u-textarea wx:if="{{w}}" class="data-v-466c6991" u-i="466c6991-5" bind:__l="__l" bindupdateModelValue="{{v}}" u-p="{{w}}"/></view></scroll-view></view><view class="action-buttons data-v-466c6991"><u-button wx:if="{{x}}" u-s="{{['d']}}" class="prev-btn data-v-466c6991" bindclick="{{y}}" u-i="466c6991-6" bind:__l="__l" u-p="{{z}}"> 上一题 </u-button><u-button wx:if="{{C}}" u-s="{{['d']}}" class="next-btn data-v-466c6991" bindclick="{{B}}" u-i="466c6991-7" bind:__l="__l" u-p="{{C}}">{{A}}</u-button></view><view class="answer-sheet-fab data-v-466c6991" bindtap="{{E}}"><u-icon wx:if="{{D}}" class="data-v-466c6991" u-i="466c6991-8" bind:__l="__l" u-p="{{D}}"/></view><u-popup wx:if="{{M}}" class="data-v-466c6991" u-s="{{['d']}}" u-i="466c6991-9" bind:__l="__l" bindupdateModelValue="{{L}}" u-p="{{M}}"><view class="answer-sheet-modal data-v-466c6991"><view class="modal-header data-v-466c6991"><text class="modal-title data-v-466c6991">答题卡</text><view class="modal-actions data-v-466c6991"><text class="time-info data-v-466c6991">剩余时间：{{F}}</text><u-icon wx:if="{{H}}" class="data-v-466c6991" bindclick="{{G}}" u-i="466c6991-10,466c6991-9" bind:__l="__l" u-p="{{H}}"/></view></view><scroll-view class="answer-grid-container data-v-466c6991" scroll-y><view class="answer-grid data-v-466c6991"><view wx:for="{{I}}" wx:for-item="question" wx:key="b" class="{{['answer-item', 'data-v-466c6991', question.c]}}" bindtap="{{question.d}}"><text class="data-v-466c6991">{{question.a}}</text></view></view></scroll-view><view class="modal-footer data-v-466c6991"><view class="answer-legend data-v-466c6991"><view class="legend-item data-v-466c6991"><view class="legend-color answered data-v-466c6991"></view><text class="data-v-466c6991">已答</text></view><view class="legend-item data-v-466c6991"><view class="legend-color current data-v-466c6991"></view><text class="data-v-466c6991">当前</text></view><view class="legend-item data-v-466c6991"><view class="legend-color unanswered data-v-466c6991"></view><text class="data-v-466c6991">未答</text></view></view><u-button wx:if="{{K}}" u-s="{{['d']}}" class="submit-exam-btn data-v-466c6991" bindclick="{{J}}" u-i="466c6991-11,466c6991-9" bind:__l="__l" u-p="{{K}}"> 提交考试 </u-button></view></view></u-popup><u-modal wx:if="{{Q}}" class="data-v-466c6991" bindconfirm="{{N}}" bindcancel="{{O}}" u-i="466c6991-12" bind:__l="__l" bindupdateModelValue="{{P}}" u-p="{{Q}}"/><u-modal wx:if="{{T}}" class="data-v-466c6991" bindconfirm="{{R}}" u-i="466c6991-13" bind:__l="__l" bindupdateModelValue="{{S}}" u-p="{{T}}"/></view>