/**
 * 疾控医护考试系统 - 样式变量配置
 * 基于uni-app内置样式变量，结合uview-plus UI库
 */
/* 主题色彩变量 */
/* 行为相关颜色 - 疾控主题 */
/* 主题色彩扩展 */
/* 文字基本颜色 */
/* 文字颜色扩展 */
/* 背景颜色 */
/* 背景颜色扩展 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.u-empty.data-v-2af81691,
.u-empty__wrap.data-v-2af81691,
.u-tabs.data-v-2af81691,
.u-tabs__wrapper.data-v-2af81691,
.u-tabs__wrapper__scroll-view-wrapper.data-v-2af81691,
.u-tabs__wrapper__scroll-view.data-v-2af81691,
.u-tabs__wrapper__nav.data-v-2af81691,
.u-tabs__wrapper__nav__line.data-v-2af81691,
.up-empty.data-v-2af81691,
.up-empty__wrap.data-v-2af81691,
.up-tabs.data-v-2af81691,
.up-tabs__wrapper.data-v-2af81691,
.up-tabs__wrapper__scroll-view-wrapper.data-v-2af81691,
.up-tabs__wrapper__scroll-view.data-v-2af81691,
.up-tabs__wrapper__nav.data-v-2af81691,
.up-tabs__wrapper__nav__line.data-v-2af81691 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-loading-icon.data-v-2af81691 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  color: #c8c9cc;
}
.u-loading-icon__text.data-v-2af81691 {
  margin-left: 4px;
  color: #606266;
  font-size: 14px;
  line-height: 20px;
}
.u-loading-icon__spinner.data-v-2af81691 {
  width: 30px;
  height: 30px;
  position: relative;
  box-sizing: border-box;
  max-width: 100%;
  max-height: 100%;
  animation: u-rotate-2af81691 1s linear infinite;
}
.u-loading-icon__spinner--semicircle.data-v-2af81691 {
  border-width: 2px;
  border-color: transparent;
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  border-style: solid;
}
.u-loading-icon__spinner--circle.data-v-2af81691 {
  border-top-right-radius: 100px;
  border-top-left-radius: 100px;
  border-bottom-left-radius: 100px;
  border-bottom-right-radius: 100px;
  border-width: 2px;
  border-top-color: #e5e5e5;
  border-right-color: #e5e5e5;
  border-bottom-color: #e5e5e5;
  border-left-color: #e5e5e5;
  border-style: solid;
}
.u-loading-icon--vertical.data-v-2af81691 {
  flex-direction: column;
}
.data-v-2af81691:host {
  font-size: 0px;
  line-height: 1;
}
.u-loading-icon__spinner--spinner.data-v-2af81691 {
  animation-timing-function: steps(12);
}
.u-loading-icon__text.data-v-2af81691:empty {
  display: none;
}
.u-loading-icon--vertical .u-loading-icon__text.data-v-2af81691 {
  margin: 6px 0 0;
  color: #606266;
}
.u-loading-icon__dot.data-v-2af81691 {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.u-loading-icon__dot.data-v-2af81691:before {
  display: block;
  width: 2px;
  height: 25%;
  margin: 0 auto;
  background-color: currentColor;
  border-radius: 40%;
  content: " ";
}
.u-loading-icon__dot.data-v-2af81691:nth-of-type(1) {
  transform: rotate(30deg);
  opacity: 1;
}
.u-loading-icon__dot.data-v-2af81691:nth-of-type(2) {
  transform: rotate(60deg);
  opacity: 0.9375;
}
.u-loading-icon__dot.data-v-2af81691:nth-of-type(3) {
  transform: rotate(90deg);
  opacity: 0.875;
}
.u-loading-icon__dot.data-v-2af81691:nth-of-type(4) {
  transform: rotate(120deg);
  opacity: 0.8125;
}
.u-loading-icon__dot.data-v-2af81691:nth-of-type(5) {
  transform: rotate(150deg);
  opacity: 0.75;
}
.u-loading-icon__dot.data-v-2af81691:nth-of-type(6) {
  transform: rotate(180deg);
  opacity: 0.6875;
}
.u-loading-icon__dot.data-v-2af81691:nth-of-type(7) {
  transform: rotate(210deg);
  opacity: 0.625;
}
.u-loading-icon__dot.data-v-2af81691:nth-of-type(8) {
  transform: rotate(240deg);
  opacity: 0.5625;
}
.u-loading-icon__dot.data-v-2af81691:nth-of-type(9) {
  transform: rotate(270deg);
  opacity: 0.5;
}
.u-loading-icon__dot.data-v-2af81691:nth-of-type(10) {
  transform: rotate(300deg);
  opacity: 0.4375;
}
.u-loading-icon__dot.data-v-2af81691:nth-of-type(11) {
  transform: rotate(330deg);
  opacity: 0.375;
}
.u-loading-icon__dot.data-v-2af81691:nth-of-type(12) {
  transform: rotate(360deg);
  opacity: 0.3125;
}
@keyframes u-rotate-2af81691 {
0% {
    transform: rotate(0deg);
}
to {
    transform: rotate(1turn);
}
}