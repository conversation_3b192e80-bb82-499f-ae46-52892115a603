<view class="login-container data-v-e4e4508d"><view class="bg-decoration data-v-e4e4508d"><view class="circle circle-1 data-v-e4e4508d"></view><view class="circle circle-2 data-v-e4e4508d"></view><view class="circle circle-3 data-v-e4e4508d"></view></view><view class="main-content data-v-e4e4508d"><view class="header data-v-e4e4508d"><image class="logo data-v-e4e4508d" src="{{a}}" mode="aspectFill"/><text class="app-name data-v-e4e4508d">疾控医护考试系统</text><text class="app-desc data-v-e4e4508d">医护人员任职资格考试平台</text></view><view class="login-form data-v-e4e4508d"><view class="agreement-section data-v-e4e4508d"><u-checkbox wx:if="{{c}}" class="data-v-e4e4508d" u-i="e4e4508d-0" bind:__l="__l" bindupdateModelValue="{{b}}" u-p="{{c}}"/><text class="agreement-text data-v-e4e4508d"> 我已阅读并同意 <text class="link-text data-v-e4e4508d" bindtap="{{d}}">《用户服务协议》</text> 和 <text class="link-text data-v-e4e4508d" bindtap="{{e}}">《隐私政策》</text></text></view><u-button wx:if="{{h}}" u-s="{{['d']}}" class="login-btn data-v-e4e4508d" bindclick="{{g}}" u-i="e4e4508d-1" bind:__l="__l" u-p="{{h}}"><u-icon wx:if="{{f}}" class="wechat-icon data-v-e4e4508d" u-i="e4e4508d-2,e4e4508d-1" bind:__l="__l" u-p="{{f}}"/> 微信授权登录 </u-button><view class="tips data-v-e4e4508d"><u-icon wx:if="{{i}}" class="data-v-e4e4508d" u-i="e4e4508d-3" bind:__l="__l" u-p="{{i}}"/><text class="tips-text data-v-e4e4508d">首次登录需完善个人资料，通过机构审核后可使用完整功能</text></view></view></view><view class="footer data-v-e4e4508d"><text class="copyright data-v-e4e4508d">© 2024 疾控医护考试系统</text></view><u-popup wx:if="{{q}}" class="data-v-e4e4508d" u-s="{{['d']}}" u-i="e4e4508d-4" bind:__l="__l" bindupdateModelValue="{{p}}" u-p="{{q}}"><view class="agreement-modal data-v-e4e4508d"><view class="modal-header data-v-e4e4508d"><text class="modal-title data-v-e4e4508d">{{j}}</text><u-icon wx:if="{{l}}" class="data-v-e4e4508d" bindclick="{{k}}" u-i="e4e4508d-5,e4e4508d-4" bind:__l="__l" u-p="{{l}}"/></view><scroll-view class="modal-content data-v-e4e4508d" scroll-y><text class="agreement-content data-v-e4e4508d">{{m}}</text></scroll-view><view class="modal-footer data-v-e4e4508d"><u-button wx:if="{{o}}" class="data-v-e4e4508d" u-s="{{['d']}}" bindclick="{{n}}" u-i="e4e4508d-6,e4e4508d-4" bind:__l="__l" u-p="{{o}}">我知道了</u-button></view></view></u-popup></view>