{"version": 3, "file": "props.js", "sources": ["uni_modules/uview-plus/components/u-status-bar/props.js"], "sourcesContent": ["import { defineMixin } from '../../libs/vue'\nimport defProps from '../../libs/config/props.js'\nexport const props = defineMixin({\n    props: {\n        bgColor: {\n            type: String,\n            default: () => defProps.statusBar.bgColor\n        },\n\t\t// 状态栏获取得高度\n\t\theight: {\n\t\t\ttype: Number,\n\t\t\tdefault: () => defProps.statusBar.height\n\t\t}\n    }\n})\n"], "names": ["defineMixin", "defProps"], "mappings": ";;;AAEY,MAAC,QAAQA,+BAAAA,YAAY;AAAA,EAC7B,OAAO;AAAA,IACH,SAAS;AAAA,MACL,MAAM;AAAA,MACN,SAAS,MAAMC,8CAAS,UAAU;AAAA,IACrC;AAAA;AAAA,IAEP,QAAQ;AAAA,MACP,MAAM;AAAA,MACN,SAAS,MAAMA,8CAAS,UAAU;AAAA,IAClC;AAAA,EACE;AACL,CAAC;;"}